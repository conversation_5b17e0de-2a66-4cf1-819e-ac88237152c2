0 verbose cli C:\Driver-D\tools\nodejs\node.exe C:\Driver-D\tools\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.15.0
3 silly config load:file:C:\Driver-D\tools\nodejs\node_modules\npm\npmrc
4 silly config load:file:C:\Driver-E\工作\代码文件\others\wvp-GB28181-pro\web\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Driver-E\工作\代码文件\nodejs\node_global\etc\npmrc
7 verbose title npm run build:prod
8 verbose argv "run" "build:prod"
9 verbose logfile logs-max:10 dir:C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T14_37_38_580Z-
10 verbose logfile C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T14_37_38_580Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 verbose cwd C:\Driver-E\工作\代码文件\others\wvp-GB28181-pro\web
14 verbose os Windows_NT 10.0.26100
15 verbose node v22.15.0
16 verbose npm  v10.9.2
17 verbose exit 0
18 info ok
