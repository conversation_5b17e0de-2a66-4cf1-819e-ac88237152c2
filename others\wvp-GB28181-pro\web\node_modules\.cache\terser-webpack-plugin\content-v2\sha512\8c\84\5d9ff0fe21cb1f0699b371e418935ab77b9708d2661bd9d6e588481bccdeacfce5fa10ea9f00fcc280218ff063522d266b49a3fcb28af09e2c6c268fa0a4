{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-551e0bbb\"],{\"4f91\":function(e,i,t){\"use strict\";var n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t(\"div\",{ref:\"container\",staticStyle:{\"background-color\":\"#000000\"},attrs:{id:\"h265Player\"},on:{dblclick:e.fullscreenSwich}},[t(\"div\",{ref:\"playerBox\",staticStyle:{width:\"100%\",height:\"100%\",margin:\"0 auto\"},attrs:{id:\"glplayer\"}}),e.playerLoading?t(\"div\",{staticClass:\"player-loading\"},[t(\"i\",{staticClass:\"el-icon-loading\"}),t(\"span\",[e._v(\"视频加载中\")])]):e._e(),e.showButton?t(\"div\",{staticClass:\"buttons-box\",attrs:{id:\"buttonsBox\"}},[t(\"div\",{staticClass:\"buttons-box-left\"},[e.playing?e._e():t(\"i\",{staticClass:\"iconfont icon-play h265web-btn\",on:{click:e.unPause}}),e.playing?t(\"i\",{staticClass:\"iconfont icon-pause h265web-btn\",on:{click:e.pause}}):e._e(),t(\"i\",{staticClass:\"iconfont icon-stop h265web-btn\",on:{click:e.destroy}}),e.isNotMute?t(\"i\",{staticClass:\"iconfont icon-audio-high h265web-btn\",on:{click:function(i){return e.mute()}}}):e._e(),e.isNotMute?e._e():t(\"i\",{staticClass:\"iconfont icon-audio-mute h265web-btn\",on:{click:function(i){return e.cancelMute()}}})]),t(\"div\",{staticClass:\"buttons-box-right\"},[t(\"i\",{staticClass:\"iconfont icon-camera1196054easyiconnet h265web-btn\",staticStyle:{\"font-size\":\"1rem !important\"},on:{click:e.screenshot}}),t(\"i\",{staticClass:\"iconfont icon-shuaxin11 h265web-btn\",on:{click:e.playBtnClick}}),e.fullscreen?e._e():t(\"i\",{staticClass:\"iconfont icon-weibiaoti10 h265web-btn\",on:{click:e.fullscreenSwich}}),e.fullscreen?t(\"i\",{staticClass:\"iconfont icon-weibiaoti11 h265web-btn\",on:{click:e.fullscreenSwich}}):e._e()])]):e._e()])},s=[],o=(t(\"b0c0\"),t(\"ac1f\"),t(\"5319\"),{}),l=\"base64:********************************************************************************************************************************************************************************************************************************************************************************\",a={name:\"H265web\",props:[\"videoUrl\",\"error\",\"hasAudio\",\"height\",\"showButton\"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1,playerWidth:0,playerHeight:0,inited:!1,playerLoading:!1,mediaInfo:null,lastPlayTimeUpdate:0}},watch:{videoUrl:function(e,i){this.play(e)},playing:function(e,i){this.$emit(\"playStatusChange\",e)},immediate:!0},mounted:function(){var e=this,i=decodeURIComponent(this.$route.params.url);window.onresize=function(){e.updatePlayerDomSize()},this.btnDom=document.getElementById(\"buttonsBox\"),this.ensureGlobalInterceptorActive(),console.log(\"初始化时的地址为: \"+i),i&&this.play(this.videoUrl)},destroyed:function(){o[this._uid]&&o[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.playerLoading=!1},methods:{updatePlayerDomSize:function(){var e=this,i=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(i){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(i.parentNode));var t=i.parentNode.clientWidth,n=i.parentNode.clientHeight,s=t,l=9/16*s;n>0&&t>n/9*16&&(l=n,s=n/9*16);var a=Math.min(document.body.clientHeight,document.documentElement.clientHeight);l>a&&(l=a,s=16/9*l),this.$refs.playerBox.style.width=s+\"px\",this.$refs.playerBox.style.height=l+\"px\",this.playerWidth=s,this.playerHeight=l,this.playing&&o[this._uid].resize(this.playerWidth,this.playerHeight)},resize:function(e,i){this.playerWidth=e,this.playerHeight=i,this.$refs.playerBox.style.width=e+\"px\",this.$refs.playerBox.style.height=i+\"px\",this.playing&&o[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(e){var i=this;if(console.log(\"开始创建播放器实例，UID:\",this._uid,\"URL:\",e),o[this._uid]){console.warn(\"创建前发现残留的播放器实例，强制清理，UID:\",this._uid);try{o[this._uid].release&&o[this._uid].release()}catch(s){console.warn(\"清理残留播放器时出现错误:\",s)}o[this._uid]=null,this.clearPlayerDOM()}this.playerLoading=!0;var t={};console.log(\"正在创建新的h265web播放器实例，UID:\",this._uid),o[this._uid]=new window.new265webjs(e,Object.assign({player:\"glplayer\",width:this.playerWidth,height:this.playerHeight,token:l,extInfo:{coreProbePart:.4,probeSize:8192,ignoreAudio:null==this.hasAudio||this.hasAudio?0:1},debug:!1,debugLevel:0,logLevel:0,disableStats:!0,disableAnalytics:!0,noStats:!0,noLog:!0,silent:!0},t)),console.log(\"h265web播放器实例创建成功，UID:\",this._uid);var n=o[this._uid];n.onOpenFullScreen=function(){i.fullscreen=!0},n.onCloseFullScreen=function(){i.fullscreen=!1},n.onReadyShowDone=function(){console.log(\"播放器准备完成，开始自动播放，UID:\",i._uid);try{var e=n.play();console.log(\"自动播放结果:\",e,\"UID:\",i._uid),setTimeout((function(){i.playing=n.isPlaying(),console.log(\"播放器初始化完成，playing状态:\",i.playing,\"UID:\",i._uid),i.playerLoading=!1}),200)}catch(s){console.warn(\"播放器自动播放失败:\",s),i.playing=!1,i.playerLoading=!1}},n.onLoadFinish=function(){try{i.loaded=!0,n.mediaInfo&&\"function\"===typeof n.mediaInfo?i.mediaInfo=n.mediaInfo():console.warn(\"播放器不支持mediaInfo方法\")}catch(s){console.warn(\"获取媒体信息时出现错误:\",s),i.loaded=!0}},n.onPlayTime=function(e){try{null===e||void 0===e||isNaN(e)?console.warn(\"播放器返回无效的时间值:\",e):(i.playing||(console.log(\"收到播放时间回调，确认播放器正在播放，UID:\",i._uid),i.playing=!0),i.lastPlayTimeUpdate=Date.now(),i.$emit(\"playTimeChange\",e))}catch(s){console.warn(\"播放器时间回调出现错误:\",s)}},n.onSeekFinish=function(){try{console.log(\"播放器seek完成\"),i.$emit(\"seekFinish\")}catch(s){console.warn(\"播放器seek完成回调出现错误:\",s)}},n.do()},screenshot:function(){if(o[this._uid]){var e=document.createElement(\"canvas\");console.log(this.mediaInfo),e.width=this.mediaInfo.meta.size.width,e.height=this.mediaInfo.meta.size.height,o[this._uid].snapshot(e);var i=document.createElement(\"a\");i.download=\"screenshot.png\",i.href=e.toDataURL(\"image/png\").replace(\"image/png\",\"image/octet-stream\"),i.click()}},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var i=this;if(console.log(\"开始播放视频，URL:\",e,\"UID:\",this._uid),o[this._uid])return console.log(\"检测到已存在的播放器实例，先销毁，UID:\",this._uid),this.destroy(),void setTimeout((function(){console.log(\"销毁完成，重新创建播放器，UID:\",i._uid),i.play(e)}),300);if(e){if(0===this.playerWidth||0===this.playerHeight)return console.log(\"播放器尺寸未初始化，等待DOM更新\"),this.updatePlayerDomSize(),void setTimeout((function(){i.play(e)}),300);console.log(\"创建新的播放器实例，UID:\",this._uid),this.create(e)}else console.warn(\"播放URL为空，无法播放\")},unPause:function(){var e=this;try{if(o[this._uid]&&o[this._uid].play){console.log(\"开始调用播放器play方法，UID:\",this._uid);var i=o[this._uid].play();i&&\"function\"===typeof i.catch?i.then((function(){console.log(\"播放器play Promise resolved，UID:\",e._uid),setTimeout((function(){e.playing=o[e._uid].isPlaying(),console.log(\"播放器状态更新，playing:\",e.playing,\"UID:\",e._uid)}),100)})).catch((function(i){\"AbortError\"!==i.name?(console.warn(\"恢复播放时出现错误:\",i),e.playing=!1):console.log(\"播放被中断（AbortError），这是正常的\")})):setTimeout((function(){e.playing=o[e._uid].isPlaying(),console.log(\"播放器状态更新（非Promise），playing:\",e.playing,\"UID:\",e._uid)}),100),console.log(\"播放器play方法调用完成，UID:\",this._uid)}else console.warn(\"播放器实例不存在或不支持play方法，UID:\",this._uid)}catch(t){console.warn(\"恢复播放时出现错误:\",t),this.playing=!1}this.err=\"\"},pause:function(){try{if(o[this._uid]&&o[this._uid].pause){var e=o[this._uid].pause();e&&\"function\"===typeof e.catch&&e.catch((function(e){\"AbortError\"!==e.name&&console.warn(\"暂停播放时出现错误:\",e)})),this.playing=o[this._uid].isPlaying()}}catch(i){console.warn(\"暂停播放时出现错误:\",i),this.playing=!1}this.err=\"\"},forcePlay:function(){var e=this;console.log(\"强制播放方法被调用，UID:\",this._uid);try{if(o[this._uid]){Date.now();var i=o[this._uid].isPlaying();if(console.log(\"当前播放状态:\",i,\"UID:\",this._uid),console.log(\"强制启动播放，UID:\",this._uid),o[this._uid].play){var t=o[this._uid].play();console.log(\"调用play()方法结果:\",t,\"UID:\",this._uid),setTimeout((function(){var i=o[e._uid].isPlaying(),t=Date.now()-e.lastPlayTimeUpdate<2e3;console.log(\"强制播放后检查:\",{isPlaying:i,hasRecentTimeUpdate:t,lastTimeUpdate:e.lastPlayTimeUpdate,uid:e._uid}),i&&t?(console.log(\"强制播放成功，播放器正在播放，UID:\",e._uid),e.playing=!0):i&&!t?(console.warn(\"播放器状态为播放但无时间回调，可能未真正播放，UID:\",e._uid),e.playing=!1,setTimeout((function(){o[e._uid]&&o[e._uid].play&&(console.log(\"再次尝试播放，UID:\",e._uid),o[e._uid].play(),setTimeout((function(){var i=o[e._uid].isPlaying(),t=Date.now()-e.lastPlayTimeUpdate<2e3;console.log(\"二次尝试后检查:\",{isPlaying:i,hasRecentTimeUpdate:t,uid:e._uid}),e.playing=i&&t,console.log(\"最终播放状态:\",e.playing,\"UID:\",e._uid)}),500))}),300)):(console.warn(\"强制播放失败，播放器未播放，UID:\",e._uid),e.playing=!1)}),500)}}else console.warn(\"播放器实例不存在，无法强制播放，UID:\",this._uid)}catch(n){console.warn(\"强制播放时出现错误:\",n),this.playing=!1}},mute:function(){o[this._uid]&&(o[this._uid].setVoice(0),this.isNotMute=!1)},cancelMute:function(){o[this._uid]&&(o[this._uid].setVoice(1),this.isNotMute=!0)},destroy:function(){if(console.log(\"开始销毁h265web播放器，UID:\",this._uid),this.playing=!1,this.loaded=!1,this.playerLoading=!1,this.err=\"\",o[this._uid])try{console.log(\"正在销毁播放器实例，UID:\",this._uid),o[this._uid].pause&&o[this._uid].pause();try{o[this._uid].release&&(o[this._uid].release(),console.log(\"播放器资源已释放，UID:\",this._uid))}catch(e){console.warn(\"释放播放器资源时出现错误:\",e)}o[this._uid]=null,console.log(\"播放器引用已清空，UID:\",this._uid),this.clearPlayerDOM()}catch(e){console.warn(\"销毁播放器时出现错误:\",e),o[this._uid]=null,this.clearPlayerDOM()}else console.log(\"播放器实例不存在，直接清理DOM，UID:\",this._uid),this.clearPlayerDOM();console.log(\"h265web播放器销毁完成，UID:\",this._uid)},clearPlayerDOM:function(){try{if(this.$refs.playerBox){console.log(\"清理播放器DOM容器，UID:\",this._uid);while(this.$refs.playerBox.firstChild)this.$refs.playerBox.removeChild(this.$refs.playerBox.firstChild);this.$refs.playerBox.style.cssText=\"\",this.$refs.playerBox.innerHTML=\"\",console.log(\"播放器DOM容器已清理，UID:\",this._uid)}}catch(e){console.warn(\"清理DOM容器时出现错误:\",e)}},fullscreenSwich:function(){var e=this.isFullscreen();e?o[this._uid].closeFullScreen():o[this._uid].fullScreen(),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1},setPlaybackRate:function(e){try{o[this._uid]&&o[this._uid].setPlaybackRate&&o[this._uid].setPlaybackRate(e)}catch(i){console.warn(\"设置播放倍速时出现错误:\",i)}},seek:function(e){var i=this;try{if(console.log(\"h265web播放器seek方法被调用，目标时间:\",e,\"秒\"),console.log(\"播放器状态:\",{playerExists:!!o[this._uid],seekMethodExists:!(!o[this._uid]||!o[this._uid].seek),playerUid:this._uid,loaded:this.loaded,playing:this.playing}),o[this._uid]&&o[this._uid].seek){console.log(\"执行播放器seek操作到:\",e,\"秒\"),e<0&&(console.warn(\"seek时间小于0，调整为0\"),e=0);var t=this.playing;return o[this._uid].seek(e),console.log(\"播放器seek操作已执行，之前播放状态:\",t),setTimeout((function(){try{i.$emit(\"seekFinish\"),console.log(\"h265web播放器seek操作完成\"),console.log(\"h265web播放器seek完成，播放状态由父组件控制\")}catch(e){console.warn(\"触发seek完成事件时出现错误:\",e)}}),200),!0}return console.warn(\"播放器未准备好或不支持seek操作\",{playerExists:!!o[this._uid],seekMethodExists:!(!o[this._uid]||!o[this._uid].seek)}),!1}catch(n){return console.error(\"播放器seek时出现错误:\",n),!1}},getCurrentTime:function(){try{if(o[this._uid]){if(o[this._uid].getCurrentTime)return o[this._uid].getCurrentTime();if(o[this._uid].getTime)return o[this._uid].getTime();if(void 0!==o[this._uid].currentTime)return o[this._uid].currentTime}return null}catch(e){return console.warn(\"获取播放器当前时间时出现错误:\",e),null}},getPlayerStatus:function(){try{return o[this._uid]?{playing:this.playing,loaded:this.loaded,playerExists:!0,hasSeekMethod:!!o[this._uid].seek,hasTimeMethod:!(!o[this._uid].getCurrentTime&&!o[this._uid].getTime)}:{playing:!1,loaded:!1,playerExists:!1,hasSeekMethod:!1,hasTimeMethod:!1}}catch(e){return console.warn(\"获取播放器状态时出现错误:\",e),null}},ensureGlobalInterceptorActive:function(){try{if(window.h265webInterceptor){var e=window.h265webInterceptor.status();e.active||window.h265webInterceptor.start()}}catch(i){}}}},c=a,r=(t(\"aae4\"),t(\"2877\")),u=Object(r[\"a\"])(c,n,s,!1,null,null,null);i[\"a\"]=u.exports},aae4:function(e,i,t){\"use strict\";t(\"eb3a\")},eb3a:function(e,i,t){}}]);", "extractedComments": []}