(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ff9b6b9","chunk-baa1c8c2","chunk-b3c5ace6"],{1502:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"live-container",attrs:{id:"live"}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"live-content",attrs:{"element-loading-text":"拼命加载中"}},[i("div",{staticClass:"device-tree-container"},[i("DeviceTree",{attrs:{"click-event":e.clickEvent,"context-menu-event":e.contextMenuEvent}})],1),i("div",{staticClass:"video-container"},[i("div",{staticClass:"control-bar"},[i("div",{staticClass:"split-controls"},[e._v(" 分屏: "),i("i",{staticClass:"iconfont icon-a-mti-1fenpingshi btn",class:{active:0===e.spiltIndex},on:{click:function(t){e.spiltIndex=0}}}),i("i",{staticClass:"iconfont icon-a-mti-4fenpingshi btn",class:{active:1===e.spiltIndex},on:{click:function(t){e.spiltIndex=1}}}),i("i",{staticClass:"iconfont icon-a-mti-6fenpingshi btn",class:{active:2===e.spiltIndex},on:{click:function(t){e.spiltIndex=2}}}),i("i",{staticClass:"iconfont icon-a-mti-9fenpingshi btn",class:{active:3===e.spiltIndex},on:{click:function(t){e.spiltIndex=3}}})]),i("div",{staticClass:"fullscreen-control"},[i("i",{staticClass:"el-icon-full-screen btn",on:{click:function(t){return e.fullScreen()}}})])]),i("div",{staticClass:"player-container"},[i("div",{ref:"playBox",staticClass:"play-grid",style:e.liveStyle},e._l(e.layout[e.spiltIndex].spilt,(function(t){return i("div",{key:t,staticClass:"play-box",class:e.getPlayerClass(e.spiltIndex,t),on:{click:function(i){e.playerIdx=t-1}}},[e.videoUrl[t-1]?i("player",{ref:"player"[t-1],refInFor:!0,attrs:{"video-url":e.videoUrl[t-1],fluent:"",autoplay:""},on:{screenshot:e.shot,destroy:e.destroy}}):i("div",{staticClass:"no-signal"},[e._v(e._s(e.videoTip[t-1]?e.videoTip[t-1]:"无信号"))])],1)})),0)])])])])},a=[],n=(i("c19f"),i("ace4"),i("e9c4"),i("b64b"),i("d3b7"),i("3ca3"),i("5cc6"),i("9a8c"),i("a975"),i("735e"),i("c1ac"),i("d139"),i("3a7b"),i("d5d6"),i("82f8"),i("e91f"),i("60bd"),i("5f96"),i("3280"),i("3fcc"),i("ca91"),i("25a1"),i("cd26"),i("3c5d"),i("2954"),i("649e"),i("219c"),i("170b"),i("b39a"),i("72f7"),i("ddb0"),i("2b3d"),i("bf19"),i("9861"),i("2655")),o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"device-tree-container",attrs:{id:"DeviceTree"}},[i("div",{staticClass:"device-tree-header"},[i("div",{staticClass:"header-title"},[e._v("通道列表")]),i("div",{staticClass:"header-switch"},[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"rgb(64, 158, 255)","active-text":"行政区划","inactive-text":"业务分组"},model:{value:e.showRegion,callback:function(t){e.showRegion=t},expression:"showRegion"}})],1)]),i("div",{staticClass:"tree-content"},[i("div",{staticClass:"tree-wrapper"},[e.showRegion?i("RegionTree",{ref:"regionTree",attrs:{edit:!1,"show-header":!1,"has-channel":!0,"click-event":e.treeNodeClickEvent,"default-expanded-keys":[]}}):e._e(),e.showRegion?e._e():i("GroupTree",{ref:"groupTree",attrs:{edit:!1,"show-header":!1,"has-channel":!0,"click-event":e.treeNodeClickEvent,"default-expanded-keys":[]}})],1)])])},s=[],c=(i("0643"),i("4e3e"),i("159b"),i("94b9")),r=i("c2c8"),d={name:"DeviceTree",components:{GroupTree:r["a"],RegionTree:c["a"]},props:{device:{type:Object,default:function(){return{}}},onlyCatalog:{type:Boolean,default:!1},clickEvent:{type:Function,default:null},contextMenuEvent:{type:Function,default:null}},data:function(){return{showRegion:!0,defaultProps:{children:"children",label:"name",isLeaf:"isLeaf"}}},mounted:function(){var e=this;this.$nextTick((function(){e.fixTreeScrollbars(),e.adjustTreeHeight(),window.addEventListener("resize",e.adjustTreeHeight)}))},updated:function(){var e=this;this.$nextTick((function(){e.fixTreeScrollbars(),e.adjustTreeHeight()}))},beforeDestroy:function(){window.removeEventListener("resize",this.adjustTreeHeight)},methods:{adjustTreeHeight:function(){var e=this.$el.clientHeight,t=this.$el.querySelector(".device-tree-header").clientHeight,i=e-t-30,l=this.$el.querySelector(".tree-content");l&&(l.style.height="".concat(i,"px"));var a=this.$el.querySelectorAll(".el-tree");a.forEach((function(e){e.style.height="100%",e.style.maxHeight="100%"}))},fixTreeScrollbars:function(){var e=this.$el.querySelectorAll(".el-tree");e.forEach((function(e){e.style.overflow="visible",e.style.width="100%";var t=e.querySelectorAll('[style*="overflow"]');t.forEach((function(e){"auto"!==e.style.overflow&&"scroll"!==e.style.overflow||(e.style.overflow="visible")}))}))},handleClick:function(e,t){},treeNodeClickEvent:function(e){e.leaf&&(console.log(23111),console.log(e),this.clickEvent&&this.clickEvent(e.id))}}},u=d,h=(i("2997"),i("2877")),v=Object(h["a"])(u,o,s,!1,null,null,null),p=v.exports,f=i("93bf"),g=i.n(f),m={name:"Live",components:{player:n["default"],DeviceTree:p},data:function(){return{videoUrl:[""],videoTip:[""],spiltIndex:2,playerIdx:0,updateLooper:0,count:15,total:0,loading:!1,layout:[{spilt:1,columns:"1fr",rows:"1fr",style:function(){}},{spilt:4,columns:"1fr 1fr",rows:"1fr 1fr",style:function(){}},{spilt:6,columns:"1fr 1fr 1fr",rows:"1fr 1fr 1fr",style:function(e){if(console.log(e),0===e)return{gridColumn:" 1 / span 2",gridRow:" 1 / span 2"}}},{spilt:9,columns:"1fr 1fr 1fr",rows:"1fr 1fr 1fr",style:function(){}}]}},computed:{liveStyle:function(){return{display:"grid",gridTemplateColumns:this.layout[this.spiltIndex].columns,gridTemplateRows:this.layout[this.spiltIndex].rows,gap:"4px",backgroundColor:"#a9a8a8"}}},watch:{spilt:function(e){var t=this;console.log("切换画幅;"+e);for(var i=this,l=function(e){if(!i.$refs["player"+e])return 1;t.$nextTick((function(){i.$refs["player"+e]instanceof Array?i.$refs["player"+e][0].resize():i.$refs["player"+e].resize()}))},a=1;a<=e;a++)l(a);window.localStorage.setItem("split",e)},"$route.fullPath":"checkPlayByParam"},mounted:function(){window.addEventListener("resize",this.handleResize),this.handleResize()},created:function(){this.checkPlayByParam()},destroyed:function(){clearTimeout(this.updateLooper),window.removeEventListener("resize",this.handleResize)},methods:{handleResize:function(){var e=this;this.$forceUpdate(),this.$nextTick((function(){for(var t=0;t<e.layout[e.spiltIndex].spilt;t++){var i=e.$refs["player".concat(t+1)];i&&(i instanceof Array?i[0].resize&&i[0].resize():i.resize&&i.resize())}}))},destroy:function(e){console.log(e),this.clear(e.substring(e.length-1))},clickEvent:function(e){this.sendDevicePush(e)},getPlayerClass:function(e,t){var i="play-box-"+e+"-"+t;return this.playerIdx===t-1&&(i+=" redborder"),i},contextMenuEvent:function(e,t,i,l){},sendDevicePush:function(e){var t=this;this.save(e);var i=this.playerIdx;this.setPlayUrl("",i),this.$set(this.videoTip,i,"正在拉流..."),this.$store.dispatch("commonChanel/playChannel",e).then((function(e){var l;l="https:"===location.protocol?e.wss_flv:e.ws_flv,t.setPlayUrl(l,i)})).catch((function(e){t.$set(t.videoTip,i,"播放失败: "+e)})).finally((function(){t.loading=!1}))},setPlayUrl:function(e,t){this.$set(this.videoUrl,t,e);var i=this;setTimeout((function(){window.localStorage.setItem("videoUrl",JSON.stringify(i.videoUrl))}),100)},checkPlayByParam:function(){var e=this.$route.query;e.channelId&&this.sendDevicePush(e.channelId)},shot:function(e){var t=function(e){for(var t=e.split(";base64,"),i=t[0].split(":")[1],l=window.atob(t[1]),a=l.length,n=new Uint8Array(a),o=0;o<a;++o)n[o]=l.charCodeAt(o);return new Blob([n],{type:i})},i=document.createElement("a"),l=t(e),a=document.createEvent("HTMLEvents");a.initEvent("click",!0,!0),i.download="截图",i.href=URL.createObjectURL(l),i.click()},save:function(e){var t=window.localStorage.getItem("playData")||"[]",i=JSON.parse(t);i[this.playerIdx]=e,window.localStorage.setItem("playData",JSON.stringify(i))},clear:function(e){var t=window.localStorage.getItem("playData")||"[]",i=JSON.parse(t);i[e-1]=null,console.log(i),window.localStorage.setItem("playData",JSON.stringify(i))},fullScreen:function(){g.a.isEnabled&&g.a.toggle(this.$refs.playBox)}}},b=m,y=(i("a35a"),Object(h["a"])(b,l,a,!1,null,null,null));t["default"]=y.exports},"165c":function(e,t,i){"use strict";var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"生成国标编码",width:"65rem",top:"2rem",center:"","append-to-body":!0,"close-on-click-modal":!1,visible:e.showVideoDialog,"destroy-on-close":!1},on:{"update:visible":function(t){e.showVideoDialog=t}}},[i("el-tabs",{staticStyle:{padding:"0 1rem",margin:"auto 0"},on:{"tab-click":e.getRegionList},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[i("el-tab-pane",{attrs:{name:"0"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[0].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[0].meaning))])]),i("el-radio-group",{model:{value:e.allVal[0].val,callback:function(t){e.$set(e.allVal[0],"val",t)},expression:"allVal[0].val"}},e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId)+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"1"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[1].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[1].meaning))])]),i("el-radio-group",{attrs:{disabled:e.allVal[1].lock},model:{value:e.allVal[1].val,callback:function(t){e.$set(e.allVal[1],"val",t)},expression:"allVal[1].val"}},e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(2)}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(2))+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"2"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[2].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[2].meaning))])]),i("el-radio-group",{attrs:{disabled:e.allVal[2].lock},model:{value:e.allVal[2].val,callback:function(t){e.$set(e.allVal[2],"val",t)},expression:"allVal[2].val"}},e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(4)}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(4))+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"3"}},[e._v(" 请手动输入基层接入单位编码,两位数字 "),i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[3].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[3].meaning))])]),i("el-input",{attrs:{type:"text",placeholder:"请输入内容",maxlength:"2",disabled:e.allVal[3].lock,"show-word-limit":""},model:{value:e.allVal[3].val,callback:function(t){e.$set(e.allVal[3],"val",t)},expression:"allVal[3].val"}})],1),i("el-tab-pane",{attrs:{name:"4"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[4].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[4].meaning))])]),i("el-radio-group",{attrs:{disabled:e.allVal[4].lock},model:{value:e.allVal[4].val,callback:function(t){e.$set(e.allVal[4],"val",t)},expression:"allVal[4].val"}},e._l(e.industryCodeTypeList,(function(t){return i("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"5"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[5].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[5].meaning))])]),i("el-radio-group",{attrs:{disabled:e.allVal[5].lock},model:{value:e.allVal[5].val,callback:function(t){e.$set(e.allVal[5],"val",t)},expression:"allVal[5].val"}},e._l(e.deviceTypeList,(function(t){return i("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"6"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[6].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[6].meaning))])]),i("el-radio-group",{attrs:{disabled:e.allVal[6].lock},model:{value:e.allVal[6].val,callback:function(t){e.$set(e.allVal[6],"val",t)},expression:"allVal[6].val"}},e._l(e.networkIdentificationTypeList,(function(t){return i("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),i("el-tab-pane",{attrs:{name:"7"}},[e._v(" 请手动输入设备/用户序号, 六位数字 "),i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[7].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[7].meaning))])]),i("el-input",{attrs:{type:"text",placeholder:"请输入内容",maxlength:"6",disabled:e.allVal[7].lock,"show-word-limit":""},model:{value:e.allVal[7].val,callback:function(t){e.$set(e.allVal[7],"val",t)},expression:"allVal[7].val"}})],1)],1),i("el-form",{},[i("el-form-item",{staticStyle:{"margin-top":"22px","margin-bottom":"0"}},[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("保存")]),i("el-button",{on:{click:e.closeModel}},[e._v("取消")])],1)])],1)],1)},a=[],n=i("a888"),o={directives:{elDragDialog:n["a"]},props:{},data:function(){return{showVideoDialog:!1,activeKey:"0",allVal:[{id:[1,2],meaning:"省级编码",val:"11",type:"中心编码",lock:!1},{id:[3,4],meaning:"市级编码",val:"01",type:"中心编码",lock:!1},{id:[5,6],meaning:"区级编码",val:"01",type:"中心编码",lock:!1},{id:[7,8],meaning:"基层接入单位编码",val:"01",type:"中心编码",lock:!1},{id:[9,10],meaning:"行业编码",val:"00",type:"行业编码",lock:!1},{id:[11,13],meaning:"类型编码",val:"132",type:"类型编码",lock:!1},{id:[14],meaning:"网络标识编码",val:"7",type:"网络标识",lock:!1},{id:[15,20],meaning:"设备/用户序号",val:"000001",type:"序号",lock:!1}],regionList:[],deviceTypeList:[],industryCodeTypeList:[],networkIdentificationTypeList:[],endCallBck:null}},computed:{},methods:{openDialog:function(e,t,i,l){console.log(t),this.showVideoDialog=!0,this.activeKey="0",this.regionList=[],this.getRegionList(),"undefined"!==typeof t&&20===t.length&&(this.allVal[0].val=t.substring(0,2),this.allVal[1].val=t.substring(2,4),this.allVal[2].val=t.substring(4,6),this.allVal[3].val=t.substring(6,8),this.allVal[4].val=t.substring(8,10),this.allVal[5].val=t.substring(10,13),this.allVal[6].val=t.substring(13,14),this.allVal[7].val=t.substring(14)),console.log(this.allVal),"undefined"!==typeof i&&(this.allVal[i].lock=!0,this.allVal[i].val=l),this.endCallBck=e},getRegionList:function(){if("0"===this.activeKey||"1"===this.activeKey||"2"===this.activeKey){var e="";"1"===this.activeKey&&(e=this.allVal[0].val),"2"===this.activeKey&&(e=this.allVal[0].val+this.allVal[1].val),"0"!==this.activeKey&&""===e&&this.$message.error({showClose:!0,message:"请先选择上级行政区划"}),this.queryChildList(e)}else"4"===this.activeKey?(console.log(222),this.queryIndustryCodeList()):"5"===this.activeKey?this.queryDeviceTypeList():"6"===this.activeKey&&this.queryNetworkIdentificationTypeList()},queryChildList:function(e){var t=this;this.regionList=[],this.$store.dispatch("region/queryChildListInBase",e).then((function(e){t.regionList=e})).catch((function(e){t.$message.error({showClose:!0,message:e})}))},queryIndustryCodeList:function(){var e=this;this.industryCodeTypeList=[],this.$store.dispatch("commonChanel/getIndustryList").then((function(t){e.industryCodeTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},queryDeviceTypeList:function(){var e=this;this.deviceTypeList=[],this.$store.dispatch("commonChanel/getTypeList").then((function(t){e.deviceTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},queryNetworkIdentificationTypeList:function(){var e=this;this.networkIdentificationTypeList=[],this.$store.dispatch("commonChanel/getNetworkIdentificationList").then((function(t){e.networkIdentificationTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},closeModel:function(){this.showVideoDialog=!1},handleOk:function(){var e=this.allVal[0].val+this.allVal[1].val+this.allVal[2].val+this.allVal[3].val+this.allVal[4].val+this.allVal[5].val+this.allVal[6].val+this.allVal[7].val;console.log(e),this.endCallBck&&this.endCallBck(e),this.showVideoDialog=!1}}},s=o,c=(i("6c17"),i("2877")),r=Object(c["a"])(s,l,a,!1,null,null,null);t["a"]=r.exports},2655:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{ref:"container",staticStyle:{width:"100%",height:"100%","background-color":"#000000",margin:"0 auto",position:"relative"},on:{dblclick:e.fullscreenSwich}},[i("div",{staticStyle:{width:"100%","padding-top":"56.25%",position:"relative"}}),i("div",{staticClass:"buttons-box",attrs:{id:"buttonsBox"}},[i("div",{staticClass:"buttons-box-left"},[e.playing?e._e():i("i",{staticClass:"iconfont icon-play jessibuca-btn",on:{click:e.playBtnClick}}),e.playing?i("i",{staticClass:"iconfont icon-pause jessibuca-btn",on:{click:e.pause}}):e._e(),i("i",{staticClass:"iconfont icon-stop jessibuca-btn",on:{click:e.destroy}}),e.isNotMute?i("i",{staticClass:"iconfont icon-audio-high jessibuca-btn",on:{click:function(t){return e.mute()}}}):e._e(),e.isNotMute?e._e():i("i",{staticClass:"iconfont icon-audio-mute jessibuca-btn",on:{click:function(t){return e.cancelMute()}}})]),i("div",{staticClass:"buttons-box-right"},[i("span",{staticClass:"jessibuca-btn"},[e._v(e._s(e.kBps)+" kb/s")]),i("i",{staticClass:"iconfont icon-camera1196054easyiconnet jessibuca-btn",staticStyle:{"font-size":"1rem !important"},on:{click:e.screenshot}}),i("i",{staticClass:"iconfont icon-shuaxin11 jessibuca-btn",on:{click:e.playBtnClick}}),e.fullscreen?e._e():i("i",{staticClass:"iconfont icon-weibiaoti10 jessibuca-btn",on:{click:e.fullscreenSwich}}),e.fullscreen?i("i",{staticClass:"iconfont icon-weibiaoti11 jessibuca-btn",on:{click:e.fullscreenSwich}}):e._e()])])])},a=[],n=i("5530"),o={},s={name:"Jessibuca",props:["videoUrl","error","hasAudio","height"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1}},watch:{videoUrl:{handler:function(e,t){var i=this;this.$nextTick((function(){i.play(e)}))},immediate:!0}},created:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){e.updatePlayerDomSize(),window.onresize=e.updatePlayerDomSize,"undefined"===typeof e.videoUrl&&(e.videoUrl=t),e.btnDom=document.getElementById("buttonsBox")}))},mounted:function(){this.updatePlayerDomSize()},destroyed:function(){o[this._uid]&&o[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.performance=""},methods:{updatePlayerDomSize:function(){var e=this,t=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(t){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(t.parentNode));var i=t.parentNode.clientWidth,l=t.parentNode.clientHeight,a=i,n=9/16*a;l>0&&i>l/9*16&&(n=l,a=l/9*16);var s=Math.min(document.body.clientHeight,document.documentElement.clientHeight);n>s&&(n=s,a=16/9*n),this.playerWidth=a,this.playerHeight=n,this.playing&&o[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(){var e={container:this.$refs.container,autoWasm:!0,background:"",controlAutoHide:!1,debug:!1,decoder:"static/js/jessibuca/decoder.js",forceNoOffscreen:!1,hasAudio:"undefined"===typeof this.hasAudio||this.hasAudio,heartTimeout:5,heartTimeoutReplay:!0,heartTimeoutReplayTimes:3,hiddenAutoPause:!1,hotKey:!0,isFlv:!1,isFullResize:!1,isNotMute:this.isNotMute,isResize:!0,keepScreenOn:!0,loadingText:"请稍等, 视频加载中......",loadingTimeout:10,loadingTimeoutReplay:!0,loadingTimeoutReplayTimes:3,openWebglAlignment:!1,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},recordType:"mp4",rotate:0,showBandwidth:!1,supportDblclickFullscreen:!1,timeout:10,useMSE:!0,useWCS:!1,useWebFullScreen:!0,videoBuffer:.1,wasmDecodeErrorReplay:!0,wcsUseVideoRender:!0};console.log("Jessibuca -> options: ",e),o[this._uid]=new window.Jessibuca(Object(n["a"])({},e));var t=o[this._uid],i=this;t.on("pause",(function(){i.playing=!1})),t.on("play",(function(){i.playing=!0})),t.on("fullscreen",(function(e){i.fullscreen=e})),t.on("mute",(function(e){i.isNotMute=!e})),t.on("performance",(function(e){var t="卡顿";2===e?t="非常流畅":1===e&&(t="流畅"),i.performance=t})),t.on("kBps",(function(e){i.kBps=Math.round(e)})),t.on("videoInfo",(function(e){console.log("Jessibuca -> videoInfo: ",e)})),t.on("audioInfo",(function(e){console.log("Jessibuca -> audioInfo: ",e)})),t.on("error",(function(e){console.log("Jessibuca -> error: ",e)})),t.on("timeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("loadingTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("delayTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("playToRenderTimes",(function(e){console.log("Jessibuca -> playToRenderTimes: ",e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log("Jessibuca -> url: ",e),o[this._uid]&&this.destroy(),this.create(),o[this._uid].on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=jessibuca.quieting})),o[this._uid].hasLoaded()?o[this._uid].play(e):o[this._uid].on("load",(function(){o[t._uid].play(e)}))},pause:function(){o[this._uid]&&o[this._uid].pause(),this.playing=!1,this.err="",this.performance=""},screenshot:function(){o[this._uid]&&o[this._uid].screenshot()},mute:function(){o[this._uid]&&o[this._uid].mute()},cancelMute:function(){o[this._uid]&&o[this._uid].cancelMute()},destroy:function(){o[this._uid]&&o[this._uid].destroy(),null==document.getElementById("buttonsBox")&&this.$refs.container.appendChild(this.btnDom),o[this._uid]=null,this.playing=!1,this.err="",this.performance=""},fullscreenSwich:function(){var e=this.isFullscreen();o[this._uid].setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}}},c=s,r=(i("989a"),i("2877")),d=Object(r["a"])(c,l,a,!1,null,null,null);t["default"]=d.exports},2997:function(e,t,i){"use strict";i("da2e")},"363b":function(e,t,i){"use strict";var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"chooseCivilCode"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"选择行政区划",width:"30%",top:"5rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("RegionTree",{ref:"regionTree",attrs:{"show-header":!0,edit:!0,"enable-add-channel":!1,"click-event":e.treeNodeClickEvent,"on-channel-change":e.onChannelChange,"tree-height":"45vh"}}),i("el-form",[i("el-form-item",[i("div",{staticStyle:{"text-align":"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)],1)},a=[],n=i("a888"),o=i("94b9"),s={name:"ChooseCivilCode",directives:{elDragDialog:n["a"]},components:{RegionTree:o["a"]},props:{},data:function(){return{showDialog:!1,endCallback:!1,regionDeviceId:""}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.endCallback=e},onSubmit:function(){this.endCallback&&this.endCallback(this.regionDeviceId),this.close()},close:function(){this.showDialog=!1},treeNodeClickEvent:function(e){this.regionDeviceId=e.deviceId},onChannelChange:function(e){}}},c=s,r=i("2877"),d=Object(r["a"])(c,l,a,!1,null,null,null);t["a"]=d.exports},"404f":function(e,t,i){},"54b8":function(e,t,i){},"6c17":function(e,t,i){"use strict";i("92b6")},"92b6":function(e,t,i){},"953f":function(e,t,i){},"989a":function(e,t,i){"use strict";i("953f")},a35a:function(e,t,i){"use strict";i("404f")},b4b5:function(e,t,i){"use strict";i("54b8")},c2c8:function(e,t,i){"use strict";var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"border-right":"1px solid #EBEEF5",padding:"0 20px"},attrs:{id:"DeviceTree"}},[e.showHeader?i("div",{staticClass:"page-header"},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{staticStyle:{visibility:"hidden"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"12rem"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"显示编号"}},[i("el-checkbox",{model:{value:e.showCode,callback:function(t){e.showCode=t},expression:"showCode"}})],1)],1)],1):e._e(),i("div",[e.showAlert&&e.edit?i("el-alert",{staticStyle:{"text-align":"left"},attrs:{title:"操作提示",description:"你可以使用右键菜单管理节点",type:"info"}}):e._e(),i("vue-easy-tree",{ref:"veTree",staticClass:"flow-tree",attrs:{"node-key":"treeId",height:e.treeHeight?e.treeHeight:"78vh",lazy:"",load:e.loadNode,data:e.treeData,props:e.props,"default-expanded-keys":[""]},on:{"node-contextmenu":e.contextmenuEventHandler,"node-click":e.nodeClickHandler},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.node;t.data;return[i("span",{staticClass:"custom-tree-node"},[0===l.data.type&&e.chooseId!==l.data.deviceId?i("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#409EFF"}}):e._e(),0===l.data.type&&e.chooseId===l.data.deviceId?i("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#c60135"}}):e._e(),1===l.data.type&&"ON"===l.data.status?i("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#409EFF"}}):e._e(),1===l.data.type&&"ON"!==l.data.status?i("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#808181"}}):e._e(),""!==l.data.deviceId&&e.showCode?i("span",{staticStyle:{"padding-left":"1px"},attrs:{title:l.data.deviceId}},[e._v(e._s(l.label)+"（编号："+e._s(l.data.deviceId)+"）")]):e._e(),""!==l.data.deviceId&&e.showCode?e._e():i("span",{staticStyle:{"padding-left":"1px"},attrs:{title:l.data.deviceId}},[e._v(e._s(l.label))])])]}}])})],1),i("groupEdit",{ref:"groupEdit"}),i("gbDeviceSelect",{ref:"gbDeviceSelect"}),i("gbChannelSelect",{ref:"gbChannelSelect",attrs:{"data-type":"group"}})],1)},a=[],n=(i("d3b7"),i("9331")),o=i.n(n),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{id:"groupEdit"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"分组编辑",width:"40%",top:"2rem","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{"margin-top":"1rem","margin-right":"100px"},attrs:{id:"shared"}},[i("el-form",{ref:"form",attrs:{model:e.group,"label-width":"140px"}},[i("el-form-item",{attrs:{label:"节点编号",prop:"id"}},[i("el-input",{attrs:{placeholder:"请输入编码"},model:{value:e.group.deviceId,callback:function(t){e.$set(e.group,"deviceId",t)},expression:"group.deviceId"}},[i("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.buildDeviceIdCode(e.group.deviceId)}},slot:"append"},[e._v("生成")])],1)],1),i("el-form-item",{attrs:{label:"节点名称",prop:"name"}},[i("el-input",{attrs:{clearable:""},model:{value:e.group.name,callback:function(t){e.$set(e.group,"name",t)},expression:"group.name"}})],1),i("el-form-item",{attrs:{label:"行政区划",prop:"name"}},[i("el-input",{model:{value:e.group.civilCode,callback:function(t){e.$set(e.group,"civilCode",t)},expression:"group.civilCode"}},[i("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.buildCivilCode(e.group.civilCode)}},slot:"append"},[e._v("选择")])],1)],1),i("el-form-item",[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确认")]),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)]),i("channelCode",{ref:"channelCode"}),i("chooseCivilCode",{ref:"chooseCivilCode"})],1)},c=[],r=i("165c"),d=i("363b"),u=i("a888"),h={name:"GroupEdit",directives:{elDragDialog:u["a"]},components:{ChooseCivilCode:d["a"],channelCode:r["a"]},props:[],data:function(){return{submitCallback:null,showDialog:!1,loading:!1,level:0,group:{id:0,deviceId:"",name:"",parentDeviceId:"",businessGroup:"",civilCode:"",platformId:""}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),e&&(this.group=e),this.showDialog=!0,this.submitCallback=t},onSubmit:function(){var e=this;this.group.id?this.$store.dispatch("group/update",this.group).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.submitCallback&&e.submitCallback(e.group)})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})):this.$store.dispatch("group/add",this.group).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.submitCallback&&e.submitCallback(e.group),e.close()})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})}))},buildDeviceIdCode:function(e){var t=this;console.log(this.group);var i=this.group.businessGroup?"216":"215";this.$refs.channelCode.openDialog((function(e){t.group.deviceId=e}),e,5,i)},buildCivilCode:function(e){var t=this;this.$refs.chooseCivilCode.openDialog((function(e){t.group.civilCode=e}))},close:function(){this.showDialog=!1,console.log(this.group)}}},v=h,p=i("2877"),f=Object(p["a"])(v,s,c,!1,null,null,null),g=f.exports,m=i("7d41"),b=i("1322"),y={name:"DeviceTree",components:{GbChannelSelect:b["a"],VueEasyTree:o.a,groupEdit:g,gbDeviceSelect:m["a"]},props:["edit","enableAddChannel","clickEvent","onChannelChange","showHeader","hasChannel","addChannelToGroup","treeHeight"],data:function(){return{props:{label:"name",id:"treeId"},showCode:!1,showAlert:!0,searchSrt:"",chooseId:"",treeData:[]}},created:function(){},destroyed:function(){},methods:{search:function(){},loadNode:function(e,t){var i=this;if(0===e.level)t([{treeId:"",deviceId:"",name:"根资源组",isLeaf:!1,type:0}]);else{if(e.data.leaf)return void t([]);this.$store.dispatch("group/getTreeList",{query:this.searchSrt,parent:e.data.id,hasChannel:this.hasChannel}).then((function(e){e.length>0&&(i.showAlert=!1),t(e)}))}},reset:function(){this.$forceUpdate()},contextmenuEventHandler:function(e,t,i,l){var a=this;if(this.edit){if(0===i.data.type){var n=[{label:"刷新节点",icon:"el-icon-refresh",disabled:!1,onClick:function(){a.refreshNode(i)}},{label:"新建节点",icon:"el-icon-plus",disabled:!1,onClick:function(){a.addGroup(t.id,i)}},{label:"编辑节点",icon:"el-icon-edit",disabled:1===i.level,onClick:function(){a.editGroup(t,i)}},{label:"删除节点",icon:"el-icon-delete",disabled:1===i.level,divided:!0,onClick:function(){a.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.removeGroup(t.id,i)})).catch((function(){}))}}];this.enableAddChannel&&(n.push({label:"添加设备",icon:"el-icon-plus",disabled:i.level<=2,onClick:function(){a.addChannelFormDevice(t.id,i)}}),n.push({label:"移除设备",icon:"el-icon-delete",disabled:i.level<=2,divided:!0,onClick:function(){a.removeChannelFormDevice(t.id,i)}}),n.push({label:"添加通道",icon:"el-icon-plus",disabled:i.level<=2,onClick:function(){a.addChannel(t.id,i)}})),this.$contextmenu({items:n,event:e,customClass:"custom-class",zIndex:3e3})}return!1}},removeGroup:function(e,t){var i=this;this.$store.dispatch("group/deleteGroup",t.data.id).then((function(e){t.parent.loaded=!1,t.parent.expand(),i.onChannelChange&&i.onChannelChange(t.data.deviceId)}))},addChannelFormDevice:function(e,t){var i=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var l=[],a=0;a<e.length;a++)l.push(e[a].id);i.$store.dispatch("group/add",{parentId:t.data.deviceId,businessGroup:t.data.businessGroup,deviceIds:l}).then((function(e){i.$message.success({showClose:!0,message:"保存成功"}),i.onChannelChange&&i.onChannelChange(),console.log(t),t.loaded=!1,t.expand()})).finally((function(){i.loading=!1}))}))},removeChannelFormDevice:function(e,t){var i=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var l=[],a=0;a<e.length;a++)l.push(e[a].id);i.$store.dispatch("commonChanel/deleteDeviceFromGroup",l).then((function(e){i.$message.success({showClose:!0,message:"保存成功"}),i.onChannelChange&&i.onChannelChange(),t.loaded=!1,t.expand()})).finally((function(){i.loading=!1}))}))},addChannel:function(e,t){var i=this;this.$refs.gbChannelSelect.openDialog((function(e){console.log("选择的数据"),console.log(e),i.addChannelToGroup(t.data.deviceId,t.data.businessGroup,e)}))},refreshNode:function(e){console.log(e),e.loaded=!1,e.expand()},refresh:function(e){console.log("刷新节点： "+e);var t=this.$refs.veTree.getNode(e);t&&(t.loaded=!1,t.expand())},addGroup:function(e,t){this.$refs.groupEdit.openDialog({id:0,name:"",deviceId:"",civilCode:"",parentDeviceId:t.level>2?t.data.deviceId:"",parentId:t.data.id,businessGroup:t.level>2?t.data.businessGroup:t.data.deviceId},(function(e){console.log(t),t.loaded=!1,t.expand()}),e)},editGroup:function(e,t){console.log(t),this.$refs.groupEdit.openDialog(t.data,(function(e){console.log(t),t.parent.loaded=!1,t.parent.expand()}),e)},nodeClickHandler:function(e,t,i){this.chooseId=e.deviceId,this.clickEvent&&this.clickEvent(e)}}},C=y,k=(i("b4b5"),Object(p["a"])(C,l,a,!1,null,null,null));t["a"]=k.exports},da2e:function(e,t,i){}}]);