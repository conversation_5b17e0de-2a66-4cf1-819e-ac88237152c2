(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-675011ec"],{4678:function(e,t,s){var i={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3b","./en-ie.js":"e1d3b","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function o(e){var t=r(e);return s(t)}function r(e){if(!s.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}o.keys=function(){return Object.keys(i)},o.resolve=r,e.exports=o,o.id="4678"},a888:function(e,t,s){"use strict";s("99af"),s("caad"),s("ac1f"),s("2532"),s("5319");var i={bind:function(e,t,s){var i=e.querySelector(".el-dialog__header"),o=e.querySelector(".el-dialog");i.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();i.onmousedown=function(e){var t=e.clientX-i.offsetLeft,n=e.clientY-i.offsetTop,a=o.offsetWidth,l=o.offsetHeight,c=document.body.clientWidth,u=document.body.clientHeight,d=o.offsetLeft,m=c-o.offsetLeft-a,f=o.offsetTop,p=u-o.offsetTop-l,h=r(o,"left"),g=r(o,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var i=e.clientX-t,r=e.clientY-n;-i>d?i=-d:i>m&&(i=m),-r>f?r=-f:r>p&&(r=p),o.style.cssText+=";left:".concat(i+h,"px;top:").concat(r+g,"px;"),s.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}},o=function(e){e.directive("el-drag-dialog",i)};window.Vue&&(window["el-drag-dialog"]=i,Vue.use(o)),i.install=o;t["a"]=i},e382:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container",attrs:{id:"app"}},[s("div",{staticStyle:{height:"calc(100vh - 124px)"}},[s("el-form",{attrs:{inline:!0,size:"mini"}},[s("el-form-item",[s("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.addUser}},[e._v(" 添加用户 ")])],1),s("el-form-item",{staticStyle:{float:"right"}})],1),s("el-table",{staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"small",data:e.userList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[s("el-table-column",{attrs:{prop:"username",label:"用户名","min-width":"160"}}),s("el-table-column",{attrs:{prop:"pushKey",label:"pushkey","min-width":"160"}}),s("el-table-column",{attrs:{prop:"role.name",label:"类型","min-width":"160"}}),s("el-table-column",{attrs:{label:"操作","min-width":"450",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(s){return e.edit(t.row)}}},[e._v("修改密码")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(s){return e.changePushKey(t.row)}}},[e._v("修改pushkey")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(s){return e.showUserApiKeyManager(t.row)}}},[e._v("管理ApiKey")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(s){return e.deleteUser(t.row)}}},[e._v("删除 ")])]}}])})],1),s("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),s("changePasswordForAdmin",{ref:"changePasswordForAdmin"}),s("changePushKey",{ref:"changePushKey"}),s("addUser",{ref:"addUser"}),s("apiKeyManager",{ref:"apiKeyManager"})],1)},o=[],r=(s("d3b7"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"changePassword"}},[s("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"修改密码",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("div",{staticStyle:{"margin-right":"20px"},attrs:{id:"shared"}},[s("el-form",{ref:"passwordForm",attrs:{rules:e.rules,"status-icon":"","label-width":"80px"}},[s("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.newPassword,callback:function(t){e.newPassword=t},expression:"newPassword"}})],1),s("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.confirmPassword,callback:function(t){e.confirmPassword=t},expression:"confirmPassword"}})],1),s("el-form-item",[s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)}),n=[],a=s("a888"),l={name:"ChangePasswordForAdmin",directives:{elDragDialog:a["a"]},props:{},data:function(){var e=this,t=function(t,s,i){""===s?i(new Error("请输入新密码")):(""!==e.confirmPassword&&e.$refs.passwordForm.validateField("confirmPassword"),i())},s=function(t,s,i){""===e.confirmPassword?i(new Error("请再次输入密码")):e.confirmPassword!==e.newPassword?i(new Error("两次输入密码不一致!")):i()};return{newPassword:null,confirmPassword:null,userId:null,showDialog:!1,isLoging:!1,listChangeCallback:null,form:{},rules:{newPassword:[{required:!0,validator:t,trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,20}$/,message:"密码长度在8-20位之间,由字母+数字+特殊字符组成"}],confirmPassword:[{required:!0,validator:s,trigger:"blur"}]}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.listChangeCallback=t,null!=e&&(this.form=e)},onSubmit:function(){var e=this;this.$store.dispatch("user/changePasswordForAdmin",{password:this.newPassword,userId:this.form.id}).then((function(t){e.$message({showClose:!0,message:"修改成功",type:"success"}),e.showDialog=!1})).catch((function(e){console.error(e)})).finally((function(){e.showDialog=!1}))},close:function(){this.showDialog=!1,this.newPassword=null,this.confirmPassword=null,this.userId=null,this.adminId=null}}},c=l,u=s("2877"),d=Object(u["a"])(c,r,n,!1,null,null,null),m=d.exports,f=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"changepushKey"}},[s("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"修改pushKey",width:"42%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("div",{staticStyle:{"margin-right":"18px"},attrs:{id:"shared"}},[s("el-form",{ref:"pushKeyForm",attrs:{rules:e.rules,"status-icon":"","label-width":"86px"}},[s("el-form-item",{attrs:{label:"新pushKey",prop:"newPushKey"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.newPushKey,callback:function(t){e.newPushKey=t},expression:"newPushKey"}})],1),s("el-form-item",[s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)},p=[],h={name:"ChangePushKey",directives:{elDragDialog:a["a"]},props:{},data:function(){var e=function(e,t,s){""===t?s(new Error("请输入新pushKey")):s()};return{newPushKey:null,confirmpushKey:null,userId:null,showDialog:!1,isLoging:!1,listChangeCallback:null,form:{},rules:{newpushKey:[{required:!0,validator:e,trigger:"blur"}]}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.listChangeCallback=t,null!=e&&(this.form=e)},onSubmit:function(){var e=this;this.$store.dispatch("user/changePushKey",{pushKey:this.newPushKey,userId:this.form.id}).then((function(t){e.$message({showClose:!0,message:"修改成功",type:"success"}),e.listChangeCallback(),e.showDialog=!1})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})}))},close:function(){this.showDialog=!1,this.newpushKey=null,this.userId=null,this.adminId=null}}},g=h,b=Object(u["a"])(g,f,p,!1,null,null,null),y=b.exports,w=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"addUser"}},[s("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加用户",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("div",{staticStyle:{"margin-right":"20px"},attrs:{id:"shared"}},[s("el-form",{ref:"passwordForm",attrs:{rules:e.rules,"status-icon":"","label-width":"80px"}},[s("el-form-item",{attrs:{label:"用户名",prop:"username"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.username,callback:function(t){e.username=t},expression:"username"}})],1),s("el-form-item",{attrs:{label:"用户类型",prop:"roleId"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.roleId,callback:function(t){e.roleId=t},expression:"roleId"}},e._l(e.options,(function(e){return s("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),s("el-form-item",{attrs:{label:"密码",prop:"password"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}})],1),s("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[s("el-input",{attrs:{autocomplete:"off"},model:{value:e.confirmPassword,callback:function(t){e.confirmPassword=t},expression:"confirmPassword"}})],1),s("el-form-item",[s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)},v=[],j={name:"AddUser",directives:{elDragDialog:a["a"]},props:{},data:function(){var e=this,t=function(t,s,i){""===s?i(new Error("请输入新密码")):(""!==e.confirmPassword&&e.$refs.passwordForm.validateField("confirmPassword"),i())},s=function(t,s,i){""===e.confirmPassword?i(new Error("请再次输入密码")):e.confirmPassword!==e.password?i(new Error("两次输入密码不一致!")):i()};return{value:"",options:[],loading:!1,username:null,password:null,roleId:null,confirmPassword:null,listChangeCallback:null,showDialog:!1,isLoging:!1,rules:{newPassword:[{required:!0,validator:t,trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,20}$/,message:"密码长度在8-20位之间,由字母+数字+特殊字符组成"}],confirmPassword:[{required:!0,validator:s,trigger:"blur"}]}}},computed:{},created:function(){this.getAllRole()},methods:{openDialog:function(e){this.listChangeCallback=e,this.showDialog=!0},onSubmit:function(){var e=this;this.$store.dispatch("user/add",{username:this.username,password:this.password,roleId:this.roleId}).then((function(t){e.$message({showClose:!0,message:"添加成功",type:"success"}),e.showDialog=!1,e.listChangeCallback()})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})}))},close:function(){this.showDialog=!1,this.password=null,this.confirmPassword=null,this.username=null,this.roleId=null},getAllRole:function(){var e=this;this.loading=!0,this.$store.dispatch("role/getAll").then((function(t){e.loading=!1,e.options=t}))}}},k=j,K=Object(u["a"])(k,w,v,!1,null,null,null),x=K.exports,A=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticStyle:{width:"100%"},attrs:{id:"app"}},[s("el-dialog",{attrs:{title:"ApiKey列表",width:"80%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("el-form",{attrs:{inline:!0,size:"mini"}},[s("el-form-item",[s("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.addUserApiKey}},[e._v(" 添加ApiKey ")])],1)],1),s("el-table",{staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"small",data:e.userList,height:e.winHeight,"header-row-class-name":"table-header"}},[s("el-table-column",{attrs:{prop:"user.username",label:"用户名","min-width":"120"}}),s("el-table-column",{attrs:{prop:"app",label:"应用名","min-width":"160"}}),s("el-table-column",{attrs:{label:"ApiKey","show-overflow-tooltip":!0,"min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("i",{directives:[{name:"clipboard",rawName:"v-clipboard",value:t.row.apiKey,expression:"scope.row.apiKey"}],staticClass:"cpoy-btn el-icon-document-copy",attrs:{title:"点击拷贝"},on:{success:function(t){return e.$message({type:"success",message:"成功拷贝到粘贴板"})}}}),s("span",[e._v(e._s(t.row.apiKey))])]}}])}),s("el-table-column",{attrs:{prop:"enable",label:"启用",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?s("el-tag",[e._v(" 启用 ")]):s("el-tag",{attrs:{type:"info"}},[e._v(" 停用 ")])]}}])}),s("el-table-column",{attrs:{label:"过期时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.expiredAt))+" ")]}}])}),s("el-table-column",{attrs:{prop:"remark",label:"备注信息","min-width":"160"}}),s("el-table-column",{attrs:{label:"操作","min-width":"260",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enable?s("el-button",{attrs:{size:"medium",icon:"el-icon-circle-close",type:"text"},on:{click:function(s){return e.disableUserApiKey(t.row)}}},[e._v(" 停用 ")]):s("el-button",{attrs:{size:"medium",icon:"el-icon-circle-check",type:"text"},on:{click:function(s){return e.enableUserApiKey(t.row)}}},[e._v(" 启用 ")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{attrs:{size:"medium",icon:"el-icon-refresh",type:"text"},on:{click:function(s){return e.resetUserApiKey(t.row)}}},[e._v(" 重置 ")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(s){return e.remarkUserApiKey(t.row)}}},[e._v(" 备注 ")]),s("el-divider",{attrs:{direction:"vertical"}}),s("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(s){return e.deleteUserApiKey(t.row)}}},[e._v(" 删除 ")])]}}])})],1),s("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),s("addUserApiKey",{ref:"addUserApiKey"}),s("remarkUserApiKey",{ref:"remarkUserApiKey"})],1)},U=[],$=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{id:"addUserApiKey"}},[s("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加ApiKey",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("div",{staticStyle:{"margin-right":"20px"},attrs:{id:"shared"}},[s("el-form",{ref:"formRef",attrs:{model:e.form,rules:e.rules,"status-icon":"","label-width":"80px"}},[s("el-form-item",{attrs:{label:"应用名",prop:"app"}},[s("el-input",{attrs:{property:"app",autocomplete:"off"},model:{value:e.form.app,callback:function(t){e.$set(e.form,"app",t)},expression:"form.app"}})],1),s("el-form-item",{staticStyle:{"text-align":"left"},attrs:{label:"启用状态",prop:"enable"}},[s("el-switch",{attrs:{property:"enable","active-text":"启用","inactive-text":"停用"},model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}})],1),s("el-form-item",{staticStyle:{"text-align":"left"},attrs:{label:"过期时间",prop:"expiresAt"}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{property:"expiresAt",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",placeholder:"选择过期时间"},model:{value:e.form.expiresAt,callback:function(t){e.$set(e.form,"expiresAt",t)},expression:"form.expiresAt"}})],1),s("el-form-item",{attrs:{label:"备注信息",prop:"remark"}},[s("el-input",{attrs:{type:"textarea",property:"remark",autocomplete:"off",autosize:{minRows:5},maxlength:"255","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),s("el-form-item",[s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)},D=[],C={name:"AddUserApiKey",directives:{elDragDialog:a["a"]},props:{},data:function(){return{userId:null,form:{app:null,enable:!0,expiresAt:null,remark:null},rules:{app:[{required:!0,trigger:"blur",message:"应用名不能为空"}]},listChangeCallback:null,showDialog:!1,isLoading:!1}},computed:{},created:function(){},methods:{resetForm:function(){this.form={app:null,enable:!0,expiresAt:null,remark:null}},openDialog:function(e,t){this.resetForm(),this.userId=e,this.listChangeCallback=t,this.showDialog=!0},onSubmit:function(){var e=this;this.$refs.formRef.validate((function(t){t&&e.$store.dispatch("userApiKeys/add",{userId:e.userId,app:e.form.app,enable:e.form.enable,expiresAt:e.form.expiresAt,remark:e.form.remark}).then((function(t){e.$message({showClose:!0,message:"添加成功",type:"success"}),e.showDialog=!1,e.listChangeCallback&&e.listChangeCallback()})).catch((function(e){console.error(e)}))}))},close:function(){this.showDialog=!1}}},P=C,L=Object(u["a"])(P,$,D,!1,null,null,null),z=L.exports,S=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{id:"remarkUserApiKey"}},[s("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"ApiKey备注",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s("div",{staticStyle:{"margin-right":"20px"},attrs:{id:"shared"}},[s("el-form",{ref:"form",attrs:{rules:e.rules,"status-icon":"","label-width":"80px"}},[s("el-form-item",{attrs:{label:"备注",prop:"oldPassword"}},[s("el-input",{attrs:{type:"textarea",autocomplete:"off",autosize:{minRows:5},maxlength:"255","show-word-limit":""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),s("el-form-item",[s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)},_=[],T={name:"RemarkUserApiKey",directives:{elDragDialog:a["a"]},props:{},data:function(){return{userApiKeyId:null,form:{remark:null},rules:{},listChangeCallback:null,showDialog:!1,isLoading:!1}},computed:{},created:function(){},methods:{resetForm:function(){this.form={remark:null}},openDialog:function(e,t){this.resetForm(),this.userApiKeyId=e,this.listChangeCallback=t,this.showDialog=!0},onSubmit:function(){var e=this;this.$store.dispatch("userApiKeys/remark",{id:this.userApiKeyId,remark:this.form.remark}).then((function(t){e.$message({showClose:!0,message:"备注修改成功!",type:"success"}),e.listChangeCallback()})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.showDialog=!1}))},close:function(){this.showDialog=!1}}},I=T,F=Object(u["a"])(I,S,_,!1,null,null,null),M=F.exports,H=s("c1df"),E=s.n(H),N={name:"UserApiKeyManager",components:{addUserApiKey:z,remarkUserApiKey:M},data:function(){return{userList:[],currentUser:{},winHeight:window.innerHeight-300,currentPage:1,count:15,total:0,getUserApiKeyListLoading:!1,showDialog:!1}},mounted:function(){},methods:{openDialog:function(e){this.userId=e,this.showDialog=!0,this.initData()},initData:function(){this.getUserApiKeyList()},currentChange:function(e){this.currentPage=e,this.getUserApiKeyList()},handleSizeChange:function(e){this.count=e,this.getUserApiKeyList()},getUserApiKeyList:function(){var e=this;this.getUserApiKeyListLoading=!0,this.$store.dispatch("userApiKeys/queryList",{page:this.currentPage,count:this.count}).then((function(t){e.total=t.total,e.userList=t.list})).finally((function(){e.getUserApiKeyListLoading=!1}))},addUserApiKey:function(){var e=this;this.$refs.addUserApiKey.openDialog(this.userId,(function(){e.$refs.addUserApiKey.close(),e.$message({showClose:!0,message:"ApiKey添加成功",type:"success"}),setTimeout(e.getUserApiKeyList,200)}))},remarkUserApiKey:function(e){var t=this;this.$refs.remarkUserApiKey.openDialog(e.id,(function(){t.$refs.remarkUserApiKey.close(),t.$message({showClose:!0,message:"备注修改成功",type:"success"}),setTimeout(t.getUserApiKeyList,200)}))},enableUserApiKey:function(e){var t=this,s="确定启用此ApiKey？";0!==e.online&&(s="<strong>确定启用此ApiKey？</strong>"),this.$confirm(s,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("userApiKeys/enable",e.id).then((function(){t.$message({showClose:!0,message:"启用成功",type:"success"}),t.getUserApiKeyList()})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})}))})).catch((function(){}))},disableUserApiKey:function(e){var t=this,s="确定停用此ApiKey？";0!==e.online&&(s="<strong>确定停用此ApiKey？</strong>"),this.$confirm(s,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("userApiKeys/disable",e.id).then((function(){t.$message({showClose:!0,message:"停用成功",type:"success"}),t.getUserApiKeyList()})).catch((function(e){t.$message({showClose:!0,message:"停用失败",type:"error"}),console.error(e)}))})).catch((function(){}))},resetUserApiKey:function(e){var t=this,s="确定重置此ApiKey？";0!==e.online&&(s="<strong>确定重置此ApiKey？</strong>"),this.$confirm(s,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("userApiKeys/reset",e.id).then((function(){t.$message({showClose:!0,message:"重置成功",type:"success"}),t.getUserApiKeyList()})).catch((function(e){t.$message({showClose:!0,message:"重置失败",type:"error"}),console.error(e)}))})).catch((function(){}))},deleteUserApiKey:function(e){var t=this,s="确定删除此ApiKey？";0!==e.online&&(s="<strong>确定删除此ApiKey？</strong>"),this.$confirm(s,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("userApiKeys/remove",e.id).then((function(){t.$message({showClose:!0,message:"删除成功",type:"success"}),t.getUserApiKeyList()})).catch((function(e){t.$message({showClose:!0,message:"删除失败",type:"error"}),console.error(e)}))})).catch((function(){}))},close:function(){this.showDialog=!1},formatTime:function(e){return E()(e).format("YYYY-MM-DD HH:mm:ss")}}},q=N,B=Object(u["a"])(q,A,U,!1,null,null,null),O=B.exports,R={name:"User",components:{changePasswordForAdmin:m,changePushKey:y,addUser:x,apiKeyManager:O},data:function(){return{userList:[],currentUser:{},videoComponentList:[],currentUserLenth:0,currentPage:1,count:15,total:0,getUserListLoading:!1}},mounted:function(){this.initData()},destroyed:function(){},methods:{initData:function(){this.getUserList()},currentChange:function(e){this.currentPage=e,this.getUserList()},handleSizeChange:function(e){this.count=e,this.getUserList()},getUserList:function(){var e=this;this.$store.dispatch("user/queryList",{page:this.currentPage,count:this.count}).then((function(t){e.total=t.total,e.userList=t.list})).catch((function(e){console.log(e)})).finally((function(){e.getUserListLoading=!1}))},edit:function(e){var t=this;this.$refs.changePasswordForAdmin.openDialog(e,(function(){t.$refs.changePasswordForAdmin.close(),t.$message({showClose:!0,message:"密码修改成功",type:"success"}),setTimeout(t.getUserList,200)}))},deleteUser:function(e){var t=this,s="确定删除此用户？";0!==e.online&&(s="<strong>确定删除此用户？</strong>"),this.$confirm(s,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("user/removeById",e.id).then((function(){t.getUserList()})).catch((function(e){console.error(e)}))})).catch((function(){}))},changePushKey:function(e){var t=this;this.$refs.changePushKey.openDialog(e,(function(){t.$refs.changePushKey.close(),t.$message({showClose:!0,message:"pushKey修改成功",type:"success"}),setTimeout(t.getUserList,200)}))},addUser:function(){var e=this;this.$refs.addUser.openDialog((function(){e.$refs.addUser.close(),e.$message({showClose:!0,message:"用户添加成功",type:"success"}),setTimeout(e.getUserList,200)}))},showUserApiKeyManager:function(e){this.$refs.apiKeyManager.openDialog(e.id)}}},Y=R,W=Object(u["a"])(Y,i,o,!1,null,null,null);t["default"]=W.exports}}]);