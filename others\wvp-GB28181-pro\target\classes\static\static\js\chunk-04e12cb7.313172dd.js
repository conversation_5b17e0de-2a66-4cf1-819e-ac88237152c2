(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-04e12cb7","chunk-b3c5ace6","chunk-3b4a2238"],{"0328":function(e,t,i){"use strict";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"devicePlayer"}},[e.showVideoDialog?i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"视频播放",top:"0","close-on-click-modal":!1,visible:e.showVideoDialog},on:{"update:visible":function(t){e.showVideoDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{width:"100%",height:"100%"}},[Object.keys(this.player).length>1?i("el-tabs",{attrs:{type:"card",stretch:!0},on:{"tab-click":e.changePlayer},model:{value:e.activePlayer,callback:function(t){e.activePlayer=t},expression:"activePlayer"}},[i("el-tab-pane",{attrs:{label:"Jessibuca",name:"jessibuca"}},["jessibuca"===e.activePlayer?i("jessibucaPlayer",{ref:"jessibuca",attrs:{visible:e.showVideoDialog,"video-url":e.videoUrl,error:e.videoError,message:e.videoError,"has-audio":e.hasAudio,fluent:"",autoplay:"",live:""},on:{"update:visible":function(t){e.showVideoDialog=t}}}):e._e()],1),i("el-tab-pane",{attrs:{label:"WebRTC",name:"webRTC"}},["webRTC"===e.activePlayer?i("rtc-player",{ref:"webRTC",attrs:{visible:e.showVideoDialog,"video-url":e.videoUrl,error:e.videoError,message:e.videoError,height:"100px","has-audio":e.hasAudio,fluent:"",autoplay:"",live:""},on:{"update:visible":function(t){e.showVideoDialog=t}}}):e._e()],1),i("el-tab-pane",{attrs:{label:"h265web",name:"h265web"}},["h265web"===e.activePlayer?i("h265web",{ref:"h265web",attrs:{"video-url":e.videoUrl,error:e.videoError,message:e.videoError,"has-audio":e.hasAudio,fluent:"",autoplay:"",live:"","show-button":!0}}):e._e()],1)],1):e._e(),1==Object.keys(this.player).length&&this.player.jessibuca?i("jessibucaPlayer",{ref:"jessibuca",attrs:{visible:e.showVideoDialog,"video-url":e.videoUrl,error:e.videoError,message:e.videoError,"has-audio":e.hasAudio,fluent:"",autoplay:"",live:""},on:{"update:visible":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.webRTC?i("rtc-player",{ref:"jessibuca",attrs:{visible:e.showVideoDialog,"video-url":e.videoUrl,error:e.videoError,message:e.videoError,height:"100px","has-audio":e.hasAudio,fluent:"",autoplay:"",live:""},on:{"update:visible":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.h265web?i("h265web",{ref:"jessibuca",attrs:{visible:e.showVideoDialog,"video-url":e.videoUrl,error:e.videoError,message:e.videoError,height:"100px","has-audio":e.hasAudio,fluent:"",autoplay:"",live:""},on:{"update:visible":function(t){e.showVideoDialog=t}}}):e._e()],1),i("div",{staticStyle:{"text-align":"right","margin-top":"1rem"},attrs:{id:"shared"}},[i("el-tabs",{on:{"tab-click":e.tabHandleClick},model:{value:e.tabActiveName,callback:function(t){e.tabActiveName=t},expression:"tabActiveName"}},[i("el-tab-pane",{attrs:{label:"实时视频",name:"media"}},[i("div",{staticStyle:{display:"flex","margin-bottom":"0.5rem",height:"2.5rem"}},[i("span",{staticStyle:{width:"5rem","line-height":"2.5rem","text-align":"right"}},[e._v("播放地址：")]),i("el-input",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedUrl,callback:function(t){e.$set(e.getPlayerShared,"sharedUrl",t)},expression:"getPlayerShared.sharedUrl"}},[i("template",{slot:"append"},[i("i",{staticClass:"cpoy-btn el-icon-document-copy",staticStyle:{cursor:"pointer"},attrs:{title:"点击拷贝"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedUrl)}}})])],2)],1),i("div",{staticStyle:{display:"flex","margin-bottom":"0.5rem",height:"2.5rem"}},[i("span",{staticStyle:{width:"5rem","line-height":"2.5rem","text-align":"right"}},[e._v("iframe：")]),i("el-input",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedIframe,callback:function(t){e.$set(e.getPlayerShared,"sharedIframe",t)},expression:"getPlayerShared.sharedIframe"}},[i("template",{slot:"append"},[i("i",{staticClass:"cpoy-btn el-icon-document-copy",staticStyle:{cursor:"pointer"},attrs:{title:"点击拷贝"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}}})])],2)],1),i("div",{staticStyle:{display:"flex","margin-bottom":"0.5rem",height:"2.5rem"}},[i("span",{staticStyle:{width:"5rem","line-height":"2.5rem","text-align":"right"}},[e._v("资源地址：")]),i("el-input",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedRtmp,callback:function(t){e.$set(e.getPlayerShared,"sharedRtmp",t)},expression:"getPlayerShared.sharedRtmp"}},[i("el-button",{staticStyle:{cursor:"pointer"},attrs:{slot:"append",icon:"el-icon-document-copy",title:"点击拷贝"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}},slot:"append"}),e.streamInfo?i("el-dropdown",{attrs:{slot:"prepend",trigger:"click"},on:{command:e.copyUrl},slot:"prepend"},[i("el-button",[e._v(" 更多地址"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",[e.streamInfo.flv?i("el-dropdown-item",{attrs:{command:e.streamInfo.flv}},[i("el-tag",[e._v("FLV:")]),i("span",[e._v(e._s(e.streamInfo.flv))])],1):e._e(),e.streamInfo.https_flv?i("el-dropdown-item",{attrs:{command:e.streamInfo.https_flv}},[i("el-tag",[e._v("FLV(https):")]),i("span",[e._v(e._s(e.streamInfo.https_flv))])],1):e._e(),e.streamInfo.ws_flv?i("el-dropdown-item",{attrs:{command:e.streamInfo.ws_flv}},[i("el-tag",[e._v("FLV(ws):")]),i("span",[e._v(e._s(e.streamInfo.ws_flv))])],1):e._e(),e.streamInfo.wss_flv?i("el-dropdown-item",{attrs:{command:e.streamInfo.wss_flv}},[i("el-tag",[e._v("FLV(wss):")]),i("span",[e._v(e._s(e.streamInfo.wss_flv))])],1):e._e(),e.streamInfo.fmp4?i("el-dropdown-item",{attrs:{command:e.streamInfo.fmp4}},[i("el-tag",[e._v("FMP4:")]),i("span",[e._v(e._s(e.streamInfo.fmp4))])],1):e._e(),e.streamInfo.https_fmp4?i("el-dropdown-item",{attrs:{command:e.streamInfo.https_fmp4}},[i("el-tag",[e._v("FMP4(https):")]),i("span",[e._v(e._s(e.streamInfo.https_fmp4))])],1):e._e(),e.streamInfo.ws_fmp4?i("el-dropdown-item",{attrs:{command:e.streamInfo.ws_fmp4}},[i("el-tag",[e._v("FMP4(ws):")]),i("span",[e._v(e._s(e.streamInfo.ws_fmp4))])],1):e._e(),e.streamInfo.wss_fmp4?i("el-dropdown-item",{attrs:{command:e.streamInfo.wss_fmp4}},[i("el-tag",[e._v("FMP4(wss):")]),i("span",[e._v(e._s(e.streamInfo.wss_fmp4))])],1):e._e(),e.streamInfo.hls?i("el-dropdown-item",{attrs:{command:e.streamInfo.hls}},[i("el-tag",[e._v("HLS:")]),i("span",[e._v(e._s(e.streamInfo.hls))])],1):e._e(),e.streamInfo.https_hls?i("el-dropdown-item",{attrs:{command:e.streamInfo.https_hls}},[i("el-tag",[e._v("HLS(https):")]),i("span",[e._v(e._s(e.streamInfo.https_hls))])],1):e._e(),e.streamInfo.ws_hls?i("el-dropdown-item",{attrs:{command:e.streamInfo.ws_hls}},[i("el-tag",[e._v("HLS(ws):")]),i("span",[e._v(e._s(e.streamInfo.ws_hls))])],1):e._e(),e.streamInfo.wss_hls?i("el-dropdown-item",{attrs:{command:e.streamInfo.wss_hls}},[i("el-tag",[e._v("HLS(wss):")]),i("span",[e._v(e._s(e.streamInfo.wss_hls))])],1):e._e(),e.streamInfo.ts?i("el-dropdown-item",{attrs:{command:e.streamInfo.ts}},[i("el-tag",[e._v("TS:")]),i("span",[e._v(e._s(e.streamInfo.ts))])],1):e._e(),e.streamInfo.https_ts?i("el-dropdown-item",{attrs:{command:e.streamInfo.https_ts}},[i("el-tag",[e._v("TS(https):")]),i("span",[e._v(e._s(e.streamInfo.https_ts))])],1):e._e(),e.streamInfo.ws_ts?i("el-dropdown-item",{attrs:{command:e.streamInfo.ws_ts}},[i("el-tag",[e._v("TS(ws):")]),i("span",[e._v(e._s(e.streamInfo.ws_ts))])],1):e._e(),e.streamInfo.wss_ts?i("el-dropdown-item",{attrs:{command:e.streamInfo.wss_ts}},[i("el-tag",[e._v("TS(wss):")]),i("span",[e._v(e._s(e.streamInfo.wss_ts))])],1):e._e(),e.streamInfo.rtc?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtc}},[i("el-tag",[e._v("RTC:")]),i("span",[e._v(e._s(e.streamInfo.rtc))])],1):e._e(),e.streamInfo.rtcs?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtcs}},[i("el-tag",[e._v("RTCS:")]),i("span",[e._v(e._s(e.streamInfo.rtcs))])],1):e._e(),e.streamInfo.rtmp?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtmp}},[i("el-tag",[e._v("RTMP:")]),i("span",[e._v(e._s(e.streamInfo.rtmp))])],1):e._e(),e.streamInfo.rtmps?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtmps}},[i("el-tag",[e._v("RTMPS:")]),i("span",[e._v(e._s(e.streamInfo.rtmps))])],1):e._e(),e.streamInfo.rtsp?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtsp}},[i("el-tag",[e._v("RTSP:")]),i("span",[e._v(e._s(e.streamInfo.rtsp))])],1):e._e(),e.streamInfo.rtsps?i("el-dropdown-item",{attrs:{command:e.streamInfo.rtsps}},[i("el-tag",[e._v("RTSPS:")]),i("span",[e._v(e._s(e.streamInfo.rtsps))])],1):e._e()],1)],1):e._e()],1)],1)]),e.showPtz?i("el-tab-pane",{attrs:{label:"云台控制",name:"control"}},[i("div",{staticStyle:{display:"grid","grid-template-columns":"240px auto",height:"180px",overflow:"auto"}},[i("div",{staticStyle:{display:"grid","grid-template-columns":"6.25rem auto"}},[i("div",{staticClass:"control-wrapper"},[i("div",{staticClass:"control-btn control-top",on:{mousedown:function(t){return e.ptzCamera("up")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-caret-top"}),i("div",{staticClass:"control-inner-btn control-inner"})]),i("div",{staticClass:"control-btn control-left",on:{mousedown:function(t){return e.ptzCamera("left")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-caret-left"}),i("div",{staticClass:"control-inner-btn control-inner"})]),i("div",{staticClass:"control-btn control-bottom",on:{mousedown:function(t){return e.ptzCamera("down")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-caret-bottom"}),i("div",{staticClass:"control-inner-btn control-inner"})]),i("div",{staticClass:"control-btn control-right",on:{mousedown:function(t){return e.ptzCamera("right")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-caret-right"}),i("div",{staticClass:"control-inner-btn control-inner"})]),i("div",{staticClass:"control-round"},[i("div",{staticClass:"control-round-inner"},[i("i",{staticClass:"fa fa-pause-circle"})])]),i("div",{staticClass:"contro-speed",staticStyle:{position:"absolute",left:"4px",top:"7rem",width:"6.25rem"}},[i("el-slider",{attrs:{max:100},model:{value:e.controSpeed,callback:function(t){e.controSpeed=t},expression:"controSpeed"}})],1)]),i("div",[i("div",{staticClass:"ptz-btn-box"},[i("div",{attrs:{title:"变倍+"},on:{mousedown:function(t){return e.ptzCamera("zoomin")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-zoom-in control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})]),i("div",{attrs:{title:"变倍-"},on:{mousedown:function(t){return e.ptzCamera("zoomout")},mouseup:function(t){return e.ptzCamera("stop")}}},[i("i",{staticClass:"el-icon-zoom-out control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})])]),i("div",{staticClass:"ptz-btn-box"},[i("div",{attrs:{title:"聚焦+"},on:{mousedown:function(t){return e.focusCamera("near")},mouseup:function(t){return e.focusCamera("stop")}}},[i("i",{staticClass:"iconfont icon-bianjiao-fangda control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})]),i("div",{attrs:{title:"聚焦-"},on:{mousedown:function(t){return e.focusCamera("far")},mouseup:function(t){return e.focusCamera("stop")}}},[i("i",{staticClass:"iconfont icon-bianjiao-suoxiao control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})])]),i("div",{staticClass:"ptz-btn-box"},[i("div",{attrs:{title:"光圈+"},on:{mousedown:function(t){return e.irisCamera("in")},mouseup:function(t){return e.irisCamera("stop")}}},[i("i",{staticClass:"iconfont icon-guangquan control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})]),i("div",{attrs:{title:"光圈-"},on:{mousedown:function(t){return e.irisCamera("out")},mouseup:function(t){return e.irisCamera("stop")}}},[i("i",{staticClass:"iconfont icon-guangquan- control-zoom-btn",staticStyle:{"font-size":"1.5rem"}})])])])]),"control"===e.tabActiveName?i("div",{staticStyle:{"text-align":"left"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{size:"mini",placeholder:"请选择云台功能"},model:{value:e.ptzMethod,callback:function(t){e.ptzMethod=t},expression:"ptzMethod"}},[i("el-option",{attrs:{label:"预置点",value:"preset"}}),i("el-option",{attrs:{label:"巡航组",value:"cruise"}}),i("el-option",{attrs:{label:"自动扫描",value:"scan"}}),i("el-option",{attrs:{label:"雨刷",value:"wiper"}}),i("el-option",{attrs:{label:"辅助开关",value:"switch"}})],1),"preset"===e.ptzMethod?i("ptzPreset",{staticStyle:{"margin-top":"1rem"},attrs:{"channel-device-id":e.channelId,"device-id":e.deviceId}}):e._e(),"cruise"===e.ptzMethod?i("ptzCruising",{staticStyle:{"margin-top":"1rem"},attrs:{"channel-device-id":e.channelId,"device-id":e.deviceId}}):e._e(),"scan"===e.ptzMethod?i("ptzScan",{staticStyle:{"margin-top":"1rem"},attrs:{"channel-device-id":e.channelId,"device-id":e.deviceId}}):e._e(),"wiper"===e.ptzMethod?i("ptzWiper",{staticStyle:{"margin-top":"1rem"},attrs:{"channel-device-id":e.channelId,"device-id":e.deviceId}}):e._e(),"switch"===e.ptzMethod?i("ptzSwitch",{staticStyle:{"margin-top":"1rem"},attrs:{"channel-device-id":e.channelId,"device-id":e.deviceId}}):e._e()],1):e._e()])]):e._e(),i("el-tab-pane",{attrs:{label:"编码信息",name:"codec"}},[i("mediaInfo",{ref:"mediaInfo",attrs:{app:e.app,stream:e.streamId,"media-server-id":e.mediaServerId}})],1),e.showBroadcast?i("el-tab-pane",{attrs:{label:"语音对讲",name:"broadcast"}},[i("div",{staticStyle:{padding:"0 10px"}},[i("el-radio-group",{attrs:{disabled:-1!==e.broadcastStatus},model:{value:e.broadcastMode,callback:function(t){e.broadcastMode=t},expression:"broadcastMode"}},[i("el-radio",{attrs:{label:!0}},[e._v("喊话(Broadcast)")]),i("el-radio",{attrs:{label:!1}},[e._v("对讲(Talk)")])],1)],1),i("div",{staticClass:"trank",staticStyle:{"text-align":"center"}},[i("el-button",{staticStyle:{"font-size":"32px",padding:"24px","margin-top":"24px"},attrs:{type:e.getBroadcastStatus(),disabled:-2===e.broadcastStatus,circle:"",icon:"el-icon-microphone"},on:{click:function(t){return e.broadcastStatusClick()}}}),i("p",[-2===e.broadcastStatus?i("span",[e._v("正在释放资源")]):e._e(),-1===e.broadcastStatus?i("span",[e._v("点击开始对讲")]):e._e(),0===e.broadcastStatus?i("span",[e._v("等待接通中...")]):e._e(),1===e.broadcastStatus?i("span",[e._v("请说话")]):e._e()])],1)]):e._e()],1)],1)]):e._e()],1)},o=[],n=(i("caad"),i("b0c0"),i("e9c4"),i("b64b"),i("2532"),i("a888")),a=i("1c46"),r=i.n(a),l=i("bbf2"),c=i("2655"),d=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{width:"100%"},attrs:{id:"ptzPreset"}},[e._l(e.presetList,(function(t){return i("el-tag",{key:t.presetId,staticStyle:{"margin-right":"1rem",cursor:"pointer","margin-bottom":"0.6rem"},attrs:{closable:"",size:"mini"},on:{close:function(i){return e.delPreset(t)},click:function(i){return e.gotoPreset(t)}}},[e._v(" "+e._s(t.presetName?t.presetName:t.presetId)+" ")])})),e.inputVisible?i("el-input",{ref:"saveTagInput",staticStyle:{width:"300px","vertical-align":"bottom"},attrs:{min:"1",max:"255",placeholder:"预置位编号","addon-before":"预置位编号","addon-after":"(1-255)",size:"small"},scopedSlots:e._u([{key:"append",fn:function(){return[i("el-button",{on:{click:function(t){return e.addPreset()}}},[e._v("保存")]),i("el-button",{on:{click:function(t){return e.cancel()}}},[e._v("取消")])]},proxy:!0}],null,!1,3889715118),model:{value:e.ptzPresetId,callback:function(t){e.ptzPresetId=t},expression:"ptzPresetId"}}):i("el-button",{attrs:{size:"small"},on:{click:e.showInput}},[e._v("+ 添加")])],2)},u=[],m=(i("d3b7"),{name:"PtzPreset",components:{},props:["channelDeviceId","deviceId"],data:function(){return{presetList:[],inputVisible:!1,ptzPresetId:""}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch("frontEnd/queryPreset",[this.deviceId,this.channelDeviceId]).then((function(t){e.presetList=t,e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))})).catch((function(e){console.log(e)}))},showInput:function(){var e=this;this.inputVisible=!0,this.$nextTick((function(t){e.$refs.saveTagInput.$refs.input.focus()}))},addPreset:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/addPreset",[this.deviceId,this.channelDeviceId,this.ptzPresetId]).then((function(t){setTimeout((function(){e.inputVisible=!1,e.ptzPresetId="",e.getPresetList()}),1e3)})).catch((function(i){t.close(),e.inputVisible=!1,e.ptzPresetId="",e.$message({showClose:!0,message:i,type:"error"})})).finally((function(){t.close()}))},cancel:function(){this.inputVisible=!1,this.ptzPresetId=""},gotoPreset:function(e){var t=this;console.log(e),this.$store.dispatch("frontEnd/callPreset",[this.deviceId,this.channelDeviceId,e.presetId]).then((function(e){t.$message({showClose:!0,message:"调用成功",type:"success"})})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})}))},delPreset:function(e){var t=this;this.$confirm("确定删除此预置位","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var i=t.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});t.$store.dispatch("frontEnd/deletePreset",[t.deviceId,t.channelDeviceId,e.presetId]).then((function(e){setTimeout((function(){t.getPresetList()}),1e3)})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})})).finally((function(){i.close()}))})).catch((function(){}))}}}),h=m,p=i("2877"),f=Object(p["a"])(h,d,u,!1,null,null,null),v=f.exports,b=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"ptzCruising"}},[i("div",{staticStyle:{display:"grid","grid-template-columns":"80px auto","line-height":"28px"}},[i("span",[e._v("巡航组号: ")]),i("el-input",{attrs:{min:"1",max:"255",placeholder:"巡航组号","addon-before":"巡航组号","addon-after":"(1-255)",size:"mini"},model:{value:e.cruiseId,callback:function(t){e.cruiseId=t},expression:"cruiseId"}})],1),i("p",e._l(e.presetList,(function(t,s){return i("el-tag",{key:t.presetId,staticStyle:{"margin-right":"1rem",cursor:"pointer"},attrs:{closable:""},on:{close:function(i){return e.delPreset(t,s)}}},[e._v(" "+e._s(t.presetName?t.presetName:t.presetId)+" ")])})),1),e.selectPresetVisible?i("el-form",{attrs:{size:"mini",inline:!0}},[i("el-form-item",[i("el-select",{attrs:{placeholder:"请选择预置点"},model:{value:e.selectPreset,callback:function(t){e.selectPreset=t},expression:"selectPreset"}},e._l(e.allPresetList,(function(e){return i("el-option",{key:e.presetId,attrs:{label:e.presetName,value:e}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:e.addCruisePoint}},[e._v("保存")]),i("el-button",{attrs:{type:"primary"},on:{click:e.cancelAddCruisePoint}},[e._v("取消")])],1)],1):i("el-button",{attrs:{size:"mini"},on:{click:function(t){e.selectPresetVisible=!0}}},[e._v("添加巡航点")]),e.setSpeedVisible?i("el-form",{attrs:{size:"mini",inline:!0}},[i("el-form-item",[e.setSpeedVisible?i("el-input",{attrs:{min:"1",max:"4095",placeholder:"巡航速度","addon-before":"巡航速度","addon-after":"(1-4095)",size:"mini"},model:{value:e.cruiseSpeed,callback:function(t){e.cruiseSpeed=t},expression:"cruiseSpeed"}}):e._e()],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:e.setCruiseSpeed}},[e._v("保存")]),i("el-button",{on:{click:e.cancelSetCruiseSpeed}},[e._v("取消")])],1)],1):i("el-button",{attrs:{size:"mini"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v("设置巡航速度")]),e.setTimeVisible?i("el-form",{attrs:{size:"mini",inline:!0}},[i("el-form-item",[i("el-input",{staticStyle:{width:"100%"},attrs:{min:"1",max:"4095",placeholder:"巡航停留时间(秒)","addon-before":"巡航停留时间(秒)","addon-after":"(1-4095)"},model:{value:e.cruiseTime,callback:function(t){e.cruiseTime=t},expression:"cruiseTime"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:e.setCruiseTime}},[e._v("保存")]),i("el-button",{on:{click:e.cancelSetCruiseTime}},[e._v("取消")])],1)],1):i("el-button",{attrs:{size:"mini"},on:{click:function(t){e.setTimeVisible=!0}}},[e._v("设置巡航时间")]),i("el-button",{attrs:{size:"mini"},on:{click:e.startCruise}},[e._v("开始巡航")]),i("el-button",{attrs:{size:"mini"},on:{click:e.stopCruise}},[e._v("停止巡航")]),i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:e.deleteCruise}},[e._v("删除巡航")])],1)},g=[],y=(i("a434"),{name:"PtzCruising",components:{},props:["channelDeviceId","deviceId"],data:function(){return{cruiseId:1,presetList:[],allPresetList:[],selectPreset:"",inputVisible:!1,selectPresetVisible:!1,setSpeedVisible:!1,setTimeVisible:!1,cruiseSpeed:"",cruiseTime:""}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch("frontEnd/queryPreset",[this.deviceId,this.channelDeviceId]).then((function(t){e.allPresetList=t}))},addCruisePoint:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/addPointForCruise",[this.deviceId,this.channelDeviceId,this.cruiseId,this.selectPreset.presetId]).then((function(t){e.presetList.push(e.selectPreset)})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.selectPreset="",e.selectPresetVisible=!1,t.close()}))},cancelAddCruisePoint:function(){this.selectPreset="",this.selectPresetVisible=!1},delPreset:function(e,t){var i=this,s=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/deletePointForCruise",[this.deviceId,this.channelDeviceId,this.cruiseId,e.presetId]).then((function(e){i.presetList.splice(t,1)})).catch((function(e){i.$message({showClose:!0,message:e,type:"error"})})).finally((function(){s.close()}))},deleteCruise:function(e,t){var i=this;this.$confirm("确定删除此巡航组","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=i.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});i.$store.dispatch("frontEnd/deletePointForCruise",[i.deviceId,i.channelDeviceId,i.cruiseId,0]).then((function(e){i.presetList=[]})).catch((function(e){i.$message({showClose:!0,message:e,type:"error"})})).finally((function(){e.close()}))}))},setCruiseSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/setCruiseSpeed",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseSpeed]).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.cruiseSpeed="",e.setSpeedVisible=!1,t.close()}))},cancelSetCruiseSpeed:function(){this.cruiseSpeed="",this.setSpeedVisible=!1},setCruiseTime:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/setCruiseTime",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseTime]).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime="",t.close()}))},cancelSetCruiseTime:function(){this.setTimeVisible=!1,this.cruiseTime=""},startCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/startCruise",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:"发送成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime="",t.close()}))},stopCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/stopCruise",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:"发送成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime="",t.close()}))}}}),w=y,I=(i("e8b4"),Object(p["a"])(w,b,g,!1,null,null,null)),S=I.exports,C=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"ptzScan"}},[i("div",{staticStyle:{display:"grid","grid-template-columns":"80px auto","line-height":"28px"}},[i("span",[e._v("扫描组号: ")]),i("el-input",{attrs:{min:"1",max:"255",placeholder:"扫描组号","addon-before":"扫描组号","addon-after":"(1-255)",size:"mini"},model:{value:e.scanId,callback:function(t){e.scanId=t},expression:"scanId"}})],1),i("el-button",{attrs:{size:"mini"},on:{click:e.setScanLeft}},[e._v("设置左边界")]),i("el-button",{attrs:{size:"mini"},on:{click:e.setScanRight}},[e._v("设置右边界")]),e.setSpeedVisible?i("el-form",{attrs:{size:"mini",inline:!0}},[i("el-form-item",[e.setSpeedVisible?i("el-input",{attrs:{min:"1",max:"4095",placeholder:"巡航速度","addon-before":"巡航速度","addon-after":"(1-4095)",size:"mini"},model:{value:e.speed,callback:function(t){e.speed=t},expression:"speed"}}):e._e()],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:e.setSpeed}},[e._v("保存")]),i("el-button",{on:{click:e.cancelSetSpeed}},[e._v("取消")])],1)],1):i("el-button",{attrs:{size:"mini"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v("设置扫描速度")]),i("el-button",{attrs:{size:"mini"},on:{click:e.startScan}},[e._v("开始自动扫描")]),i("el-button",{attrs:{size:"mini"},on:{click:e.stopScan}},[e._v("停止自动扫描")])],1)},_=[],k={name:"PtzScan",components:{},props:["channelDeviceId","deviceId"],data:function(){return{scanId:1,setSpeedVisible:!1,speed:""}},created:function(){},methods:{setSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/setSpeedForScan",[this.deviceId,this.channelDeviceId,this.scanId,this.speed]).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.speed="",e.setSpeedVisible=!1,t.close()}))},cancelSetSpeed:function(){this.speed="",this.setSpeedVisible=!1},setScanLeft:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/setLeftForScan",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.speed="",e.setSpeedVisible=!1,t.close()}))},setScanRight:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/setRightForScan",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){e.speed="",e.setSpeedVisible=!1,t.close()}))},startScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/startScan",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:"发送成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){t.close()}))},stopScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/stopScan",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:"发送成功",type:"success"})})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})).finally((function(){t.close()}))}}},$=k,x=(i("fdc8"),Object(p["a"])($,C,_,!1,null,null,null)),P=x.exports,T=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"ptzWiper"}},[i("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.open("on")}}},[e._v("开启")]),i("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.open("off")}}},[e._v("关闭")])],1)},D=[],E={name:"PtzWiper",components:{},props:["channelDeviceId","deviceId"],data:function(){return{}},created:function(){},methods:{open:function(e){var t=this,i=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/wiper",[this.deviceId,this.channelDeviceId,e]).then((function(e){t.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})})).finally((function(){i.close()}))}}},z=E,L=(i("a135"),Object(p["a"])(z,T,D,!1,null,null,null)),R=L.exports,M=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"ptzScan"}},[i("el-form",{attrs:{size:"mini",inline:!0}},[i("el-form-item",[i("el-input",{attrs:{min:"1",max:"4095",placeholder:"开关编号","addon-before":"开关编号","addon-after":"(2-255)",size:"mini"},model:{value:e.switchId,callback:function(t){e.switchId=t},expression:"switchId"}})],1),i("el-form-item",[i("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.open("on")}}},[e._v("开启")]),i("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.open("off")}}},[e._v("关闭")])],1)],1)],1)},A=[],B={name:"PtzScan",components:{},props:["channelDeviceId","deviceId"],data:function(){return{switchId:1}},created:function(){},methods:{open:function(e){var t=this,i=this.$loading({lock:!0,fullscreen:!0,text:"正在发送指令",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$store.dispatch("frontEnd/auxiliary",[this.deviceId,this.channelDeviceId,e,this.switchId]).then((function(e){t.$message({showClose:!0,message:"保存成功",type:"success"})})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})})).finally((function(){i.close()}))}}},N=B,F=(i("34c9"),Object(p["a"])(N,M,A,!1,null,null,null)),U=F.exports,V=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"mediaInfo"}},[i("el-button",{staticStyle:{position:"absolute",right:"1rem"},attrs:{icon:"el-icon-refresh-right",circle:"",size:"mini"},on:{click:e.getMediaInfo}}),i("el-descriptions",{attrs:{size:"mini",column:3,title:"概况"}},[i("el-descriptions-item",{attrs:{label:"观看人数"}},[e._v(e._s(e.info.readerCount))]),i("el-descriptions-item",{attrs:{label:"网络"}},[e._v(e._s(e.formatByteSpeed()))]),i("el-descriptions-item",{attrs:{label:"持续时间"}},[e._v(e._s(e.info.aliveSecond)+"秒")])],1),i("div",{staticStyle:{display:"grid","grid-template-columns":"1fr 1fr"}},[e.info.videoCodec?i("el-descriptions",{attrs:{size:"mini",column:2,title:"视频信息"}},[i("el-descriptions-item",{attrs:{label:"编码"}},[e._v(e._s(e.info.videoCodec))]),i("el-descriptions-item",{attrs:{label:"分辨率"}},[e._v(e._s(e.info.width)+"x"+e._s(e.info.height)+" ")]),i("el-descriptions-item",{attrs:{label:"FPS"}},[e._v(e._s(e.info.fps))]),i("el-descriptions-item",{attrs:{label:"丢包率"}},[e._v(e._s(e.info.loss))])],1):e._e(),e.info.audioCodec?i("el-descriptions",{attrs:{size:"mini",column:2,title:"音频信息"}},[i("el-descriptions-item",{attrs:{label:"编码"}},[e._v(" "+e._s(e.info.audioCodec)+" ")]),i("el-descriptions-item",{attrs:{label:"采样率"}},[e._v(e._s(e.info.audioSampleRate))])],1):e._e()],1)],1)},j=[],O=(i("99af"),i("b680"),{name:"MediaInfo",components:{},props:["app","stream","mediaServerId"],data:function(){return{info:{},task:null}},created:function(){this.getMediaInfo()},methods:{getMediaInfo:function(){var e=this;this.$store.dispatch("server/getMediaInfo",{app:this.app,stream:this.stream,mediaServerId:this.mediaServerId}).then((function(t){e.info=t}))},startTask:function(){this.task=setInterval(this.getMediaInfo,1e3)},stopTask:function(){this.task&&(window.clearInterval(this.task),this.task=null)},formatByteSpeed:function(){var e=this.info.bytesSpeed,t=1024;return e<t?e+" B/S":e<Math.pow(t,2)?(e/t).toFixed(2)+" KB/S":e<Math.pow(t,3)?(e/Math.pow(t,2)).toFixed(2)+" MB/S":e<Math.pow(t,4)?(e/Math.pow(t,3)).toFixed(2)+" G/S":(e/Math.pow(t,4)).toFixed(2)+" T/S"},formatAliveSecond:function(){var e=this.info.aliveSecond,t=parseInt(e.value/3600),i=parseInt(e.value/60%60),s=Math.ceil(e.value%60),o=t<10?"0"+t:t,n=s>59?59:s;return"".concat(o>0?"".concat(o,"小时"):"").concat(i<10?"0"+i:i,"分").concat(n<10?"0"+n:n,"秒")}}}),G=O,W=(i("5869"),Object(p["a"])(G,V,j,!1,null,null,null)),q=W.exports,H=i("4f91"),J={name:"DevicePlayer",directives:{elDragDialog:n["a"]},components:{H265web:H["a"],PtzPreset:v,PtzCruising:S,ptzScan:P,ptzWiper:R,ptzSwitch:U,mediaInfo:q,jessibucaPlayer:c["default"],rtcPlayer:l["default"]},props:{},data:function(){return{video:"http://lndxyj.iqilu.com/public/upload/2019/10/14/8c001ea0c09cdc59a57829dabc8010fa.mp4",videoUrl:"",activePlayer:"jessibuca",player:{jessibuca:["ws_flv","wss_flv"],webRTC:["rtc","rtcs"],h265web:["ws_flv","wss_flv"]},showVideoDialog:!1,streamId:"",ptzMethod:"preset",ptzPresetId:"",app:"",mediaServerId:"",deviceId:"",channelId:"",tabActiveName:"media",hasAudio:!1,loadingRecords:!1,recordsLoading:!1,isLoging:!1,controSpeed:30,timeVal:0,timeMin:0,timeMax:1440,presetPos:1,cruisingSpeed:100,cruisingTime:5,cruisingGroup:0,scanSpeed:100,scanGroup:0,tracks:[],showPtz:!0,showBroadcast:!0,showRrecord:!0,sliderTime:0,seekTime:0,recordStartTime:0,showTimeText:"00:00:00",streamInfo:null,broadcastMode:!0,broadcastRtc:null,broadcastStatus:-1}},computed:{getPlayerShared:function(){return{sharedUrl:window.location.origin+"/#/play/wasm/"+encodeURIComponent(this.videoUrl),sharedIframe:'<iframe src="'+window.location.origin+"/#/play/wasm/"+encodeURIComponent(this.videoUrl)+'"></iframe>',sharedRtmp:this.videoUrl}}},created:function(){console.log("created"),console.log(this.player),this.broadcastStatus=-1,1===Object.keys(this.player).length&&(this.activePlayer=Object.keys(this.player)[0])},methods:{tabHandleClick:function(e,t){console.log(e),this.tracks=[],"codec"===e.name?this.$refs.mediaInfo.startTask():this.$refs.mediaInfo.stopTask()},changePlayer:function(e){console.log(this.player[e.name][0]),this.activePlayer=e.name,this.videoUrl=this.getUrlByStreamInfo(),console.log(this.videoUrl)},openDialog:function(e,t,i,s){if(!this.showVideoDialog)switch(this.tabActiveName=e,this.channelId=i,this.deviceId=t,this.streamId="",this.mediaServerId="",this.app="",this.videoUrl="",this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),e){case"media":this.play(s.streamInfo,s.hasAudio);break;case"streamPlay":this.tabActiveName="media",this.showRrecord=!1,this.showPtz=!1,this.showBroadcast=!1,this.play(s.streamInfo,s.hasAudio);break;case"control":break}},play:function(e,t){this.streamInfo=e,this.hasAudio=t,this.isLoging=!1,this.videoUrl=this.getUrlByStreamInfo(),this.streamId=e.stream,this.app=e.app,this.mediaServerId=e.mediaServerId,this.playFromStreamInfo(!1,e)},getUrlByStreamInfo:function(){console.log(this.streamInfo);var e=this.streamInfo;return this.streamInfo.transcodeStream&&(e=this.streamInfo.transcodeStream),"https:"===location.protocol?this.videoUrl=e[this.player[this.activePlayer][1]]:this.videoUrl=e[this.player[this.activePlayer][0]],this.videoUrl},playFromStreamInfo:function(e,t){var i=this;this.showVideoDialog=!0,this.hasaudio=e&&this.hasaudio,this.$refs[this.activePlayer]?this.$refs[this.activePlayer].play(this.getUrlByStreamInfo(t)):this.$nextTick((function(){i.$refs[i.activePlayer].play(i.getUrlByStreamInfo(t))}))},close:function(){console.log("关闭视频"),this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),this.videoUrl="",this.showVideoDialog=!1,this.stopBroadcast()},ptzCamera:function(e){console.log("云台控制："+e),this.$store.dispatch("frontEnd/ptz",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100),parseInt(255*this.controSpeed/100),parseInt(16*this.controSpeed/100)])},irisCamera:function(e){this.$store.dispatch("frontEnd/iris",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},focusCamera:function(e){this.$store.dispatch("frontEnd/focus",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},copyUrl:function(e){var t=this;console.log(e),this.$copyText(e).then((function(e){t.$message.success({showClose:!0,message:"成功拷贝到粘贴板"})}),(function(e){}))},getBroadcastStatus:function(){return-2==this.broadcastStatus||-1==this.broadcastStatus?"primary":0==this.broadcastStatus?"warning":1===this.broadcastStatus?"danger":void 0},broadcastStatusClick:function(){var e=this;-1===this.broadcastStatus?(this.broadcastStatus=0,this.$store.dispatch("play/broadcastStart",[this.deviceId,this.channelId,this.broadcastMode]).then((function(t){var i=t.streamInfo;document.location.protocol.includes("https")?e.startBroadcast(i.rtcs):e.startBroadcast(i.rtc)}))):1===this.broadcastStatus&&(this.broadcastStatus=-1,this.broadcastRtc.close())},startBroadcast:function(e){var t=this;this.$store.dispatch("user/getUserInfo").then((function(i){if(null!=i){var s=i.pushKey;e+="&sign="+r.a.createHash("md5").update(s,"utf8").digest("hex"),console.log("开始语音喊话： "+e),t.broadcastRtc=new ZLMRTCClient.Endpoint({debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!1,recvOnly:!1}),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT,(function(e){console.error("不支持webrtc",e),t.$message({showClose:!0,message:"不支持webrtc, 无法进行语音喊话",type:"error"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error("ICE 协商出错"),t.$message({showClose:!0,message:"ICE 协商出错",type:"error"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(e){console.error("offer anwser 交换失败",e),t.$message({showClose:!0,message:"offer anwser 交换失败"+e,type:"error"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,(function(e){console.log("状态改变",e),"connecting"===e?t.broadcastStatus=0:"connected"===e?t.broadcastStatus=1:"disconnected"===e&&(t.broadcastStatus=-1)})),t.broadcastRtc.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED,(function(e){console.log("捕获流失败",e),t.$message({showClose:!0,message:"捕获流失败"+e,type:"error"}),t.broadcastStatus=-1}))}else t.broadcastStatus=-1})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"}),t.broadcastStatus=-1}))},stopBroadcast:function(){this.broadcastRtc.close(),this.broadcastStatus=-1,this.$store.dispatch("play/broadcastStop",[this.deviceId,this.channelId])}}},K=J,Z=(i("c5f0"),Object(p["a"])(K,s,o,!1,null,null,null));t["a"]=Z.exports},"0868":function(e,t,i){},"0db8":function(e,t,i){"use strict";i("a4be")},1148:function(e,t,i){"use strict";var s=i("a691"),o=i("1d80");e.exports="".repeat||function(e){var t=String(o(this)),i="",n=s(e);if(n<0||n==1/0)throw RangeError("Wrong number of repetitions");for(;n>0;(n>>>=1)&&(t+=t))1&n&&(i+=t);return i}},"257e":function(e,t,i){"use strict";i("d017")},2655:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{ref:"container",staticStyle:{width:"100%",height:"100%","background-color":"#000000",margin:"0 auto",position:"relative"},on:{dblclick:e.fullscreenSwich}},[i("div",{staticStyle:{width:"100%","padding-top":"56.25%",position:"relative"}}),i("div",{staticClass:"buttons-box",attrs:{id:"buttonsBox"}},[i("div",{staticClass:"buttons-box-left"},[e.playing?e._e():i("i",{staticClass:"iconfont icon-play jessibuca-btn",on:{click:e.playBtnClick}}),e.playing?i("i",{staticClass:"iconfont icon-pause jessibuca-btn",on:{click:e.pause}}):e._e(),i("i",{staticClass:"iconfont icon-stop jessibuca-btn",on:{click:e.destroy}}),e.isNotMute?i("i",{staticClass:"iconfont icon-audio-high jessibuca-btn",on:{click:function(t){return e.mute()}}}):e._e(),e.isNotMute?e._e():i("i",{staticClass:"iconfont icon-audio-mute jessibuca-btn",on:{click:function(t){return e.cancelMute()}}})]),i("div",{staticClass:"buttons-box-right"},[i("span",{staticClass:"jessibuca-btn"},[e._v(e._s(e.kBps)+" kb/s")]),i("i",{staticClass:"iconfont icon-camera1196054easyiconnet jessibuca-btn",staticStyle:{"font-size":"1rem !important"},on:{click:e.screenshot}}),i("i",{staticClass:"iconfont icon-shuaxin11 jessibuca-btn",on:{click:e.playBtnClick}}),e.fullscreen?e._e():i("i",{staticClass:"iconfont icon-weibiaoti10 jessibuca-btn",on:{click:e.fullscreenSwich}}),e.fullscreen?i("i",{staticClass:"iconfont icon-weibiaoti11 jessibuca-btn",on:{click:e.fullscreenSwich}}):e._e()])])])},o=[],n=i("5530"),a={},r={name:"Jessibuca",props:["videoUrl","error","hasAudio","height"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1}},watch:{videoUrl:{handler:function(e,t){var i=this;this.$nextTick((function(){i.play(e)}))},immediate:!0}},created:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){e.updatePlayerDomSize(),window.onresize=e.updatePlayerDomSize,"undefined"===typeof e.videoUrl&&(e.videoUrl=t),e.btnDom=document.getElementById("buttonsBox")}))},mounted:function(){this.updatePlayerDomSize()},destroyed:function(){a[this._uid]&&a[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.performance=""},methods:{updatePlayerDomSize:function(){var e=this,t=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(t){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(t.parentNode));var i=t.parentNode.clientWidth,s=t.parentNode.clientHeight,o=i,n=9/16*o;s>0&&i>s/9*16&&(n=s,o=s/9*16);var r=Math.min(document.body.clientHeight,document.documentElement.clientHeight);n>r&&(n=r,o=16/9*n),this.playerWidth=o,this.playerHeight=n,this.playing&&a[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(){var e={container:this.$refs.container,autoWasm:!0,background:"",controlAutoHide:!1,debug:!1,decoder:"static/js/jessibuca/decoder.js",forceNoOffscreen:!1,hasAudio:"undefined"===typeof this.hasAudio||this.hasAudio,heartTimeout:5,heartTimeoutReplay:!0,heartTimeoutReplayTimes:3,hiddenAutoPause:!1,hotKey:!0,isFlv:!1,isFullResize:!1,isNotMute:this.isNotMute,isResize:!0,keepScreenOn:!0,loadingText:"请稍等, 视频加载中......",loadingTimeout:10,loadingTimeoutReplay:!0,loadingTimeoutReplayTimes:3,openWebglAlignment:!1,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},recordType:"mp4",rotate:0,showBandwidth:!1,supportDblclickFullscreen:!1,timeout:10,useMSE:!0,useWCS:!1,useWebFullScreen:!0,videoBuffer:.1,wasmDecodeErrorReplay:!0,wcsUseVideoRender:!0};console.log("Jessibuca -> options: ",e),a[this._uid]=new window.Jessibuca(Object(n["a"])({},e));var t=a[this._uid],i=this;t.on("pause",(function(){i.playing=!1})),t.on("play",(function(){i.playing=!0})),t.on("fullscreen",(function(e){i.fullscreen=e})),t.on("mute",(function(e){i.isNotMute=!e})),t.on("performance",(function(e){var t="卡顿";2===e?t="非常流畅":1===e&&(t="流畅"),i.performance=t})),t.on("kBps",(function(e){i.kBps=Math.round(e)})),t.on("videoInfo",(function(e){console.log("Jessibuca -> videoInfo: ",e)})),t.on("audioInfo",(function(e){console.log("Jessibuca -> audioInfo: ",e)})),t.on("error",(function(e){console.log("Jessibuca -> error: ",e)})),t.on("timeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("loadingTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("delayTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),t.on("playToRenderTimes",(function(e){console.log("Jessibuca -> playToRenderTimes: ",e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log("Jessibuca -> url: ",e),a[this._uid]&&this.destroy(),this.create(),a[this._uid].on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=jessibuca.quieting})),a[this._uid].hasLoaded()?a[this._uid].play(e):a[this._uid].on("load",(function(){a[t._uid].play(e)}))},pause:function(){a[this._uid]&&a[this._uid].pause(),this.playing=!1,this.err="",this.performance=""},screenshot:function(){a[this._uid]&&a[this._uid].screenshot()},mute:function(){a[this._uid]&&a[this._uid].mute()},cancelMute:function(){a[this._uid]&&a[this._uid].cancelMute()},destroy:function(){a[this._uid]&&a[this._uid].destroy(),null==document.getElementById("buttonsBox")&&this.$refs.container.appendChild(this.btnDom),a[this._uid]=null,this.playing=!1,this.err="",this.performance=""},fullscreenSwich:function(){var e=this.isFullscreen();a[this._uid].setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}}},l=r,c=(i("989a"),i("2877")),d=Object(c["a"])(l,s,o,!1,null,null,null);t["default"]=d.exports},"26d6":function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container",attrs:{id:"device"}},[i("deviceList",{directives:[{name:"show",rawName:"v-show",value:null===e.deviceId,expression:"deviceId === null"}],on:{"show-channel":e.showChannelList}}),null!==e.deviceId?i("channelList",{attrs:{"device-id":e.deviceId},on:{"show-device":e.showDevice}}):e._e()],1)},o=[],n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{height:"calc(100vh - 124px)"},attrs:{id:"app"}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.initData},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.initData},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",[i("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",type:"primary"},on:{click:e.add}},[e._v("添加设备")]),i("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-info"},on:{click:function(t){return e.showInfo()}}},[e._v("接入信息 ")])],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",loading:e.getDeviceListLoading},on:{click:function(t){return e.getDeviceList()}}})],1)],1),i("el-table",{attrs:{size:"small",data:e.deviceList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"160"}}),i("el-table-column",{attrs:{prop:"deviceId",label:"设备编号","min-width":"160"}}),i("el-table-column",{attrs:{label:"地址","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.hostAddress?i("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.transport.toLowerCase())+"://"+e._s(t.row.hostAddress))]):e._e(),t.row.hostAddress?e._e():i("el-tag",{attrs:{size:"medium"}},[e._v("未知")])],1)]}}])}),i("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"100"}}),i("el-table-column",{attrs:{label:"流传输模式","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{staticStyle:{width:"120px"},attrs:{size:"mini",placeholder:"请选择"},on:{change:function(i){return e.transportChange(t.row)}},model:{value:t.row.streamMode,callback:function(i){e.$set(t.row,"streamMode",i)},expression:"scope.row.streamMode"}},[i("el-option",{key:"UDP",attrs:{label:"UDP",value:"UDP"}}),i("el-option",{key:"TCP-ACTIVE",attrs:{label:"TCP主动模式",value:"TCP-ACTIVE"}}),i("el-option",{key:"TCP-PASSIVE",attrs:{label:"TCP被动模式",value:"TCP-PASSIVE"}})],1)]}}])}),i("el-table-column",{attrs:{label:"通道数","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{staticStyle:{"font-size":"1rem"}},[e._v(e._s(t.row.channelCount))])]}}])}),i("el-table-column",{attrs:{label:"状态","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.onLine&&e.myServerId!==t.row.serverId?i("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("在线 ")]):e._e(),t.row.onLine&&e.myServerId===t.row.serverId?i("el-tag",{attrs:{size:"medium"}},[e._v("在线 ")]):e._e(),t.row.onLine?e._e():i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")])],1)]}}])}),i("el-table-column",{attrs:{label:"订阅","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-checkbox",{attrs:{label:"目录",checked:t.row.subscribeCycleForCatalog>0},on:{change:function(i){return e.subscribeForCatalog(t.row.id,i)}}}),i("el-checkbox",{attrs:{label:"位置",checked:t.row.subscribeCycleForMobilePosition>0},on:{change:function(i){return e.subscribeForMobilePosition(t.row.id,i)}}})]}}])}),i("el-table-column",{attrs:{prop:"keepaliveTime",label:"最近心跳","min-width":"140"}}),i("el-table-column",{attrs:{prop:"registerTime",label:"最近注册","min-width":"140"}}),i("el-table-column",{attrs:{label:"操作","min-width":"300",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text",size:"medium",disabled:0===t.row.online,icon:"el-icon-refresh"},on:{click:function(i){return e.refDevice(t.row)},mouseover:function(i){return e.getTooltipContent(t.row.deviceId)}}},[e._v("刷新 ")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{type:"text",size:"medium",icon:"el-icon-video-camera"},on:{click:function(i){return e.showChannelList(t.row)}}},[e._v("通道 ")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(i){return e.edit(t.row)}}},[e._v("编辑")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-dropdown",{on:{command:function(i){e.moreClick(i,t.row)}}},[i("el-button",{attrs:{size:"medium",type:"text"}},[e._v(" 操作"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",[i("el-dropdown-item",{staticStyle:{color:"#f56c6c"},attrs:{command:"delete"}},[e._v(" 删除 ")]),i("el-dropdown-item",{attrs:{command:"setGuard",disabled:!t.row.onLine}},[e._v(" 布防 ")]),i("el-dropdown-item",{attrs:{command:"resetGuard",disabled:!t.row.onLine}},[e._v(" 撤防 ")]),i("el-dropdown-item",{attrs:{command:"syncBasicParam",disabled:!t.row.onLine}},[e._v(" 基础配置同步 ")])],1)],1)]}}])})],1),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}}),i("deviceEdit",{ref:"deviceEdit"}),i("syncChannelProgress",{ref:"syncChannelProgress"}),i("configInfo",{ref:"configInfo"})],1)},a=[],r=i("c14f"),l=i("1da1"),c=(i("99af"),i("d3b7"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"deviceEdit"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"设备编辑",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{"margin-right":"50px"},attrs:{id:"shared"}},[i("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"设备编号",prop:"deviceId"}},[e.isEdit?i("el-input",{attrs:{disabled:""},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}}):e._e(),e.isEdit?e._e():i("el-input",{attrs:{clearable:""},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}})],1),i("el-form-item",{attrs:{label:"设备名称",prop:"name"}},[i("el-input",{attrs:{clearable:""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),i("el-form-item",{attrs:{label:"密码",prop:"password"}},[i("el-input",{attrs:{clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),i("el-form-item",{attrs:{label:"收流IP",prop:"sdpIp"}},[i("el-input",{attrs:{type:"sdpIp",clearable:""},model:{value:e.form.sdpIp,callback:function(t){e.$set(e.form,"sdpIp",t)},expression:"form.sdpIp"}})],1),i("el-form-item",{attrs:{label:"流媒体ID",prop:"mediaServerId"}},[i("el-select",{staticStyle:{float:"left",width:"100%"},model:{value:e.form.mediaServerId,callback:function(t){e.$set(e.form,"mediaServerId",t)},expression:"form.mediaServerId"}},[i("el-option",{key:"auto",attrs:{label:"自动负载最小",value:"auto"}}),e._l(e.mediaServerList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),i("el-form-item",{attrs:{label:"字符集",prop:"charset"}},[i("el-select",{staticStyle:{float:"left",width:"100%"},model:{value:e.form.charset,callback:function(t){e.$set(e.form,"charset",t)},expression:"form.charset"}},[i("el-option",{key:"GB2312",attrs:{label:"GB2312",value:"gb2312"}}),i("el-option",{key:"UTF-8",attrs:{label:"UTF-8",value:"utf-8"}})],1)],1),i("el-form-item",{attrs:{label:"其他选项"}},[i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"SSRC校验"},model:{value:e.form.ssrcCheck,callback:function(t){e.$set(e.form,"ssrcCheck",t)},expression:"form.ssrcCheck"}}),i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"作为消息通道"},model:{value:e.form.asMessageChannel,callback:function(t){e.$set(e.form,"asMessageChannel",t)},expression:"form.asMessageChannel"}}),i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"收到ACK后发流"},model:{value:e.form.broadcastPushAfterAck,callback:function(t){e.$set(e.form,"broadcastPushAfterAck",t)},expression:"form.broadcastPushAfterAck"}})],1),i("el-form-item",[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确认")]),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)}),d=[],u=i("a888"),m={name:"DeviceEdit",directives:{elDragDialog:u["a"]},props:{},data:function(){return{listChangeCallback:null,showDialog:!1,isLoging:!1,hostNames:[],mediaServerList:[],form:{},isEdit:!1,rules:{deviceId:[{required:!0,message:"请输入设备编号",trigger:"blur"}]}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.isEdit=!1,e&&(this.isEdit=!0),this.form={},this.listChangeCallback=t,null!=e&&(this.form=e),this.getMediaServerList()},getMediaServerList:function(){var e=this;this.$store.dispatch("server/getOnlineMediaServerList").then((function(t){e.mediaServerList=t}))},onSubmit:function(){var e=this;this.isEdit?this.$store.dispatch("device/update",this.form).then((function(t){e.listChangeCallback()})):this.$store.dispatch("device/add",this.form).then((function(t){e.listChangeCallback()}))},close:function(){this.showDialog=!1,this.$refs.form.resetFields()}}},h=m,p=i("2877"),f=Object(p["a"])(h,c,d,!1,null,null,null),v=f.exports,b=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"SyncChannelProgress"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],staticStyle:{"text-align":"center"},attrs:{width:"240px",top:"13%","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"show-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("el-progress",{attrs:{type:"circle",percentage:e.percentage,status:e.syncStatus}}),i("div",{staticStyle:{"text-align":"center"}},[e._v(" "+e._s(e.msg)+" ")])],1)],1)},g=[],y=(i("a9e3"),{name:"SyncChannelProgress",directives:{elDragDialog:u["a"]},props:["platformId"],data:function(){return{endCallBack:null,syncStatus:null,percentage:0,total:0,current:0,showDialog:!1,isLoging:!1,syncFlag:!1,deviceId:null,timer:null,errorTimer:null,msg:"正在同步"}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log("deviceId: "+e),this.deviceId=e,this.showDialog=!0,this.msg="",this.percentage=0,this.total=0,this.current=0,this.syncFlag=!1,this.syncStatus=null,this.endCallBack=t,this.getProgress()},getProgress:function(){var e=this;this.$store.dispatch("device/queryDeviceSyncStatus",this.deviceId).then((function(t){var i=t.data,s=(t.code,t.msg);null===i?(e.msg=s,e.timer=setTimeout(e.getProgress,300)):i.syncIng?0===i.total?(e.msg="等待同步中",e.timer=setTimeout(e.getProgress,300)):(e.syncFlag=!0,e.total=i.total,e.current=i.current,e.percentage=Math.floor(Number(i.current)/Number(i.total)*1e4)/100,e.msg="同步中...[".concat(i.current,"/").concat(i.total,"]"),e.timer=setTimeout(e.getProgress,300)):i.errorMsg?(e.msg=i.errorMsg,e.syncStatus="exception"):(e.syncStatus="success",e.percentage=100,e.msg="同步成功",setTimeout((function(){e.showDialog=!1}),3e3))})).catch((function(t){console.log(t),e.syncStatus="error",e.msg=t,window.clearTimeout(e.errorTimer),e.errorTimer=setTimeout((function(){e.showDialog=!1}),2e3)}))},close:function(){this.endCallBack&&this.endCallBack(),window.clearTimeout(this.timer)}}}),w=y,I=Object(p["a"])(w,b,g,!1,null,null,null),S=I.exports,C=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"configInfo"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"接入信息",width:"=80%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{"margin-top":"1rem","margin-right":"100px"},attrs:{id:"shared"}},[e.key&&"sip"!==e.key||!e.configInfoData.sip?e._e():i("el-descriptions",{attrs:{title:"国标服务信息",span:2}},[i("el-descriptions-item",{attrs:{label:"编号"}},[e._v(e._s(e.configInfoData.sip.id))]),i("el-descriptions-item",{attrs:{label:"域"}},[e._v(e._s(e.configInfoData.sip.domain))]),i("el-descriptions-item",{attrs:{label:"IP"}},[e._v(e._s(e.configInfoData.sip.showIp))]),i("el-descriptions-item",{attrs:{label:"端口"}},[e._v(e._s(e.configInfoData.sip.port))]),i("el-descriptions-item",{attrs:{label:"密码"}},[i("span",{staticClass:"password-text",on:{click:function(t){e.passwordVisible=!e.passwordVisible}}},[e._v(" "+e._s(e.passwordVisible?e.configInfoData.sip.password:"•••••••••••")+" ")])])],1),"jt1078Config"===e.key&&e.configInfoData.jt1078Config?i("el-descriptions",{attrs:{title:"部标服务信息",span:2}},[i("el-descriptions-item",{attrs:{label:"端口"}},[e._v(e._s(e.configInfoData.jt1078Config.port))]),i("el-descriptions-item",{attrs:{label:"密码"}},[i("span",{staticClass:"password-text",on:{click:function(t){e.jt1078PasswordVisible=!e.jt1078PasswordVisible}}},[e._v(" "+e._s(e.jt1078PasswordVisible?e.configInfoData.jt1078Config.password:"•••••••••••")+" ")])])],1):e._e()],1)])],1)},_=[],k={name:"ConfigInfo",directives:{elDragDialog:u["a"]},props:{},data:function(){return{showDialog:!1,key:null,passwordVisible:!1,jt1078PasswordVisible:!1,configInfoData:{sip:{}}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.key=t,this.configInfoData=e,this.passwordVisible=!1,this.jt1078PasswordVisible=!1},close:function(){this.showDialog=!1}}},$=k,x=(i("0db8"),Object(p["a"])($,C,_,!1,null,null,null)),P=x.exports,T=i("2b0e"),D={name:"App",components:{configInfo:P,deviceEdit:v,syncChannelProgress:S},data:function(){return{deviceList:[],currentDevice:{},searchSrt:"",online:null,videoComponentList:[],updateLooper:0,currentDeviceChannelsLength:0,currentPage:1,count:15,total:0,getDeviceListLoading:!1}},computed:{Vue:function(){return T["default"]},myServerId:function(){return this.$store.getters.serverId}},mounted:function(){this.initData(),this.updateLooper=setInterval(this.getDeviceList,1e4)},destroyed:function(){this.$destroy("videojs"),clearTimeout(this.updateLooper)},methods:{initData:function(){this.currentPage=1,this.total=0,this.getDeviceList()},currentChange:function(e){this.currentPage=e,this.getDeviceList()},handleSizeChange:function(e){this.count=e,this.getDeviceList()},getDeviceList:function(){var e=this;this.getDeviceListLoading=!0,this.$store.dispatch("device/queryDevices",{page:this.currentPage,count:this.count,query:this.searchSrt,status:this.online}).then((function(t){e.total=t.total,e.deviceList=t.list})).finally((function(){e.getDeviceListLoading=!1}))},deleteDevice:function(e){var t=this,i="确定删除此设备？";0!==e.online&&(i="在线设备删除后仍可通过注册再次上线。<br/>如需彻底删除请先将设备离线。<br/><strong>确定删除此设备？</strong>"),this.$confirm(i,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("device/deleteDevice",e.deviceId).then((function(e){t.getDeviceList()}))}))},showChannelList:function(e){this.$emit("show-channel",e.deviceId)},showDevicePosition:function(e){this.$router.push("/map?deviceId=".concat(e.deviceId))},refDevice:function(e){var t=this;console.log("刷新对应设备:"+e.deviceId),this.$store.dispatch("device/sync",e.deviceId).then((function(i){i&&i.errorMsg?t.$message({showClose:!0,message:i.errorMsg,type:"error"}):t.$refs.syncChannelProgress.openDialog(e.deviceId,(function(){t.getDeviceList()}))})).finally((function(){t.getDeviceList()}))},getTooltipContent:function(){var e=Object(l["a"])(Object(r["a"])().m((function e(t){var i;return Object(r["a"])().w((function(e){while(1)switch(e.n){case 0:return i="",e.n=1,this.$store.dispatch("device/queryDeviceSyncStatus",t).then((function(e){null!==e.errorMsg&&(i=e.errorMsg),i="同步中...[".concat(e.current,"/").concat(e.total,"]")})).catch((function(e){i=e}));case 1:return e.a(2,i)}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),transportChange:function(e){console.log("修改传输方式为 ".concat(e.streamMode,"：").concat(e.deviceId," ")),console.log(e.streamMode),this.$store.dispatch("device/updateDeviceTransport",[e.deviceId,e.streamMode])},edit:function(e){var t=this;this.$refs.deviceEdit.openDialog(e,(function(){t.$refs.deviceEdit.close(),t.$message({showClose:!0,message:"设备修改成功，通道字符集将在下次更新生效",type:"success"}),setTimeout(t.getDeviceList,200)}))},add:function(){var e=this;this.$refs.deviceEdit.openDialog(null,(function(){e.$refs.deviceEdit.close(),e.$message({showClose:!0,message:"添加成功",type:"success"}),setTimeout(e.getDeviceList,200)}))},showInfo:function(){var e=this;this.$store.dispatch("server/getSystemConfig").then((function(t){e.serverId=t.addOn.serverId,e.$refs.configInfo.openDialog(t)}))},moreClick:function(e,t){"setGuard"===e?this.setGuard(t):"resetGuard"===e?this.resetGuard(t):"delete"===e?this.deleteDevice(t):"syncBasicParam"===e&&this.syncBasicParam(t)},setGuard:function(e){var t=this;this.$store.dispatch("device/setGuard",e.deviceId).then((function(e){t.$message.success({showClose:!0,message:"布防成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},resetGuard:function(e){var t=this;this.$store.dispatch("device/ResetGuard",e.deviceId).then((function(e){t.$message.success({showClose:!0,message:"撤防成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},subscribeForCatalog:function(e,t){var i=this;this.$store.dispatch("device/subscribeCatalog",{id:e,cycle:t?60:0}).then((function(e){i.$message.success({showClose:!0,message:t?"订阅成功":"取消订阅成功"})})).catch((function(e){i.$message.error({showClose:!0,message:e.message})}))},subscribeForMobilePosition:function(e,t){var i=this;this.$store.dispatch("device/subscribeMobilePosition",{id:e,cycle:t?60:0,interval:t?5:0}).then((function(e){i.$message.success({showClose:!0,message:t?"订阅成功":"取消订阅成功"})})).catch((function(e){i.$message.error({showClose:!0,message:e.message})}))},syncBasicParam:function(e){var t=this;this.$store.dispatch("device/queryBasicParam").then((function(e){t.$message.success({showClose:!0,message:"配置已同步，当前心跳间隔： ".concat(e.BasicParam.HeartBeatInterval," 心跳间隔:").concat(res.data.data.BasicParam.HeartBeatCount)})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))}}},E=D,z=Object(p["a"])(E,n,a,!1,null,null,null),L=z.exports,R=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{height:"calc(100vh - 124px)"},attrs:{id:"channelList"}},[e.editId?e._e():i("div",{staticStyle:{height:"100%"}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{staticStyle:{"margin-right":"2rem"}},[i("el-page-header",{attrs:{content:"通道列表"},on:{back:e.showDevice}})],1),i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"通道类型"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"设备",value:"false"}}),i("el-option",{attrs:{label:"子目录",value:"true"}})],1)],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",{attrs:{label:"码流类型重置"}},[i("el-select",{staticStyle:{width:"16rem","margin-right":"1rem"},attrs:{placeholder:"请选择码流类型","default-first-option":""},on:{change:e.subStreamChange},model:{value:e.subStream,callback:function(t){e.subStream=t},expression:"subStream"}},[i("el-option",{attrs:{label:"stream:0(主码流)",value:"stream:0"}}),i("el-option",{attrs:{label:"stream:1(子码流)",value:"stream:1"}}),i("el-option",{attrs:{label:"streamnumber:0(主码流-2022)",value:"streamnumber:0"}}),i("el-option",{attrs:{label:"streamnumber:1(子码流-2022)",value:"streamnumber:1"}}),i("el-option",{attrs:{label:"streamprofile:0(主码流-大华)",value:"streamprofile:0"}}),i("el-option",{attrs:{label:"streamprofile:1(子码流-大华)",value:"streamprofile:1"}}),i("el-option",{attrs:{label:"streamMode:main(主码流-水星+TP-LINK)",value:"streamMode:main"}}),i("el-option",{attrs:{label:"streamMode:sub(子码流-水星+TP-LINK)",value:"streamMode:sub"}})],1)],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.refresh()}}})],1)],1),i("el-table",{ref:"channelListTable",staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"small",data:e.deviceChannelList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"180"}}),i("el-table-column",{attrs:{prop:"deviceId",label:"编号","min-width":"180"}}),i("el-table-column",{attrs:{label:"快照","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-image",{staticStyle:{width:"60px"},attrs:{src:e.getSnap(t.row),"preview-src-list":e.getBigSnap(t.row),fit:"contain"},on:{error:function(i){return e.getSnapErrorEvent(t.row.deviceId,t.row.channelId)}}},[i("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[i("i",{staticClass:"el-icon-picture-outline"})])])]}}],null,!1,2328878535)}),i("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"100"}}),i("el-table-column",{attrs:{label:"位置信息","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.longitude&&t.row.latitude?i("span",[e._v(e._s(t.row.longitude)),i("br"),e._v(e._s(t.row.latitude))]):e._e(),t.row.longitude&&t.row.latitude?e._e():i("span",[e._v("无")])]}}],null,!1,1496757730)}),i("el-table-column",{attrs:{prop:"ptzType",label:"云台类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",[e._v(e._s(t.row.ptzTypeText))])]}}],null,!1,867005290)}),i("el-table-column",{attrs:{label:"开启音频","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-color":"#409EFF"},on:{change:function(i){return e.updateChannel(t.row)}},model:{value:t.row.hasAudio,callback:function(i){e.$set(t.row,"hasAudio",i)},expression:"scope.row.hasAudio"}})]}}],null,!1,280923771)}),i("el-table-column",{attrs:{label:"码流类型","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择码流类型","default-first-option":""},on:{change:function(i){return e.channelSubStreamChange(t.row)}},model:{value:t.row.streamIdentification,callback:function(i){e.$set(t.row,"streamIdentification",i)},expression:"scope.row.streamIdentification"}},[i("el-option",{attrs:{label:"stream:0(主码流)",value:"stream:0"}}),i("el-option",{attrs:{label:"stream:1(子码流)",value:"stream:1"}}),i("el-option",{attrs:{label:"streamnumber:0(主码流-2022)",value:"streamnumber:0"}}),i("el-option",{attrs:{label:"streamnumber:1(子码流-2022)",value:"streamnumber:1"}}),i("el-option",{attrs:{label:"streamprofile:0(主码流-大华)",value:"streamprofile:0"}}),i("el-option",{attrs:{label:"streamprofile:1(子码流-大华)",value:"streamprofile:1"}}),i("el-option",{attrs:{label:"streamMode:main(主码流-水星+TP-LINK)",value:"streamMode:main"}}),i("el-option",{attrs:{label:"streamMode:sub(子码流-水星+TP-LINK)",value:"streamMode:sub"}})],1)]}}],null,!1,2473089669)}),i("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.status?i("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.status?i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}],null,!1,2943336009)}),i("el-table-column",{attrs:{label:"操作","min-width":"340",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"medium",disabled:null==e.device||0===e.device.online,icon:"el-icon-video-play",type:"text",loading:t.row.playLoading},on:{click:function(i){return e.sendDevicePush(t.row)}}},[e._v("播放 ")]),t.row.streamId?i("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",disabled:null==e.device||0===e.device.online,icon:"el-icon-switch-button",type:"text"},on:{click:function(i){return e.stopDevicePush(t.row)}}},[e._v("停止 ")]):e._e(),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{size:"medium",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleEdit(t.row)}}},[e._v(" 编辑 ")]),i("el-divider",{attrs:{direction:"vertical"}}),t.row.subCount>0||1===t.row.parental||t.row.deviceId.length<=8?i("el-button",{attrs:{size:"medium",icon:"el-icon-s-open",type:"text"},on:{click:function(i){return e.changeSubchannel(t.row)}}},[e._v("查看 ")]):e._e(),t.row.subCount>0||1===t.row.parental||t.row.deviceId.length<=8?i("el-divider",{attrs:{direction:"vertical"}}):e._e(),i("el-dropdown",{on:{command:function(i){e.moreClick(i,t.row)}}},[i("el-button",{attrs:{size:"medium",type:"text"}},[e._v(" 更多"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",[i("el-dropdown-item",{attrs:{command:"records",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像")]),i("el-dropdown-item",{attrs:{command:"cloudRecords",disabled:null==e.device||0===e.device.online}},[e._v(" 云端录像")]),i("el-dropdown-item",{attrs:{command:"record",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像控制-开始")]),i("el-dropdown-item",{attrs:{command:"stopRecord",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像控制-停止")])],1)],1)]}}],null,!1,289514641)})],1),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),i("devicePlayer",{ref:"devicePlayer"}),e.editId?i("channel-edit",{attrs:{id:e.editId,"close-edit":e.closeEdit}}):e._e()],1)},M=[],A=(i("b0c0"),i("0643"),i("4e3e"),i("159b"),i("0328")),B=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.locading,expression:"locading"}],staticStyle:{width:"100%"},attrs:{id:"ChannelEdit"}},[i("div",{staticClass:"page-header"},[i("div",{staticClass:"page-title"},[i("el-page-header",{attrs:{content:"编辑通道"},on:{back:e.close}})],1)]),i("CommonChannelEdit",{ref:"commonChannelEdit",attrs:{id:e.id,"save-success":e.close,cancel:e.close}})],1)},N=[],F=i("7317"),U={name:"ChannelEdit",components:{CommonChannelEdit:F["a"]},props:["id","closeEdit"],data:function(){return{}},methods:{close:function(){this.closeEdit()}}},V=U,j=Object(p["a"])(V,B,N,!1,null,null,null),O=j.exports,G={name:"ChannelList",components:{devicePlayer:A["a"],ChannelEdit:O},props:{defaultPage:{type:Number,default:1},defaultCount:{type:Number,default:15},deviceId:{type:String,default:null},parentChannelId:{type:String||null,default:null}},data:function(){return{device:null,deviceChannelList:[],videoComponentList:[],currentPlayerInfo:{},updateLooper:0,searchSrt:"",channelType:"",online:"",subStream:"",winHeight:window.innerHeight-200,currentPage:1|this.defaultPage,count:15|this.defaultCount,total:0,beforeUrl:"/device",editId:null,loadSnap:{},ptzTypes:{0:"未知",1:"球机",2:"半球",3:"固定枪机",4:"遥控枪机"}}},watch:{deviceId:function(e){var t=this;this.$store.dispatch("device/queryDeviceOne",this.deviceId).then((function(e){t.device=e})),this.initData()}},mounted:function(){var e=this;console.log(23222),this.deviceId&&this.$store.dispatch("device/queryDeviceOne",this.deviceId).then((function(t){e.device=t})),this.initData()},destroyed:function(){this.$destroy("videojs"),clearTimeout(this.updateLooper)},methods:{initData:function(){null===this.parentChannelId||"undefined"===typeof this.parentChannelId||0===this.parentChannelId?this.getDeviceChannelList():this.showSubChannels()},initParam:function(){this.deviceId=this.$route.params.deviceId,this.parentChannelId=this.$route.params.parentChannelId,this.currentPage=1,this.count=15,""!==this.parentChannelId&&0!==this.parentChannelId||(this.beforeUrl="/device/list")},currentChange:function(e){this.currentPage=e,this.initData()},handleSizeChange:function(e){this.count=e,this.getDeviceChannelList()},getDeviceChannelList:function(){var e=this;console.log(this.deviceId),"undefined"!==typeof this.deviceId&&this.$store.dispatch("device/queryChannels",[this.deviceId,{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType}]).then((function(t){e.total=t.total,e.deviceChannelList=t.list,e.deviceChannelList.forEach((function(t){t.ptzType=t.ptzType+"",e.$set(t,"playLoading",!1)})),e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))}))},sendDevicePush:function(e){var t=this,i=this.deviceId,s=e.deviceId;e.playLoading=!0,console.log("通知设备推流1："+i+" : "+s),this.$store.dispatch("play/play",[i,s]).then((function(o){setTimeout((function(){var e=i+"_"+s;t.loadSnap[i+s]=0,t.getSnapErrorEvent(e)}),5e3),e.streamId=o.stream,t.$refs.devicePlayer.openDialog("media",i,s,{streamInfo:o,hasAudio:e.hasAudio}),setTimeout((function(){t.initData()}),1e3)})).finally((function(){e.playLoading=!1}))},moreClick:function(e,t){"records"===e?this.queryRecords(t):"cloudRecords"===e?this.queryCloudRecords(t):"record"===e?this.startRecord(t):"stopRecord"===e&&this.stopRecord(t)},queryRecords:function(e){var t=this.deviceId,i=e.deviceId;this.$router.push("/device/record/".concat(t,"/").concat(i))},queryCloudRecords:function(e){var t=this.deviceId,i=e.deviceId;this.$router.push("/cloudRecord/detail/rtp/".concat(t,"_").concat(i))},startRecord:function(e){var t=this;this.$store.dispatch("device/deviceRecord",{deviceId:this.deviceId,channelId:e.deviceId,recordCmdStr:"Record"}).then((function(e){t.$message.success({showClose:!0,message:"开始录像成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},stopRecord:function(e){var t=this;this.$store.dispatch("device/deviceRecord",{deviceId:this.deviceId,channelId:e.deviceId,recordCmdStr:"StopRecord"}).then((function(e){t.$message.success({showClose:!0,message:"停止录像成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},stopDevicePush:function(e){var t=this;this.$store.dispatch("play/stop",[this.deviceId,e.deviceId]).then((function(e){t.initData()})).catch((function(e){402===e.response.status?t.initData():console.log(e)}))},getSnap:function(e){var t=window.baseUrl?window.baseUrl:"";return t+"/api/device/query/snap/"+this.deviceId+"/"+e.deviceId},getBigSnap:function(e){return[this.getSnap(e)]},getSnapErrorEvent:function(e,t){var i=this;if("undefined"!==typeof this.loadSnap[e+t]){if(console.log("下载截图"+this.loadSnap[e+t]),this.loadSnap[e+t]>5)return void delete this.loadSnap[e+t];setTimeout((function(){var s="/api/device/query/snap/"+e+"/"+t;i.loadSnap[e+t]++,document.getElementById(e+t).setAttribute("src",s+"?"+(new Date).getTime())}),1e3)}},showDevice:function(){this.$emit("show-device")},changeSubchannel:function(e){var t=this;this.beforeUrl=this.$router.currentRoute.path;var i="/".concat(this.$router.currentRoute.name,"/").concat(this.$router.currentRoute.params.deviceId,"/").concat(e.deviceId);this.$router.push(i).then((function(){t.searchSrt="",t.channelType="",t.online="",t.initParam(),t.initData()}))},showSubChannels:function(){var e=this;this.$store.dispatch("device/querySubChannels",[{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType},this.deviceId,this.parentChannelId]).then((function(t){e.total=t.total,e.deviceChannelList=t.list,e.deviceChannelList.forEach((function(e){e.ptzType=e.ptzType+""})),e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))}))},search:function(){this.currentPage=1,this.total=0,this.initData()},updateChannel:function(e){this.$store.dispatch("device/changeChannelAudio",{channelId:e.id,audio:e.hasAudio})},subStreamChange:function(){var e=this;this.$confirm("确定重置所有通道的码流类型?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$store.dispatch("device/updateChannelStreamIdentification",{deviceDbId:e.device.id,streamIdentification:e.subStream}).then((function(t){e.initData()})).finally((function(){e.subStream=""}))})).catch((function(){e.subStream=""}))},channelSubStreamChange:function(e){var t=this;this.$store.dispatch("device/updateChannelStreamIdentification",{deviceDbId:e.deviceDbId,id:e.id,streamIdentification:e.streamIdentification}).then((function(e){t.initData()})).finally((function(){t.subStream=""}))},refresh:function(){this.initData()},handleEdit:function(e){this.editId=e.id},closeEdit:function(){this.editId=null,this.getDeviceChannelList()}}},W=G,q=Object(p["a"])(W,R,M,!1,null,null,null),H=q.exports,J={name:"Device",components:{deviceList:L,channelList:H},data:function(){return{deviceId:null}},methods:{showChannelList:function(e){this.deviceId=e},showDevice:function(){this.deviceId=null}}},K=J,Z=Object(p["a"])(K,s,o,!1,null,null,null);t["default"]=Z.exports},"28b5":function(e,t,i){},"2b7e":function(e,t,i){},"34c9":function(e,t,i){"use strict";i("2b7e")},"408a":function(e,t,i){var s=i("c6b6");e.exports=function(e){if("number"!=typeof e&&"Number"!=s(e))throw TypeError("Incorrect invocation");return+e}},5869:function(e,t,i){"use strict";i("e0ad")},"6ebe":function(e,t,i){"use strict";i("28b5")},7295:function(e,t,i){},7317:function(e,t,i){"use strict";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{id:"CommonChannelEdit"}},[i("el-form",{ref:"passwordForm",staticClass:"channel-form",attrs:{"status-icon":"","label-width":"160px"}},[i("div",{staticClass:"form-box"},[i("el-form-item",{attrs:{label:"名称"}},[i("el-input",{attrs:{placeholder:"请输入通道名称"},model:{value:e.form.gbName,callback:function(t){e.$set(e.form,"gbName",t)},expression:"form.gbName"}})],1),i("el-form-item",{attrs:{label:"编码"}},[i("el-input",{attrs:{placeholder:"请输入通道编码"},scopedSlots:e._u([{key:"append",fn:function(){return[i("el-button",{on:{click:function(t){return e.buildDeviceIdCode(e.form.gbDeviceId)}}},[e._v("生成")])]},proxy:!0}]),model:{value:e.form.gbDeviceId,callback:function(t){e.$set(e.form,"gbDeviceId",t)},expression:"form.gbDeviceId"}})],1),i("el-form-item",{attrs:{label:"设备厂商"}},[i("el-input",{attrs:{placeholder:"请输入设备厂商"},model:{value:e.form.gbManufacturer,callback:function(t){e.$set(e.form,"gbManufacturer",t)},expression:"form.gbManufacturer"}})],1),i("el-form-item",{attrs:{label:"设备型号"}},[i("el-input",{attrs:{placeholder:"请输入设备型号"},model:{value:e.form.gbModel,callback:function(t){e.$set(e.form,"gbModel",t)},expression:"form.gbModel"}})],1),i("el-form-item",{attrs:{label:"行政区域"}},[i("el-input",{attrs:{placeholder:"请输入行政区域"},scopedSlots:e._u([{key:"append",fn:function(){return[i("el-button",{on:{click:function(t){return e.chooseCivilCode()}}},[e._v("选择")])]},proxy:!0}]),model:{value:e.form.gbCivilCode,callback:function(t){e.$set(e.form,"gbCivilCode",t)},expression:"form.gbCivilCode"}})],1),i("el-form-item",{attrs:{label:"安装地址"}},[i("el-input",{attrs:{placeholder:"请输入安装地址"},model:{value:e.form.gbAddress,callback:function(t){e.$set(e.form,"gbAddress",t)},expression:"form.gbAddress"}})],1),i("el-form-item",{attrs:{label:"子设备"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择是否有子设备"},model:{value:e.form.gbParental,callback:function(t){e.$set(e.form,"gbParental",t)},expression:"form.gbParental"}},[i("el-option",{attrs:{label:"有",value:1}}),i("el-option",{attrs:{label:"无",value:0}})],1)],1),i("el-form-item",{attrs:{label:"父节点编码"}},[i("el-input",{attrs:{placeholder:"请输入父节点编码或选择所属虚拟组织"},scopedSlots:e._u([{key:"append",fn:function(){return[i("el-button",{on:{click:function(t){return e.chooseGroup()}}},[e._v("选择")])]},proxy:!0}]),model:{value:e.form.gbParentId,callback:function(t){e.$set(e.form,"gbParentId",t)},expression:"form.gbParentId"}})],1),i("el-form-item",{attrs:{label:"设备状态"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择设备状态"},model:{value:e.form.gbStatus,callback:function(t){e.$set(e.form,"gbStatus",t)},expression:"form.gbStatus"}},[i("el-option",{attrs:{label:"在线",value:"ON"}}),i("el-option",{attrs:{label:"离线",value:"OFF"}})],1)],1),i("el-form-item",{attrs:{label:"经度"}},[i("el-input",{attrs:{placeholder:"请输入经度"},model:{value:e.form.gbLongitude,callback:function(t){e.$set(e.form,"gbLongitude",t)},expression:"form.gbLongitude"}})],1),i("el-form-item",{attrs:{label:"纬度"}},[i("el-input",{attrs:{placeholder:"请输入纬度"},model:{value:e.form.gbLatitude,callback:function(t){e.$set(e.form,"gbLatitude",t)},expression:"form.gbLatitude"}})],1),i("el-form-item",{attrs:{label:"云台类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择云台类型"},model:{value:e.form.gbPtzType,callback:function(t){e.$set(e.form,"gbPtzType",t)},expression:"form.gbPtzType"}},[i("el-option",{attrs:{label:"球机",value:1}}),i("el-option",{attrs:{label:"半球",value:2}}),i("el-option",{attrs:{label:"固定枪机",value:3}}),i("el-option",{attrs:{label:"遥控枪机",value:4}}),i("el-option",{attrs:{label:"遥控半球",value:5}}),i("el-option",{attrs:{label:"多目设备的全景/拼接通道",value:6}}),i("el-option",{attrs:{label:"多目设备的分割通道",value:7}})],1)],1)],1),i("div",[i("el-form-item",{attrs:{label:"警区"}},[i("el-input",{attrs:{placeholder:"请输入警区"},model:{value:e.form.gbBlock,callback:function(t){e.$set(e.form,"gbBlock",t)},expression:"form.gbBlock"}})],1),i("el-form-item",{attrs:{label:"设备归属"}},[i("el-input",{attrs:{placeholder:"请输入设备归属"},model:{value:e.form.gbOwner,callback:function(t){e.$set(e.form,"gbOwner",t)},expression:"form.gbOwner"}})],1),i("el-form-item",{attrs:{label:"信令安全模式"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择信令安全模式"},model:{value:e.form.gbSafetyWay,callback:function(t){e.$set(e.form,"gbSafetyWay",t)},expression:"form.gbSafetyWay"}},[i("el-option",{attrs:{label:"不采用",value:0}}),i("el-option",{attrs:{label:"S/MIME签名",value:2}}),i("el-option",{attrs:{label:"S/MIME加密签名同时采用",value:3}}),i("el-option",{attrs:{label:"数字摘要",value:4}})],1)],1),i("el-form-item",{attrs:{label:"注册方式"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择注册方式"},model:{value:e.form.gbRegisterWay,callback:function(t){e.$set(e.form,"gbRegisterWay",t)},expression:"form.gbRegisterWay"}},[i("el-option",{attrs:{label:"IETFRFC3261标准",value:1}}),i("el-option",{attrs:{label:"基于口令的双向认证",value:2}}),i("el-option",{attrs:{label:"基于数字证书的双向认证注册",value:3}})],1)],1),i("el-form-item",{attrs:{label:"证书序列号"}},[i("el-input",{attrs:{type:"number",placeholder:"请输入证书序列号"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,"gbCertNum",t)},expression:"form.gbCertNum"}})],1),i("el-form-item",{attrs:{label:"证书有效标识"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择证书有效标识"},model:{value:e.form.gbCertifiable,callback:function(t){e.$set(e.form,"gbCertifiable",t)},expression:"form.gbCertifiable"}},[i("el-option",{attrs:{label:"有效",value:1}}),i("el-option",{attrs:{label:"无效",value:0}})],1)],1),i("el-form-item",{attrs:{label:"无效原因码"}},[i("el-input",{attrs:{type:"errCode",placeholder:"请输入无效原因码"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,"gbCertNum",t)},expression:"form.gbCertNum"}})],1),i("el-form-item",{attrs:{label:"证书终止有效期"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:e.form.gbEndTime,callback:function(t){e.$set(e.form,"gbEndTime",t)},expression:"form.gbEndTime"}})],1),i("el-form-item",{attrs:{label:"保密属性"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择保密属性"},model:{value:e.form.gbSecrecy,callback:function(t){e.$set(e.form,"gbSecrecy",t)},expression:"form.gbSecrecy"}},[i("el-option",{attrs:{label:"不涉密",value:0}}),i("el-option",{attrs:{label:"涉密",value:1}})],1)],1),i("el-form-item",{attrs:{label:"IP地址"}},[i("el-input",{attrs:{placeholder:"请输入IP地址"},model:{value:e.form.gbIpAddress,callback:function(t){e.$set(e.form,"gbIpAddress",t)},expression:"form.gbIpAddress"}})],1),i("el-form-item",{attrs:{label:"端口"}},[i("el-input",{attrs:{type:"number",placeholder:"请输入端口"},model:{value:e.form.gbPort,callback:function(t){e.$set(e.form,"gbPort",t)},expression:"form.gbPort"}})],1),i("el-form-item",{attrs:{label:"设备口令"}},[i("el-input",{attrs:{placeholder:"请输入设备口令"},model:{value:e.form.gbPassword,callback:function(t){e.$set(e.form,"gbPassword",t)},expression:"form.gbPassword"}})],1)],1),i("div",[i("el-form-item",{attrs:{label:"业务分组编号"}},[i("el-input",{attrs:{placeholder:"请输入业务分组编号"},model:{value:e.form.gbBusinessGroupId,callback:function(t){e.$set(e.form,"gbBusinessGroupId",t)},expression:"form.gbBusinessGroupId"}})],1),i("el-form-item",{attrs:{label:"位置类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择位置类型"},model:{value:e.form.gbPositionType,callback:function(t){e.$set(e.form,"gbPositionType",t)},expression:"form.gbPositionType"}},[i("el-option",{attrs:{label:"省际检查站",value:1}}),i("el-option",{attrs:{label:"党政机关",value:2}}),i("el-option",{attrs:{label:"车站码头",value:3}}),i("el-option",{attrs:{label:"中心广场",value:4}}),i("el-option",{attrs:{label:"体育场馆",value:5}}),i("el-option",{attrs:{label:"商业中心",value:6}}),i("el-option",{attrs:{label:"宗教场所",value:7}}),i("el-option",{attrs:{label:"校园周边",value:8}}),i("el-option",{attrs:{label:"治安复杂区域",value:9}}),i("el-option",{attrs:{label:"交通干线",value:10}})],1)],1),i("el-form-item",{attrs:{label:"室外/室内"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择位置类型"},model:{value:e.form.gbRoomType,callback:function(t){e.$set(e.form,"gbRoomType",t)},expression:"form.gbRoomType"}},[i("el-option",{attrs:{label:"室外",value:1}}),i("el-option",{attrs:{label:"室内",value:2}})],1)],1),i("el-form-item",{attrs:{label:"用途"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择位置类型"},model:{value:e.form.gbUseType,callback:function(t){e.$set(e.form,"gbUseType",t)},expression:"form.gbUseType"}},[i("el-option",{attrs:{label:"治安",value:1}}),i("el-option",{attrs:{label:"交通",value:2}}),i("el-option",{attrs:{label:"重点",value:3}})],1)],1),i("el-form-item",{attrs:{label:"补光"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择位置类型"},model:{value:e.form.gbSupplyLightType,callback:function(t){e.$set(e.form,"gbSupplyLightType",t)},expression:"form.gbSupplyLightType"}},[i("el-option",{attrs:{label:"无补光",value:1}}),i("el-option",{attrs:{label:"红外补光",value:2}}),i("el-option",{attrs:{label:"白光补光",value:3}}),i("el-option",{attrs:{label:"激光补光",value:4}}),i("el-option",{attrs:{label:"其他",value:9}})],1)],1),i("el-form-item",{attrs:{label:"监视方位"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择位置类型"},model:{value:e.form.gbDirectionType,callback:function(t){e.$set(e.form,"gbDirectionType",t)},expression:"form.gbDirectionType"}},[i("el-option",{attrs:{label:"东(西向东)",value:1}}),i("el-option",{attrs:{label:"西(东向西)",value:2}}),i("el-option",{attrs:{label:"南(北向南)",value:3}}),i("el-option",{attrs:{label:"北(南向北)",value:4}}),i("el-option",{attrs:{label:"东南(西北到东南)",value:5}}),i("el-option",{attrs:{label:"东北(西南到东北)",value:6}}),i("el-option",{attrs:{label:"西南(东北到西南)",value:7}}),i("el-option",{attrs:{label:"西北(东南到西北)",value:8}})],1)],1),i("el-form-item",{attrs:{label:"分辨率"}},[i("el-input",{attrs:{placeholder:"请输入分辨率"},model:{value:e.form.gbResolution,callback:function(t){e.$set(e.form,"gbResolution",t)},expression:"form.gbResolution"}})],1),i("el-form-item",{attrs:{label:"下载倍速"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择位置类型"},model:{value:e.form.gbDownloadSpeedArray,callback:function(t){e.$set(e.form,"gbDownloadSpeedArray",t)},expression:"form.gbDownloadSpeedArray"}},[i("el-option",{attrs:{label:"1倍速",value:"1"}}),i("el-option",{attrs:{label:"2倍速",value:"2"}}),i("el-option",{attrs:{label:"4倍速",value:"4"}}),i("el-option",{attrs:{label:"8倍速",value:"8"}}),i("el-option",{attrs:{label:"16倍速",value:"16"}})],1)],1),i("el-form-item",{attrs:{label:"空域编码能力"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择空域编码能力"},model:{value:e.form.gbSvcSpaceSupportMod,callback:function(t){e.$set(e.form,"gbSvcSpaceSupportMod",t)},expression:"form.gbSvcSpaceSupportMod"}},[i("el-option",{attrs:{label:"1级增强",value:"1"}}),i("el-option",{attrs:{label:"2级增强",value:"2"}}),i("el-option",{attrs:{label:"3级增强",value:"3"}})],1)],1),i("el-form-item",{attrs:{label:"时域编码能力"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择空域编码能力"},model:{value:e.form.gbSvcTimeSupportMode,callback:function(t){e.$set(e.form,"gbSvcTimeSupportMode",t)},expression:"form.gbSvcTimeSupportMode"}},[i("el-option",{attrs:{label:"1级增强",value:"1"}}),i("el-option",{attrs:{label:"2级增强",value:"2"}}),i("el-option",{attrs:{label:"3级增强",value:"3"}})],1)],1),i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),e.cancel?i("el-button",{on:{click:e.cancelSubmit}},[e._v("取消")]):e._e(),1===e.form.dataType?i("el-button",{on:{click:e.reset}},[e._v("重置")]):e._e()],1)],1)]),i("channelCode",{ref:"channelCode"}),i("chooseCivilCode",{ref:"chooseCivilCode"}),i("chooseGroup",{ref:"chooseGroup"})],1)},o=[],n=(i("a15b"),i("d3b7"),i("165c")),a=i("363b"),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"chooseGroup"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"选择虚拟组织",width:"30%",top:"5rem","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("GroupTree",{ref:"regionTree",attrs:{"show-header":!0,edit:!0,"enable-add-channel":!1,"click-event":e.treeNodeClickEvent,"on-channel-change":e.onChannelChange,"tree-height":"45vh"}}),i("el-form",[i("el-form-item",[i("div",{staticStyle:{"text-align":"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)],1)},l=[],c=i("a888"),d=i("c2c8"),u={name:"ChooseCivilCode",directives:{elDragDialog:c["a"]},components:{GroupTree:d["a"]},props:{},data:function(){return{showDialog:!1,endCallback:!1,groupDeviceId:"",businessGroup:""}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.endCallback=e},onSubmit:function(){this.endCallback&&this.endCallback(this.groupDeviceId,this.businessGroup),this.close()},close:function(){this.showDialog=!1},treeNodeClickEvent:function(e){""!==e.deviceId&&e.deviceId!==e.businessGroup&&(this.groupDeviceId=e.deviceId,this.businessGroup=e.businessGroup)},onChannelChange:function(e){}}},m=u,h=i("2877"),p=Object(h["a"])(m,r,l,!1,null,null,null),f=p.exports,v={name:"CommonChannelEdit",components:{ChooseCivilCode:a["a"],ChooseGroup:f,channelCode:n["a"]},props:["id","dataForm","saveSuccess","cancel"],data:function(){return{loading:!1,form:{}}},created:function(){this.id?this.getCommonChannel():(this.dataForm.gbDeviceId||(this.dataForm.gbDeviceId=""),console.log(this.dataForm),this.form=this.dataForm)},methods:{onSubmit:function(){var e=this;this.loading=!0,this.form.gbDownloadSpeedArray&&(this.form.gbDownloadSpeed=this.form.gbDownloadSpeedArray.join("/")),this.form.gbId?this.$store.dispatch("commonChanel/update",this.form).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]})):this.$store.dispatch("commonChanel/add",this.form).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]}))},reset:function(){var e=this;this.$confirm("确定重置为默认内容?","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,e.$axios({method:"post",url:"/api/common/channel/reset",params:{id:e.form.gbId}}).then((function(t){0===t.data.code&&(e.$message.success({showClose:!0,message:"重置成功 已保存"}),e.getCommonChannel())})).catch((function(e){console.error(e)})).finally((function(){return[e.loading=!1]}))})).catch((function(){}))},getCommonChannel:function(){var e=this;this.loading=!0,this.$store.dispatch("commonChanel/queryOne",this.id).then((function(t){t.gbDownloadSpeed&&(t.gbDownloadSpeedArray=t.gbDownloadSpeed.split("/")),e.form=t})).finally((function(){e.loading=!1}))},buildDeviceIdCode:function(e){var t=this;this.$refs.channelCode.openDialog((function(e){console.log(t.form),console.log("code===> "+e),t.form.gbDeviceId=e,console.log("code22===> "+e)}),e)},chooseCivilCode:function(){var e=this;this.$refs.chooseCivilCode.openDialog((function(t){e.form.gbCivilCode=t}))},chooseGroup:function(){var e=this;this.$refs.chooseGroup.openDialog((function(t,i){e.form.gbBusinessGroupId=i,e.form.gbParentId=t}))},cancelSubmit:function(){this.cancel&&this.cancel()}}},b=v,g=(i("257e"),Object(h["a"])(b,s,o,!1,null,null,null));t["a"]=g.exports},"953f":function(e,t,i){},"989a":function(e,t,i){"use strict";i("953f")},a135:function(e,t,i){"use strict";i("b21a")},a4be:function(e,t,i){},b21a:function(e,t,i){},b680:function(e,t,i){"use strict";var s=i("23e7"),o=i("a691"),n=i("408a"),a=i("1148"),r=i("d039"),l=1..toFixed,c=Math.floor,d=function(e,t,i){return 0===t?i:t%2===1?d(e,t-1,i*e):d(e*e,t/2,i)},u=function(e){var t=0,i=e;while(i>=4096)t+=12,i/=4096;while(i>=2)t+=1,i/=2;return t},m=l&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!r((function(){l.call({})}));s({target:"Number",proto:!0,forced:m},{toFixed:function(e){var t,i,s,r,l=n(this),m=o(e),h=[0,0,0,0,0,0],p="",f="0",v=function(e,t){var i=-1,s=t;while(++i<6)s+=e*h[i],h[i]=s%1e7,s=c(s/1e7)},b=function(e){var t=6,i=0;while(--t>=0)i+=h[t],h[t]=c(i/e),i=i%e*1e7},g=function(){var e=6,t="";while(--e>=0)if(""!==t||0===e||0!==h[e]){var i=String(h[e]);t=""===t?i:t+a.call("0",7-i.length)+i}return t};if(m<0||m>20)throw RangeError("Incorrect fraction digits");if(l!=l)return"NaN";if(l<=-1e21||l>=1e21)return String(l);if(l<0&&(p="-",l=-l),l>1e-21)if(t=u(l*d(2,69,1))-69,i=t<0?l*d(2,-t,1):l/d(2,t,1),i*=4503599627370496,t=52-t,t>0){v(0,i),s=m;while(s>=7)v(1e7,0),s-=7;v(d(10,s,1),0),s=t-1;while(s>=23)b(1<<23),s-=23;b(1<<s),v(1,1),b(2),f=g()}else v(0,i),v(1<<-t,0),f=g()+a.call("0",m);return m>0?(r=f.length,f=p+(r<=m?"0."+a.call("0",m-r)+f:f.slice(0,r-m)+"."+f.slice(r-m))):f=p+f,f}})},bbbb:function(e,t,i){},bbf2:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},o=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"rtcPlayer"}},[i("video",{staticStyle:{"text-align":"left"},attrs:{id:"webRtcPlayerBox",controls:"",autoplay:""}},[e._v(" Your browser is too old which doesn't support HTML5 video. ")])])}],n=null,a={name:"RtcPlayer",props:["videoUrl","error","hasaudio"],data:function(){return{timer:null}},watch:{videoUrl:function(e,t){this.pause(),this.play(e)},immediate:!0},mounted:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){"undefined"===typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},destroyed:function(){clearTimeout(this.timer)},methods:{play:function(e){var t=this;n=new ZLMRTCClient.Endpoint({element:document.getElementById("webRtcPlayerBox"),debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!0,recvOnly:!0,usedatachannel:!1}),n.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error("ICE 协商出错"),t.eventcallbacK("ICE ERROR","ICE 协商出错")})),n.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS,(function(e){console.log("播放成功",e.streams),t.eventcallbacK("playing","播放成功")})),n.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(i){console.error("offer anwser 交换失败",i),t.eventcallbacK("OFFER ANSWER ERROR ","offer anwser 交换失败"),-400==i.code&&"流不存在"==i.msg&&(console.log("流不存在"),t.timer=setTimeout((function(){t.webrtcPlayer.close(),t.play(e)}),100))})),n.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM,(function(e){t.eventcallbacK("LOCAL STREAM","获取到了本地流")}))},pause:function(){null!=n&&(n.close(),n=null)},eventcallbacK:function(e,t){console.log("player 事件回调"),console.log(e),console.log(t)}}},r=a,l=(i("6ebe"),i("2877")),c=Object(l["a"])(r,s,o,!1,null,null,null);t["default"]=c.exports},c5f0:function(e,t,i){"use strict";i("bbbb")},d017:function(e,t,i){},e0ad:function(e,t,i){},e8b4:function(e,t,i){"use strict";i("0868")},e9c4:function(e,t,i){var s=i("23e7"),o=i("d066"),n=i("d039"),a=o("JSON","stringify"),r=/[\uD800-\uDFFF]/g,l=/^[\uD800-\uDBFF]$/,c=/^[\uDC00-\uDFFF]$/,d=function(e,t,i){var s=i.charAt(t-1),o=i.charAt(t+1);return l.test(e)&&!c.test(o)||c.test(e)&&!l.test(s)?"\\u"+e.charCodeAt(0).toString(16):e},u=n((function(){return'"\\udf06\\ud834"'!==a("\udf06\ud834")||'"\\udead"'!==a("\udead")}));a&&s({target:"JSON",stat:!0,forced:u},{stringify:function(e,t,i){var s=a.apply(null,arguments);return"string"==typeof s?s.replace(r,d):s}})},fdc8:function(e,t,i){"use strict";i("7295")}}]);