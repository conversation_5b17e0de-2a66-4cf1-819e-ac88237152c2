{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750426849484}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}