{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750430926012}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}