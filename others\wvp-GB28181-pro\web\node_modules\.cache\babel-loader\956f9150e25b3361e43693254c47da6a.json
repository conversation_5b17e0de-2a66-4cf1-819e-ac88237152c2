{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750431331348}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}