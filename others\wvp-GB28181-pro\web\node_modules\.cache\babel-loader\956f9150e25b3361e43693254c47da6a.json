{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750430544768}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}