{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"app\"],{0:function(e,t,n){e.exports=n(\"56d7\")},\"03ed\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-historyLog\",use:\"icon-historyLog-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-historyLog\"><path d=\"M927.44064 56.064967A191.149846 191.149846 0 1 0 983.35197 191.303483a189.95516 189.95516 0 0 0-55.91133-135.238516zM792.202123 334.665867a143.362385 143.362385 0 1 1 143.362385-143.362384 143.362385 143.362385 0 0 1-143.362385 143.362384z\" p-id=\"2810\" /><path d=\"M210.389779 1024h-11.230053a169.884426 169.884426 0 0 1-158.176498-169.884426V163.825692A169.406551 169.406551 0 0 1 210.389779 5.410257H515.990596a33.929098 33.929098 0 0 1 33.929097 33.929098 34.168035 34.168035 0 0 1-33.929097 33.929098H200.59335A101.548356 101.548356 0 0 0 108.602486 174.577871v689.334133A101.548356 101.548356 0 0 0 209.911905 955.902867h553.378804a101.787293 101.787293 0 0 0 92.229801-101.309418V480.656562a33.69016 33.69016 0 0 1 33.929098-33.929097 33.929098 33.929098 0 0 1 33.929097 33.929097v384.689066a169.645488 169.645488 0 0 1-169.406551 158.654372z\" p-id=\"2811\" /><path d=\"M293.539962 565.240369h376.565197v57.822829H293.539962zM294.495712 405.869185h202.857774v57.822829h-202.857774zM770.219891 88.082566h28.672477V186.524737h-28.672477z\" p-id=\"2812\" /><path d=\"M770.219891 185.091113h134.521704v28.672477h-134.521704z\" fill=\"#040000\" p-id=\"2813\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"05ff\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-setting\",use:\"icon-setting-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-setting\"><path d=\"M936.6 403.9c-14.4-14-34.2-21.9-54.9-22.1h-0.1l-5.4 0.2c-13.6 0-46.1-3-55.6-31.2-8.8-26.4 10.5-59 10.4-59 23.8-33.8 14.2-79.4-21.5-102l-100.2-63c-34.5-21.9-84.2-13.8-109.8 23.2-0.2 0.5-23.3 51.7-69.7 52.9-49.9 0-70.6-58.6-72.1-62-19.6-34.6-68.8-48.7-105.7-30l-106 54c-37.8 19.3-51.9 63.9-29.5 102.5 11.8 13.2 42.4 57 27.8 83.9-19.4 36-90.2 24.9-101.4 23.9-42.5 0-77.4 32.7-78 72.8L64 561.7c-0.2 19.6 7.7 38.2 22.3 52.2 14.6 14 34 21.8 54.9 22 1.8-0.1 3.4-0.2 5-0.7 0.2 0 17.4-4.6 36.4-4.6 22.4 0 36.8 5.9 42.7 17.6 11.2 22.4-20.1 63.3-33.6 77.7-23.8 33.8-14.2 79.4 21.5 102l100.3 63.2c34.6 21.7 84.2 13.5 109.5-22.4 0.2-0.7 33.2-61.4 75.8-56.4 48.3 5.8 64.5 60.6 66.2 64.8 20.5 35.7 67.8 49.1 105.7 29.8L776.6 853c37.8-19.3 52-63.9 29.3-102.8-1.6-1.7-39.2-42.8-12-85.2 21.4-33.5 74.8-23.4 85.9-22.4 42.5 0 77.4-32.6 77.9-72.8l1.2-113.6c0.2-19.7-7.7-38.3-22.3-52.3zM511.5 654.6c-85.3-0.1-154.4-65.3-154.6-145.8 0.2-80.5 69.3-145.6 154.6-145.7 85.3 0.1 154.4 65.3 154.5 145.7-0.2 80.5-69.3 145.7-154.5 145.8z\" p-id=\"14694\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"08e8\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-captcha\",use:\"icon-captcha-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-captcha\"> \\x3c!-- 新增 width 和 height 属性 --\\x3e\\r\\n\\t<path d=\"M981.333333 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a42.666667 42.666667 0 1 1 85.333333 0v768a42.666667 42.666667 0 0 0 42.666667 42.666667h768a42.666667 42.666667 0 0 0 42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666666-42.666667zM640 704a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 1 1 0-85.333333z m0-213.333333a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 1 1 0-85.333333z m0-213.333334a42.666667 42.666667 0 1 1 0 85.333334H384a42.666667 42.666667 0 1 1 0-85.333334z m128-277.333333a42.666667 42.666667 0 1 1 0 85.333333H256a42.666667 42.666667 0 1 1 0-85.333333z\" p-id=\"8825\" />\\r\\n</symbol>'});i.a.add(r);t[\"default\"]=r},\"0ea9\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-channelManger\",use:\"icon-channelManger-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-channelManger\"><path d=\"M104.64 0h128v632H337.28v128H232.64V1024h-128v-264H0v-128h104.64V0z m686.72 0h128v264H1024v128h-104.64V1024h-128V392H686.72v-128h104.64V0zM448 0h128v448h104.576v128H576v448H448V576H343.36V448H448z\" p-id=\"4476\" /></symbol>'});i.a.add(r);t[\"default\"]=r},1:function(e,t){},10:function(e,t){},1515:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-realLog\",use:\"icon-realLog-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-realLog\"><path d=\"M981.333333 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a42.666667 42.666667 0 1 1 85.333333 0v768a42.666667 42.666667 0 0 0 42.666667 42.666667h768a42.666667 42.666667 0 0 0 42.666667-42.666667V128a42.666667 42.666667 0 0 1 42.666666-42.666667zM640 704a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 1 1 0-85.333333z m0-213.333333a42.666667 42.666667 0 1 1 0 85.333333H384a42.666667 42.666667 0 1 1 0-85.333333z m0-213.333334a42.666667 42.666667 0 1 1 0 85.333334H384a42.666667 42.666667 0 1 1 0-85.333334z m128-277.333333a42.666667 42.666667 0 1 1 0 85.333333H256a42.666667 42.666667 0 1 1 0-85.333333z\" p-id=\"8825\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"153b\":function(e,t,n){\"use strict\";n(\"271c\")},\"18f0\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-link\",use:\"icon-link-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-link\"><path d=\"M115.625 127.937H.063V12.375h57.781v12.374H12.438v90.813h90.813V70.156h12.374z\" /><path d=\"M116.426 2.821l8.753 8.753-56.734 56.734-8.753-8.745z\" /><path d=\"M127.893 37.982h-12.375V12.375H88.706V0h39.187z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"19a6\":function(e,t,n){\"use strict\";n(\"e66f\")},2:function(e,t){},\"271c\":function(e,t,n){},\"279e\":function(e,t,n){},\"2a3d\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-password\",use:\"icon-password-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-password\"><path d=\"M108.8 44.322H89.6v-5.36c0-9.04-3.308-24.163-25.6-24.163-23.145 0-25.6 16.881-25.6 24.162v5.361H19.2v-5.36C19.2 15.281 36.798 0 64 0c27.202 0 44.8 15.281 44.8 38.961v5.361zm-32 39.356c0-5.44-5.763-9.832-12.8-9.832-7.037 0-12.8 4.392-12.8 9.832 0 3.682 2.567 6.808 6.407 8.477v11.205c0 2.718 2.875 4.962 6.4 4.962 3.524 0 6.4-2.244 6.4-4.962V92.155c3.833-1.669 6.393-4.795 6.393-8.477zM128 64v49.201c0 8.158-8.645 14.799-19.2 14.799H19.2C8.651 128 0 121.359 0 113.201V64c0-8.153 8.645-14.799 19.2-14.799h89.6c10.555 0 19.2 6.646 19.2 14.799z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"2b29\":function(e,t,n){\"use strict\";n(\"f6c3\")},\"2bb1\":function(e,t,n){},\"2c87\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-device\",use:\"icon-device-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-device\"><path d=\"M868 96H156c-33.1 0-60 26.9-60 60v712c0 33.1 26.9 60 60 60h712c33.1 0 60-26.9 60-60V156c0-33.1-26.9-60-60-60zM743.5 691.3c-20.9 20.3-51.3 38.1-91 53.5-39.7 15.4-80 23.1-120.7 23.1-51.8 0-96.9-10.9-135.4-32.6-38.5-21.7-67.4-52.8-86.8-93.2-19.4-40.4-29-84.4-29-131.9 0-51.6 10.8-97.4 32.4-137.5 21.6-40.1 53.2-70.8 94.9-92.2 31.7-16.4 71.3-24.7 118.5-24.7 61.5 0 109.5 12.9 144 38.7 34.6 25.8 56.8 61.4 66.7 106.9L637.8 420c-7-24.3-20.1-43.5-39.3-57.6-19.3-14.1-43.3-21.1-72.1-21.1-43.7 0-78.4 13.8-104.2 41.5-25.8 27.7-38.7 68.8-38.7 123.3 0 58.8 13.1 102.8 39.2 132.2 26.1 29.4 60.3 44.1 102.7 44.1 20.9 0 41.9-4.1 63-12.3 21-8.2 39.1-18.2 54.2-29.9v-62.8H528V494h215.5v197.3z\" p-id=\"45091\" /></symbol>'});i.a.add(r);t[\"default\"]=r},3:function(e,t){},\"30c3\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-example\",use:\"icon-example-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-example\"><path d=\"M96.258 57.462h31.421C124.794 27.323 100.426 2.956 70.287.07v31.422a32.856 32.856 0 0 1 25.971 25.97zm-38.796-25.97V.07C27.323 2.956 2.956 27.323.07 57.462h31.422a32.856 32.856 0 0 1 25.97-25.97zm12.825 64.766v31.421c30.46-2.885 54.507-27.253 57.713-57.712H96.579c-2.886 13.466-13.146 23.726-26.292 26.291zM31.492 70.287H.07c2.886 30.46 27.253 54.507 57.713 57.713V96.579c-13.466-2.886-23.726-13.146-26.291-26.292z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"331a\":function(e,t){var n={admin:{token:\"admin-token\"},editor:{token:\"editor-token\"}},a={\"admin-token\":{roles:[\"admin\"],introduction:\"I am a super administrator\",avatar:\"https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif\",name:\"Super Admin\"},\"editor-token\":{roles:[\"editor\"],introduction:\"I am an editor\",avatar:\"https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif\",name:\"Normal Editor\"}};e.exports=[{url:\"/vue-admin-template/user/login\",type:\"post\",response:function(e){var t=e.body.username,a=n[t];return a?{code:2e4,data:a}:{code:60204,message:\"Account and password are incorrect.\"}}},{url:\"/vue-admin-template/user/info.*\",type:\"get\",response:function(e){var t=e.query.token,n=a[t];return n?{code:2e4,data:n}:{code:50008,message:\"Login failed, unable to get user details.\"}}},{url:\"/vue-admin-template/user/logout\",type:\"post\",response:function(e){return{code:2e4,data:\"success\"}}}]},\"3ecb\":function(e,t,n){},4:function(e,t){},4360:function(e,t,n){\"use strict\";var a=n(\"2b0e\"),c=n(\"2f62\"),o=(n(\"b0c0\"),{sidebar:function(e){return e.app.sidebar},device:function(e){return e.app.device},token:function(e){return e.user.token},serverId:function(e){return e.user.serverId},name:function(e){return e.user.name},visitedViews:function(e){return e.tagsView.visitedViews},cachedViews:function(e){return e.tagsView.cachedViews}}),i=o,r=n(\"a78e\"),u=n.n(r),s={sidebar:{opened:!u.a.get(\"sidebarStatus\")||!!+u.a.get(\"sidebarStatus\"),withoutAnimation:!1},device:\"desktop\"},d={TOGGLE_SIDEBAR:function(e){e.sidebar.opened=!e.sidebar.opened,e.sidebar.withoutAnimation=!1,e.sidebar.opened?u.a.set(\"sidebarStatus\",1):u.a.set(\"sidebarStatus\",0)},CLOSE_SIDEBAR:function(e,t){u.a.set(\"sidebarStatus\",0),e.sidebar.opened=!1,e.sidebar.withoutAnimation=t},TOGGLE_DEVICE:function(e,t){e.device=t}},l={toggleSideBar:function(e){var t=e.commit;t(\"TOGGLE_SIDEBAR\")},closeSideBar:function(e,t){var n=e.commit,a=t.withoutAnimation;n(\"CLOSE_SIDEBAR\",a)},toggleDevice:function(e,t){var n=e.commit;n(\"TOGGLE_DEVICE\",t)}},f={namespaced:!0,state:s,mutations:d,actions:l},m=n(\"83d6\"),h=n.n(m),p=h.a.showSettings,v=h.a.fixedHeader,w=h.a.sidebarLogo,b=h.a.tagsViews,g={showSettings:p,fixedHeader:v,sidebarLogo:w,tagsViews:b},y={CHANGE_SETTING:function(e,t){var n=t.key,a=t.value;e.hasOwnProperty(n)&&(e[n]=a)}},O={changeSetting:function(e,t){var n=e.commit;n(\"CHANGE_SETTING\",t)}},P={namespaced:!0,state:g,mutations:y,actions:O},j=(n(\"d3b7\"),n(\"498a\"),n(\"1c46\")),x=n.n(j),_=n(\"b775\");function k(e){return Object(_[\"a\"])({url:\"/api/user/login\",method:\"get\",params:e})}function I(){return Object(_[\"a\"])({url:\"/api/user/logout\",method:\"get\"})}function V(){return Object(_[\"a\"])({method:\"post\",url:\"/api/user/userInfo\"})}function C(e){var t=e.pushKey,n=e.userId;return Object(_[\"a\"])({method:\"post\",url:\"/api/user/changePushKey\",params:{pushKey:t,userId:n}})}function S(e){var t=e.page,n=e.count;return Object(_[\"a\"])({method:\"get\",url:\"/api/user/users\",params:{page:t,count:n}})}function T(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/user/delete?id=\".concat(e)})}function L(e){var t=e.username,n=e.password,a=e.roleId;return Object(_[\"a\"])({method:\"post\",url:\"/api/user/add\",params:{username:t,password:n,roleId:a}})}function z(e){var t=e.oldPassword,n=e.password;return Object(_[\"a\"])({method:\"post\",url:\"/api/user/changePassword\",params:{oldPassword:t,password:n}})}function E(e){var t=e.password,n=e.userId;return Object(_[\"a\"])({method:\"post\",url:\"/api/user/changePasswordForAdmin\",params:{password:t,userId:n}})}var H=n(\"5f87\"),M=n(\"a18c\"),q=function(){return{token:Object(H[\"c\"])(),name:\"\",serverId:\"\"}},D=q(),B={RESET_STATE:function(e){Object.assign(e,q())},SET_TOKEN:function(e,t){e.token=t},SET_NAME:function(e,t){e.name=t},SET_SERVER_ID:function(e,t){e.serverId=t}},A={login:function(e,t){var n=e.commit,a=t.username,c=t.password,o=t.captcha;return new Promise((function(e,t){k({username:a.trim(),password:x.a.createHash(\"md5\").update(c,\"utf8\").digest(\"hex\"),captcha:o}).then((function(t){var a=t.data;n(\"SET_TOKEN\",a.accessToken),n(\"SET_NAME\",a.username),n(\"SET_SERVER_ID\",a.serverId),Object(H[\"i\"])(a.accessToken),Object(H[\"g\"])(a.username),Object(H[\"h\"])(a.serverId),e()})).catch((function(e){t(e)}))}))},logout:function(e){var t=e.commit,n=e.state;return new Promise((function(e,a){I(n.token).then((function(){Object(H[\"f\"])(),Object(H[\"e\"])(),Object(H[\"d\"])(),Object(M[\"b\"])(),t(\"RESET_STATE\"),e()})).catch((function(e){a(e)}))}))},resetToken:function(e){var t=e.commit;return new Promise((function(e){Object(H[\"f\"])(),t(\"RESET_STATE\"),e()}))},getUserInfo:function(e){e.commit;return new Promise((function(e,t){V().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},changePushKey:function(e,t){e.commit;return new Promise((function(e,n){C(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){S(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},removeById:function(e,t){e.commit;return new Promise((function(e,n){T(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){L(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},changePassword:function(e,t){e.commit;return new Promise((function(e,n){z(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},changePasswordForAdmin:function(e,t){e.commit;return new Promise((function(e,n){E(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},$={namespaced:!0,state:D,mutations:B,actions:A},R=n(\"2909\"),G=n(\"3835\"),W=n(\"b85c\"),N=(n(\"4de4\"),n(\"caad\"),n(\"fb6a\"),n(\"a434\"),n(\"2532\"),n(\"0643\"),n(\"2382\"),n(\"9a9a\"),n(\"ddb0\"),{visitedViews:[],cachedViews:[]}),F={ADD_VISITED_VIEW:function(e,t){e.visitedViews.some((function(e){return e.path===t.path}))||e.visitedViews.push(Object.assign({},t,{title:t.meta.title||\"no-name\"}))},ADD_CACHED_VIEW:function(e,t){e.cachedViews.includes(t.name)||t.meta.noCache||e.cachedViews.push(t.name)},DEL_VISITED_VIEW:function(e,t){var n,a=Object(W[\"a\"])(e.visitedViews.entries());try{for(a.s();!(n=a.n()).done;){var c=Object(G[\"a\"])(n.value,2),o=c[0],i=c[1];if(i.path===t.path){e.visitedViews.splice(o,1);break}}}catch(r){a.e(r)}finally{a.f()}},DEL_CACHED_VIEW:function(e,t){var n=e.cachedViews.indexOf(t.name);n>-1&&e.cachedViews.splice(n,1)},DEL_OTHERS_VISITED_VIEWS:function(e,t){e.visitedViews=e.visitedViews.filter((function(e){return e.meta.affix||e.path===t.path}))},DEL_OTHERS_CACHED_VIEWS:function(e,t){var n=e.cachedViews.indexOf(t.name);e.cachedViews=n>-1?e.cachedViews.slice(n,n+1):[]},DEL_ALL_VISITED_VIEWS:function(e){var t=e.visitedViews.filter((function(e){return e.meta.affix}));e.visitedViews=t},DEL_ALL_CACHED_VIEWS:function(e){e.cachedViews=[]},UPDATE_VISITED_VIEW:function(e,t){var n,a=Object(W[\"a\"])(e.visitedViews);try{for(a.s();!(n=a.n()).done;){var c=n.value;if(c.path===t.path){c=Object.assign(c,t);break}}}catch(o){a.e(o)}finally{a.f()}}},X={addView:function(e,t){var n=e.dispatch;n(\"addVisitedView\",t),n(\"addCachedView\",t)},addVisitedView:function(e,t){var n=e.commit;n(\"ADD_VISITED_VIEW\",t)},addCachedView:function(e,t){var n=e.commit;n(\"ADD_CACHED_VIEW\",t)},delView:function(e,t){var n=e.dispatch,a=e.state;return new Promise((function(e){n(\"delVisitedView\",t),n(\"delCachedView\",t),e({visitedViews:Object(R[\"a\"])(a.visitedViews),cachedViews:Object(R[\"a\"])(a.cachedViews)})}))},delVisitedView:function(e,t){var n=e.commit,a=e.state;return new Promise((function(e){n(\"DEL_VISITED_VIEW\",t),e(Object(R[\"a\"])(a.visitedViews))}))},delCachedView:function(e,t){var n=e.commit,a=e.state;return new Promise((function(e){n(\"DEL_CACHED_VIEW\",t),e(Object(R[\"a\"])(a.cachedViews))}))},delOthersViews:function(e,t){var n=e.dispatch,a=e.state;return new Promise((function(e){n(\"delOthersVisitedViews\",t),n(\"delOthersCachedViews\",t),e({visitedViews:Object(R[\"a\"])(a.visitedViews),cachedViews:Object(R[\"a\"])(a.cachedViews)})}))},delOthersVisitedViews:function(e,t){var n=e.commit,a=e.state;return new Promise((function(e){n(\"DEL_OTHERS_VISITED_VIEWS\",t),e(Object(R[\"a\"])(a.visitedViews))}))},delOthersCachedViews:function(e,t){var n=e.commit,a=e.state;return new Promise((function(e){n(\"DEL_OTHERS_CACHED_VIEWS\",t),e(Object(R[\"a\"])(a.cachedViews))}))},delAllViews:function(e,t){var n=e.dispatch,a=e.state;return new Promise((function(e){n(\"delAllVisitedViews\",t),n(\"delAllCachedViews\",t),e({visitedViews:Object(R[\"a\"])(a.visitedViews),cachedViews:Object(R[\"a\"])(a.cachedViews)})}))},delAllVisitedViews:function(e){var t=e.commit,n=e.state;return new Promise((function(e){t(\"DEL_ALL_VISITED_VIEWS\"),e(Object(R[\"a\"])(n.visitedViews))}))},delAllCachedViews:function(e){var t=e.commit,n=e.state;return new Promise((function(e){t(\"DEL_ALL_CACHED_VIEWS\"),e(Object(R[\"a\"])(n.cachedViews))}))},updateVisitedView:function(e,t){var n=e.commit;n(\"UPDATE_VISITED_VIEW\",t)}},K={namespaced:!0,state:N,mutations:F,actions:X};function U(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/one\",params:{id:e}})}function J(){return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/industry/list\"})}function Y(){return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/type/list\"})}function Q(){return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/network/identification/list\"})}function Z(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/update\",data:e})}function ee(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/reset\",params:{id:e}})}function te(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/add\",data:e})}function ne(e){var t=e.page,n=e.count,a=e.channelType,c=e.query,o=e.online,i=e.civilCode;return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/civilcode/list\",params:{page:t,count:n,channelType:a,query:c,online:o,civilCode:i}})}function ae(e){var t=e.page,n=e.count,a=e.channelType,c=e.query,o=e.online;return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/civilCode/unusual/list\",params:{page:t,count:n,channelType:a,query:c,online:o}})}function ce(e){var t=e.page,n=e.count,a=e.channelType,c=e.query,o=e.online;return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/parent/unusual/list\",params:{page:t,count:n,channelType:a,query:c,online:o}})}function oe(e){var t=e.all,n=e.channelIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/civilCode/unusual/clear\",data:{all:t,channelIds:n}})}function ie(e){var t=e.all,n=e.channelIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/parent/unusual/clear\",data:{all:t,channelIds:n}})}function re(e){var t=e.page,n=e.count,a=e.channelType,c=e.query,o=e.online,i=e.groupDeviceId;return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/parent/list\",params:{page:t,count:n,channelType:a,query:c,online:o,groupDeviceId:i}})}function ue(e){var t=e.civilCode,n=e.channelIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/region/add\",data:{civilCode:t,channelIds:n}})}function se(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/region/delete\",data:{channelIds:e}})}function de(e){var t=e.civilCode,n=e.deviceIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/region/device/add\",data:{civilCode:t,deviceIds:n}})}function le(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/region/device/delete\",data:{deviceIds:e}})}function fe(e){var t=e.parentId,n=e.businessGroup,a=e.channelIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/group/add\",data:{parentId:t,businessGroup:n,channelIds:a}})}function me(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/group/delete\",data:{channelIds:e}})}function he(e){var t=e.parentId,n=e.businessGroup,a=e.deviceIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/group/device/add\",data:{parentId:t,businessGroup:n,deviceIds:a}})}function pe(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/common/channel/group/device/delete\",data:{deviceIds:e}})}function ve(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/common/channel/play\",params:{channelId:e}})}var we={update:function(e,t){e.commit;return new Promise((function(e,n){Z(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){te(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},reset:function(e,t){e.commit;return new Promise((function(e,n){ee(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryOne:function(e,t){e.commit;return new Promise((function(e,n){U(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addDeviceToGroup:function(e,t){e.commit;return new Promise((function(e,n){he(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addToGroup:function(e,t){e.commit;return new Promise((function(e,n){fe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteDeviceFromGroup:function(e,t){e.commit;return new Promise((function(e,n){pe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteFromGroup:function(e,t){e.commit;return new Promise((function(e,n){me(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addDeviceToRegion:function(e,t){e.commit;return new Promise((function(e,n){de(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addToRegion:function(e,t){e.commit;return new Promise((function(e,n){ue(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteDeviceFromRegion:function(e,t){e.commit;return new Promise((function(e,n){le(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteFromRegion:function(e,t){e.commit;return new Promise((function(e,n){se(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getCivilCodeList:function(e,t){e.commit;return new Promise((function(e,n){ne(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getParentList:function(e,t){e.commit;return new Promise((function(e,n){re(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getUnusualParentList:function(e,t){e.commit;return new Promise((function(e,n){ce(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},clearUnusualParentList:function(e,t){e.commit;return new Promise((function(e,n){ie(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getUnusualCivilCodeList:function(e,t){e.commit;return new Promise((function(e,n){ae(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},clearUnusualCivilCodeList:function(e,t){e.commit;return new Promise((function(e,n){oe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getIndustryList:function(e){e.commit;return new Promise((function(e,t){J().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getTypeList:function(e){e.commit;return new Promise((function(e,t){Y().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getNetworkIdentificationList:function(e){e.commit;return new Promise((function(e,t){Q().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},playChannel:function(e,t){e.commit;return new Promise((function(e,n){ve(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},be={namespaced:!0,actions:we};function ge(e){var t=e.query,n=e.parent,a=e.hasChannel;return Object(_[\"a\"])({method:\"get\",url:\"/api/region/tree/list\",params:{query:t,parent:n,hasChannel:a}})}function ye(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/region/delete\",params:{id:e}})}function Oe(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/region/description\",params:{civilCode:e}})}function Pe(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/region/addByCivilCode\",params:{civilCode:e}})}function je(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/region/base/child/list\",params:{parent:e}})}function xe(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/region/update\",data:e})}function _e(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/region/add\",data:e})}function ke(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/region/path\",params:{deviceId:e}})}var Ie={getTreeList:function(e,t){e.commit;return new Promise((function(e,n){ge(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteRegion:function(e,t){e.commit;return new Promise((function(e,n){ye(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},description:function(e,t){e.commit;return new Promise((function(e,n){Oe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addByCivilCode:function(e,t){e.commit;return new Promise((function(e,n){Pe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryChildListInBase:function(e,t){e.commit;return new Promise((function(e,n){je(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},update:function(e,t){e.commit;return new Promise((function(e,n){xe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){_e(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryPath:function(e,t){e.commit;return new Promise((function(e,n){ke(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},Ve={namespaced:!0,actions:Ie};n(\"99af\");function Ce(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/\".concat(e,\"/sync_status/\")})}function Se(e){var t=e.page,n=e.count,a=e.query,c=e.status;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/devices\",params:{page:t,count:n,query:a,status:c}})}function Te(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/device/query/devices/\".concat(e,\"/delete\")})}function Le(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/devices/\".concat(e,\"/sync\")})}function ze(e,t){return Object(_[\"a\"])({method:\"post\",url:\"/api/device/query/transport/\".concat(e,\"/\").concat(t)})}function Ee(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/control/guard/\".concat(e,\"/SetGuard\")})}function He(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/control/guard/\".concat(e,\"/ResetGuard\")})}function Me(e){var t=e.id,n=e.cycle;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/subscribe/catalog\",params:{id:t,cycle:n}})}function qe(e){var t=e.id,n=e.cycle,a=e.interval;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/subscribe/mobile-position\",params:{id:t,cycle:n,interval:a}})}function De(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/config/query/\".concat(e,\"/BasicParam\")})}function Be(e){var t=e.deviceId,n=e.channelDeviceId;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/channel/one\",params:{deviceId:t,channelDeviceId:n}})}function Ae(e,t){var n=t.page,a=t.count,c=t.query,o=t.online,i=t.channelType,r=t.catalogUnderDevice;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/devices/\".concat(e,\"/channels\"),params:{page:n,count:a,query:c,online:o,channelType:i,catalogUnderDevice:r}})}function $e(e){var t=e.deviceId,n=e.channelId,a=e.recordCmdStr;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/control/record\",params:{deviceId:t,channelId:n,recordCmdStr:a}})}function Re(e){var t=e.parentId,n=e.page,a=e.count;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/tree/channel/\".concat(this.deviceId),params:{parentId:t,page:n,count:a}})}function Ge(e){var t=e.channelId,n=e.audio;return Object(_[\"a\"])({method:\"post\",url:\"/api/device/query/channel/audio\",params:{channelId:t,audio:n}})}function We(e){var t=e.deviceDbId,n=e.streamIdentification;return Object(_[\"a\"])({method:\"post\",url:\"/api/device/query/channel/stream/identification/update/\",params:{deviceDbId:t,streamIdentification:n}})}function Ne(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/device/query/device/update\",data:e})}function Fe(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/device/query/device/add\",data:e})}function Xe(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/devices/\".concat(e)})}function Ke(e,t){var n=e.page,a=e.count,c=e.parentId,o=e.onlyCatalog;return Object(_[\"a\"])({method:\"get\",url:\"/api/device/query/tree/\".concat(t),params:{page:n,count:a,parentId:c,onlyCatalog:o}})}var Ue={queryDeviceSyncStatus:function(e,t){e.commit;return new Promise((function(e,n){Ce(t).then((function(t){e(t)})).catch((function(e){n(e)}))}))},queryDevices:function(e,t){e.commit;return new Promise((function(e,n){Se(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},sync:function(e,t){e.commit;return new Promise((function(e,n){Le(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},updateDeviceTransport:function(e,t){e.commit;var n=Object(G[\"a\"])(t,2),a=n[0],c=n[1];return new Promise((function(e,t){ze(a,c).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},setGuard:function(e,t){e.commit;return new Promise((function(e,n){Ee(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},resetGuard:function(e,t){e.commit;return new Promise((function(e,n){He(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},subscribeCatalog:function(e,t){e.commit;return new Promise((function(e,n){Me(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},subscribeMobilePosition:function(e,t){e.commit;return new Promise((function(e,n){qe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryBasicParam:function(e,t){e.commit;return new Promise((function(e,n){De(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryChannelOne:function(e,t){e.commit;return new Promise((function(e,n){Be(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryChannels:function(e,t){e.commit;var n=Object(G[\"a\"])(t,2),a=n[0],c=n[1];return new Promise((function(e,t){Ae(a,c).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},deviceRecord:function(e,t){e.commit;return new Promise((function(e,n){$e(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},querySubChannels:function(e,t){e.commit;var n=Object(G[\"a\"])(t,3),a=n[0],c=n[1],o=n[2];return new Promise((function(e,t){$e(a,c,o).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},queryChannelTree:function(e,t){e.commit;return new Promise((function(e,n){Re(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},changeChannelAudio:function(e,t){e.commit;return new Promise((function(e,n){Ge(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},updateChannelStreamIdentification:function(e,t){e.commit;return new Promise((function(e,n){We(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},update:function(e,t){e.commit;return new Promise((function(e,n){Ne(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){Fe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryDeviceOne:function(e,t){e.commit;return new Promise((function(e,n){Xe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryDeviceTree:function(e,t,n){e.commit;return new Promise((function(e,a){Ke(t,n).then((function(t){var n=t.data;e(n)})).catch((function(e){a(e)}))}))},deleteDevice:function(e,t){e.commit;return new Promise((function(e,n){Te(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},Je={namespaced:!0,actions:Ue};function Ye(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/group/update\",data:e})}function Qe(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/group/add\",data:e})}function Ze(e){var t=e.query,n=e.parent,a=e.hasChannel;return Object(_[\"a\"])({method:\"get\",url:\"/api/group/tree/list\",params:{query:t,parent:n,hasChannel:a}})}function et(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/group/delete\",params:{id:e}})}function tt(e){var t=e.deviceId,n=e.businessGroup;return Object(_[\"a\"])({method:\"get\",url:\"/api/group/path\",params:{deviceId:t,businessGroup:n}})}var nt={update:function(e,t){e.commit;return new Promise((function(e,n){Ye(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){Qe(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getTreeList:function(e,t){e.commit;return new Promise((function(e,n){Ze(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteGroup:function(e,t){e.commit;return new Promise((function(e,n){et(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getPath:function(e,t){e.commit;return new Promise((function(e,n){tt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},at={namespaced:!0,actions:nt};function ct(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/online/list\"})}function ot(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/list\"})}function it(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/one/\"+e})}function rt(e){var t=e.ip,n=e.httpPort,a=e.secret,c=e.type;return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/check\",params:{ip:t,port:n,secret:a,type:c}})}function ut(e){var t=e.ip,n=e.port;return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/record/check\",params:{ip:t,port:n}})}function st(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/server/media_server/save\",data:e})}function dt(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/server/media_server/delete\",params:{id:e}})}function lt(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/system/configInfo\"})}function ft(e){var t=e.app,n=e.stream,a=e.mediaServerId;return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/media_info\",params:{app:t,stream:n,mediaServerId:a}})}function mt(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/system/info\"})}function ht(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/media_server/load\"})}function pt(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/resource/info\"})}function vt(){return Object(_[\"a\"])({method:\"get\",url:\"/api/server/info\"})}var wt={getOnlineMediaServerList:function(e){e.commit;return new Promise((function(e,t){ct().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getMediaServerList:function(e){e.commit;return new Promise((function(e,t){ot().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getMediaServer:function(e,t){e.commit;return new Promise((function(e,n){it(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},checkMediaServer:function(e,t){e.commit;return new Promise((function(e,n){rt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},checkMediaServerRecord:function(e,t){e.commit;return new Promise((function(e,n){ut(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},saveMediaServer:function(e,t){e.commit;return new Promise((function(e,n){st(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteMediaServer:function(e,t){e.commit;return new Promise((function(e,n){dt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getSystemConfig:function(e){e.commit;return new Promise((function(e,t){lt().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getMediaInfo:function(e,t){e.commit;return new Promise((function(e,n){ft(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getSystemInfo:function(e){e.commit;return new Promise((function(e,t){mt().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getMediaServerLoad:function(e){e.commit;return new Promise((function(e,t){ht().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},getResourceInfo:function(e){e.commit;return new Promise((function(e,t){pt().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},info:function(e){e.commit;return new Promise((function(e,t){vt().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))}},bt={namespaced:!0,actions:wt};function gt(e,t){return Object(_[\"a\"])({method:\"get\",url:\"/api/play/start/\"+e+\"/\"+t})}function yt(e,t){return Object(_[\"a\"])({method:\"get\",url:\"/api/play/stop/\"+e+\"/\"+t})}function Ot(e,t,n){return Object(_[\"a\"])({method:\"get\",url:\"/api/play/broadcast/\"+e+\"/\"+t+\"?timeout=30&broadcastMode=\"+n})}function Pt(e,t){return Object(_[\"a\"])({method:\"get\",url:\"/api/play/broadcast/stop/\"+e+\"/\"+t})}var jt={play:function(e,t){e.commit;var n=Object(G[\"a\"])(t,2),a=n[0],c=n[1];return new Promise((function(e,t){gt(a,c).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},stop:function(e,t){e.commit;var n=Object(G[\"a\"])(t,2),a=n[0],c=n[1];return new Promise((function(e,t){yt(a,c).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},broadcastStart:function(e,t){e.commit;var n=Object(G[\"a\"])(t,3),a=n[0],c=n[1],o=n[2];return new Promise((function(e,t){Ot(a,c,o).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},broadcastStop:function(e,t){e.commit;var n=Object(G[\"a\"])(t,2),a=n[0],c=n[1];return new Promise((function(e,t){Pt(a,c).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))}},xt={namespaced:!0,actions:jt};function _t(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/playback/start/\"+n+\"/\"+a+\"?startTime=\"+c+\"&endTime=\"+o})}function kt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/playback/resume/\"+e})}function It(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/playback/pause/\"+e})}function Vt(e){var t=Object(G[\"a\"])(e,2),n=t[0],a=t[1];return Object(_[\"a\"])({method:\"get\",url:\"/api/playback/speed/\".concat(n,\"/\").concat(a)})}function Ct(e,t,n){return Object(_[\"a\"])({method:\"get\",url:\"/api/playback/stop/\"+e+\"/\"+t+\"/\"+n})}var St={play:function(e,t){e.commit;return new Promise((function(e,n){_t(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},resume:function(e,t){e.commit;return new Promise((function(e,n){kt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},pause:function(e,t){e.commit;return new Promise((function(e,n){It(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},setSpeed:function(e,t){e.commit;return new Promise((function(e,n){Vt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},stop:function(e,t){e.commit;var n=Object(G[\"a\"])(t,3),a=n[0],c=n[1],o=n[2];return new Promise((function(e,t){Ct(a,c,o).then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))}},Tt={namespaced:!0,actions:St};function Lt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/push/save_to_gb\",data:e})}function zt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/push/add\",data:e})}function Et(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/push/update\",data:e})}function Ht(e){var t=e.page,n=e.count,a=e.query,c=e.pushing,o=e.mediaServerId;return Object(_[\"a\"])({method:\"get\",url:\"/api/push/list\",params:{page:t,count:n,query:a,pushing:c,mediaServerId:o}})}function Mt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/push/start\",params:{id:e}})}function qt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/push/remove\",params:{id:e}})}function Dt(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/push/remove_form_gb\",data:e})}function Bt(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/push/batchRemove\",data:{ids:e}})}var At={saveToGb:function(e,t){e.commit;return new Promise((function(e,n){Lt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){zt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},update:function(e,t){e.commit;return new Promise((function(e,n){Et(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){Ht(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},play:function(e,t){e.commit;return new Promise((function(e,n){Mt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},remove:function(e,t){e.commit;return new Promise((function(e,n){qt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},removeFormGb:function(e,t){e.commit;return new Promise((function(e,n){Dt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},batchRemove:function(e,t){e.commit;return new Promise((function(e,n){Bt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},$t={namespaced:!0,actions:At};function Rt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/proxy/ffmpeg_cmd/list\",params:{mediaServerId:e}})}function Gt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/proxy/save\",data:e})}function Wt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/proxy/update\",data:e})}function Nt(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/proxy/add\",data:e})}function Ft(e){var t=e.page,n=e.count,a=e.query,c=e.pulling,o=e.mediaServerId;return Object(_[\"a\"])({method:\"get\",url:\"/api/proxy/list\",params:{page:t,count:n,query:a,pulling:c,mediaServerId:o}})}function Xt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/proxy/start\",params:{id:e}})}function Kt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/proxy/stop\",params:{id:e}})}function Ut(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/proxy/delete\",params:{id:e}})}var Jt={queryFfmpegCmdList:function(e,t){e.commit;return new Promise((function(e,n){Rt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},save:function(e,t){e.commit;return new Promise((function(e,n){Gt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},update:function(e,t){e.commit;return new Promise((function(e,n){Wt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){Nt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){Ft(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},play:function(e,t){e.commit;return new Promise((function(e,n){Xt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},stopPlay:function(e,t){e.commit;return new Promise((function(e,n){Kt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},remove:function(e,t){e.commit;return new Promise((function(e,n){Ut(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},Yt={namespaced:!0,actions:Jt};function Qt(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/record/plan/get\",params:{planId:e}})}function Zt(e){var t=e.name,n=e.planList;return Object(_[\"a\"])({method:\"post\",url:\"/api/record/plan/add\",data:{name:t,planItemList:n}})}function en(e){var t=e.id,n=e.name,a=e.planList;return Object(_[\"a\"])({method:\"post\",url:\"/api/record/plan/update\",data:{id:t,name:n,planItemList:a}})}function tn(e){var t=e.page,n=e.count,a=e.query;return Object(_[\"a\"])({method:\"get\",url:\"/api/record/plan/query\",params:{page:t,count:n,query:a}})}function nn(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/record/plan/delete\",params:{planId:e}})}function an(e){var t=e.page,n=e.count,a=e.channelType,c=e.query,o=e.online,i=e.planId,r=e.hasLink;return Object(_[\"a\"])({method:\"get\",url:\"/api/record/plan/channel/list\",params:{page:t,count:n,query:c,online:o,channelType:a,planId:i,hasLink:r}})}function cn(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/record/plan/link\",data:e})}var on={getPlan:function(e,t){e.commit;return new Promise((function(e,n){Qt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addPlan:function(e,t){e.commit;return new Promise((function(e,n){Zt(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},update:function(e,t){e.commit;return new Promise((function(e,n){en(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){tn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deletePlan:function(e,t){e.commit;return new Promise((function(e,n){nn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryChannelList:function(e,t){e.commit;return new Promise((function(e,n){an(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},linkPlan:function(e,t){e.commit;return new Promise((function(e,n){cn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},rn={namespaced:!0,actions:on};function un(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/play/path\",params:{recordId:e}})}function sn(e){var t=e.app,n=e.stream,a=e.year,c=e.month,o=e.mediaServerId;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/date/list\",params:{app:t,stream:n,year:a,month:c,mediaServerId:o}})}function dn(e){var t=e.app,n=e.stream,a=e.date;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/loadRecord\",params:{app:t,stream:n,date:a}})}function ln(e){var t=e.app,n=e.stream,a=e.date,c=e.fileIndex;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/loadRecordByFileIndex\",params:{app:t,stream:n,date:a,fileIndex:c}})}function fn(e){var t=e.mediaServerId,n=e.app,a=e.stream,c=e.seek,o=e.schema;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/seek\",params:{mediaServerId:t,app:n,stream:a,seek:c,schema:o}})}function mn(e){var t=e.mediaServerId,n=e.app,a=e.stream,c=e.speed,o=e.schema;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/speed\",params:{mediaServerId:t,app:n,stream:a,speed:c,schema:o}})}function hn(e){var t=e.app,n=e.stream,a=e.mediaServerId,c=e.startTime,o=e.endTime;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/task/add\",params:{app:t,stream:n,mediaServerId:a,startTime:c,endTime:o}})}function pn(e){var t=e.mediaServerId,n=e.isEnd;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/task/list\",params:{mediaServerId:t,isEnd:n}})}function vn(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/cloud/record/delete\",data:{ids:e}})}function wn(e){var t=e.app,n=e.stream,a=e.query,c=e.startTime,o=e.endTime,i=e.mediaServerId,r=e.page,u=e.count,s=e.ascOrder;return Object(_[\"a\"])({method:\"get\",url:\"/api/cloud/record/list\",params:{app:t,stream:n,query:a,startTime:c,endTime:o,mediaServerId:i,page:r,count:u,ascOrder:s}})}var bn={getPlayPath:function(e,t){e.commit;return new Promise((function(e,n){un(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},loadRecord:function(e,t){e.commit;return new Promise((function(e,n){dn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},loadRecordByFileIndex:function(e,t){e.commit;return new Promise((function(e,n){ln(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},seek:function(e,t){e.commit;return new Promise((function(e,n){fn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},speed:function(e,t){e.commit;return new Promise((function(e,n){mn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryListByData:function(e,t){e.commit;return new Promise((function(e,n){sn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addTask:function(e,t){e.commit;return new Promise((function(e,n){hn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryTaskList:function(e,t){e.commit;return new Promise((function(e,n){pn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){wn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deleteRecord:function(e,t){e.commit;return new Promise((function(e,n){vn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},gn={namespaced:!0,actions:bn};function yn(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/update\",data:e})}function On(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/add\",data:e})}function Pn(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/platform/exit/\".concat(e)})}function jn(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/platform/delete/\",params:{id:e}})}function xn(e){return Object(_[\"a\"])({method:\"get\",url:\"/api/platform/channel/push\",params:{id:e}})}function _n(){return Object(_[\"a\"])({method:\"get\",url:\"/api/platform/server_config\"})}function kn(e){var t=e.count,n=e.page,a=e.query;return Object(_[\"a\"])({method:\"get\",url:\"/api/platform/query\",params:{count:t,page:n,query:a}})}function In(e){var t=e.page,n=e.count,a=e.query,c=e.online,o=e.channelType,i=e.platformId,r=e.hasShare;return Object(_[\"a\"])({method:\"get\",url:\"/api/platform/channel/list\",params:{page:t,count:n,query:a,online:c,channelType:o,platformId:i,hasShare:r}})}function Vn(e){var t=e.platformId,n=e.channelIds,a=e.all;return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/channel/add\",data:{platformId:t,channelIds:n,all:a}})}function Cn(e){var t=e.platformId,n=e.deviceIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/channel/device/add\",data:{platformId:t,deviceIds:n}})}function Sn(e){var t=e.platformId,n=e.deviceIds;return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/channel/device/remove\",data:{platformId:t,deviceIds:n}})}function Tn(e){var t=e.platformId,n=e.channelIds,a=e.all;return Object(_[\"a\"])({method:\"delete\",url:\"/api/platform/channel/remove\",data:{platformId:t,channelIds:n,all:a}})}function Ln(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/platform/channel/custom/update\",data:e})}var zn={update:function(e,t){e.commit;return new Promise((function(e,n){yn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){On(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},exit:function(e,t){e.commit;return new Promise((function(e,n){Pn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},remove:function(e,t){e.commit;return new Promise((function(e,n){jn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},pushChannel:function(e,t){e.commit;return new Promise((function(e,n){xn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getServerConfig:function(e){e.commit;return new Promise((function(e,t){_n().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))},query:function(e,t){e.commit;return new Promise((function(e,n){kn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},getChannelList:function(e,t){e.commit;return new Promise((function(e,n){In(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addChannel:function(e,t){e.commit;return new Promise((function(e,n){Vn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addChannelByDevice:function(e,t){e.commit;return new Promise((function(e,n){Cn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},removeChannelByDevice:function(e,t){e.commit;return new Promise((function(e,n){Sn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},removeChannel:function(e,t){e.commit;return new Promise((function(e,n){Tn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},updateCustomChannel:function(e,t){e.commit;return new Promise((function(e,n){Ln(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},En={namespaced:!0,actions:zn};function Hn(){return Object(_[\"a\"])({method:\"get\",url:\"/api/role/all\"})}var Mn={getAll:function(e){e.commit;return new Promise((function(e,t){Hn().then((function(t){var n=t.data;e(n)})).catch((function(e){t(e)}))}))}},qn={namespaced:!0,actions:Mn};function Dn(e){var t=e.id,n=e.remark;return Object(_[\"a\"])({method:\"post\",url:\"/api/userApiKey/remark\",params:{id:t,remark:n}})}function Bn(e){var t=e.page,n=e.count;return Object(_[\"a\"])({method:\"get\",url:\"/api/userApiKey/userApiKeys\",params:{page:t,count:n}})}function An(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/userApiKey/enable?id=\".concat(e)})}function $n(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/userApiKey/disable?id=\".concat(e)})}function Rn(e){return Object(_[\"a\"])({method:\"post\",url:\"/api/userApiKey/reset?id=\".concat(e)})}function Gn(e){return Object(_[\"a\"])({method:\"delete\",url:\"/api/userApiKey/delete?id=\".concat(e)})}function Wn(e){var t=e.userId,n=e.app,a=e.enable,c=e.expiresAt,o=e.remark;return Object(_[\"a\"])({method:\"post\",url:\"/api/userApiKey/add\",params:{userId:t,app:n,enable:a,expiresAt:c,remark:o}})}var Nn={remark:function(e,t){e.commit;return new Promise((function(e,n){Dn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryList:function(e,t){e.commit;return new Promise((function(e,n){Bn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},enable:function(e,t){e.commit;return new Promise((function(e,n){An(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},disable:function(e,t){e.commit;return new Promise((function(e,n){$n(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},reset:function(e,t){e.commit;return new Promise((function(e,n){Rn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},remove:function(e,t){e.commit;return new Promise((function(e,n){Gn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},add:function(e,t){e.commit;return new Promise((function(e,n){Wn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},Fn={namespaced:!0,actions:Nn};function Xn(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/gb_record/query/\"+n+\"/\"+a+\"?startTime=\"+c+\"&endTime=\"+o})}function Kn(e){var t=Object(G[\"a\"])(e,5),n=t[0],a=t[1],c=t[2],o=t[3],i=t[4];return Object(_[\"a\"])({url:\"/api/gb_record/download/start/\"+n+\"/\"+a+\"?startTime=\"+c+\"&endTime=\"+o+\"&downloadSpeed=\"+i})}function Un(e,t,n){return Object(_[\"a\"])({method:\"get\",url:\"/api/gb_record/download/stop/\"+e+\"/\"+t+\"/\"+n})}function Jn(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/gb_record/download/progress/\".concat(n,\"/\").concat(a,\"/\").concat(c)})}var Yn={query:function(e,t){e.commit;return new Promise((function(e,n){Xn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},startDownLoad:function(e,t){e.commit;return new Promise((function(e,n){Kn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},stopDownLoad:function(e,t,n,a){e.commit;return new Promise((function(e,c){Un(t,n,a).then((function(t){var n=t.data;e(n)})).catch((function(e){c(e)}))}))},queryDownloadProgress:function(e,t){e.commit;return new Promise((function(e,n){Jn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},Qn={namespaced:!0,actions:Yn};function Zn(e){var t=e.query,n=e.startTime,a=e.endTime;return Object(_[\"a\"])({method:\"get\",url:\"/api/log/list\",params:{query:t,startTime:n,endTime:a}})}var ea={queryList:function(e,t){e.commit;return new Promise((function(e,n){Zn(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},ta={namespaced:!0,actions:ea};function na(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/scan/set/speed/\".concat(n,\"/\").concat(a),params:{scanId:c,speed:o}})}function aa(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/scan/set/left/\".concat(n,\"/\").concat(a),params:{scanId:c}})}function ca(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/scan/set/right/\".concat(n,\"/\").concat(a),params:{scanId:c}})}function oa(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/scan/start/\".concat(n,\"/\").concat(a),params:{scanId:c}})}function ia(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/scan/stop/\".concat(n,\"/\").concat(a),params:{scanId:c}})}function ra(e){var t=Object(G[\"a\"])(e,2),n=t[0],a=t[1];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/preset/query/\".concat(n,\"/\").concat(a)})}function ua(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/point/add/\".concat(n,\"/\").concat(a),params:{cruiseId:c,presetId:o}})}function sa(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/point/delete/\".concat(n,\"/\").concat(a),params:{cruiseId:c,presetId:o}})}function da(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/speed/\".concat(n,\"/\").concat(a),params:{cruiseId:c,speed:o}})}function la(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/time/\".concat(n,\"/\").concat(a),params:{cruiseId:c,time:o}})}function fa(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/start/\".concat(n,\"/\").concat(a),params:{cruiseId:c}})}function ma(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/cruise/stop/\".concat(n,\"/\").concat(a),params:{cruiseId:c}})}function ha(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/preset/add/\".concat(n,\"/\").concat(a),params:{presetId:c}})}function pa(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/preset/call/\".concat(n,\"/\").concat(a),params:{presetId:c}})}function va(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/preset/delete/\".concat(n,\"/\").concat(a),params:{presetId:c}})}function wa(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/auxiliary/\".concat(n,\"/\").concat(a),params:{command:c,switchId:o}})}function ba(e){var t=Object(G[\"a\"])(e,3),n=t[0],a=t[1],c=t[2];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/wiper/\".concat(n,\"/\").concat(a),params:{command:c}})}function ga(e){var t=Object(G[\"a\"])(e,6),n=t[0],a=t[1],c=t[2],o=t[3],i=t[4],r=t[5];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/ptz/\".concat(n,\"/\").concat(a),params:{command:c,horizonSpeed:o,verticalSpeed:i,zoomSpeed:r}})}function ya(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/fi/iris/\".concat(n,\"/\").concat(a),params:{command:c,speed:o}})}function Oa(e){var t=Object(G[\"a\"])(e,4),n=t[0],a=t[1],c=t[2],o=t[3];return Object(_[\"a\"])({method:\"get\",url:\"/api/front-end/fi/focus/\".concat(n,\"/\").concat(a),params:{command:c,speed:o}})}var Pa={setSpeedForScan:function(e,t){e.commit;return new Promise((function(e,n){na(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},setLeftForScan:function(e,t){e.commit;return new Promise((function(e,n){aa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},setRightForScan:function(e,t){e.commit;return new Promise((function(e,n){ca(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},startScan:function(e,t){e.commit;return new Promise((function(e,n){oa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},stopScan:function(e,t){e.commit;return new Promise((function(e,n){ia(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addPointForCruise:function(e,t){e.commit;return new Promise((function(e,n){ua(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deletePointForCruise:function(e,t){e.commit;return new Promise((function(e,n){sa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},setCruiseSpeed:function(e,t){e.commit;return new Promise((function(e,n){da(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},setCruiseTime:function(e,t){e.commit;return new Promise((function(e,n){la(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},startCruise:function(e,t){e.commit;return new Promise((function(e,n){fa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},stopCruise:function(e,t){e.commit;return new Promise((function(e,n){ma(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},addPreset:function(e,t){e.commit;return new Promise((function(e,n){ha(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},queryPreset:function(e,t){e.commit;return new Promise((function(e,n){ra(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},callPreset:function(e,t){e.commit;return new Promise((function(e,n){pa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},deletePreset:function(e,t){e.commit;return new Promise((function(e,n){va(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},auxiliary:function(e,t){e.commit;return new Promise((function(e,n){wa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},wiper:function(e,t){e.commit;return new Promise((function(e,n){ba(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},ptz:function(e,t){e.commit;return new Promise((function(e,n){ga(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},iris:function(e,t){e.commit;return new Promise((function(e,n){ya(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))},focus:function(e,t){e.commit;return new Promise((function(e,n){Oa(t).then((function(t){var n=t.data;e(n)})).catch((function(e){n(e)}))}))}},ja={namespaced:!0,actions:Pa};a[\"default\"].use(c[\"a\"]);var xa=new c[\"a\"].Store({modules:{app:f,settings:P,user:$,tagsView:K,commonChanel:be,region:Ve,device:Je,group:at,server:bt,play:xt,playback:Tt,streamPush:$t,streamProxy:Yt,recordPlan:rn,cloudRecord:gn,platform:En,role:qn,userApiKeys:Fn,gbRecord:Qn,log:ta,frontEnd:ja},getters:i});t[\"a\"]=xa},\"47f1\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-table\",use:\"icon-table-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-table\"><path d=\"M.006.064h127.988v31.104H.006V.064zm0 38.016h38.396v41.472H.006V38.08zm0 48.384h38.396v41.472H.006V86.464zM44.802 38.08h38.396v41.472H44.802V38.08zm0 48.384h38.396v41.472H44.802V86.464zM89.598 38.08h38.396v41.472H89.598zm0 48.384h38.396v41.472H89.598z\" /><path d=\"M.006.064h127.988v31.104H.006V.064zm0 38.016h38.396v41.472H.006V38.08zm0 48.384h38.396v41.472H.006V86.464zM44.802 38.08h38.396v41.472H44.802V38.08zm0 48.384h38.396v41.472H44.802V86.464zM89.598 38.08h38.396v41.472H89.598zm0 48.384h38.396v41.472H89.598z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"4b0f\":function(e,t,n){var a=n(\"6374\").default,c=n(\"448a\").default;n(\"99af\"),n(\"b64b\"),n(\"4d63\"),n(\"ac1f\"),n(\"2c3e\"),n(\"25f0\");var o=n(\"96eb\"),i=n(\"8a60\"),r=i.param2Obj,u=n(\"331a\"),s=n(\"a0bc\"),d=[].concat(c(u),c(s));function l(){function e(e){return function(t){var n=null;if(e instanceof Function){var a=t.body,c=t.type,i=t.url;n=e({method:c,body:JSON.parse(a),query:r(i)})}else n=e;return o.mock(n)}}o.XHR.prototype.proxy_send=o.XHR.prototype.send,o.XHR.prototype.send=function(){this.custom.xhr&&(this.custom.xhr.withCredentials=this.withCredentials||!1,this.responseType&&(this.custom.xhr.responseType=this.responseType)),this.proxy_send.apply(this,arguments)};var t,n=a(d);try{for(n.s();!(t=n.n()).done;){var c=t.value;o.mock(new RegExp(c.url),c.type||\"get\",e(c.response))}}catch(i){n.e(i)}finally{n.f()}}e.exports={mocks:d,mockXHR:l}},\"4d24\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-streamPush\",use:\"icon-streamPush-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-streamPush\"><path d=\"M1009.886 451.873c-14.958-29.869-51.485-42.159-81.4-27.088l-98.957 49.464v-88.568c0-33.472-27.225-60.697-60.697-60.697H680.64c17.873-26.677 28.362-58.704 28.362-93.165 0-92.573-75.289-167.862-167.839-167.862-92.573 0-167.862 75.289-167.862 167.862 0 34.461 10.489 66.488 28.362 93.165H350.5c6.672-15.237 10.489-31.987 10.489-49.661 0-68.586-55.772-124.357-124.357-124.357s-124.357 55.772-124.357 124.357c0 17.674 3.819 34.424 10.491 49.661H68.381c-33.472 0-60.697 27.225-60.697 60.697v513.664c0 33.472 27.225 60.697 60.697 60.697h700.451c33.472 0 60.697-27.225 60.697-60.697v-70.729l98.775 49.387c8.345 4.241 17.831 6.476 27.316 6.476 33.472 0 60.697-27.225 60.697-60.697V479.075c-0.001-9.257-2.19-18.583-6.431-27.202zM447.997 231.82c0-51.371 41.794-93.165 93.165-93.165s93.143 41.794 93.143 93.165-41.772 93.165-93.143 93.165-93.165-41.794-93.165-93.165zM186.97 275.324c0-27.384 22.277-49.661 49.661-49.661s49.661 22.277 49.661 49.661-22.277 49.661-49.661 49.661-49.661-22.277-49.661-49.661z m567.862 610.022H82.381V399.682h672.451v485.664z m186.787-84.228l-112.09-56V557.785l112.09-56.045v299.378z\" p-id=\"1471\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"4df5\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-eye\",use:\"icon-eye-usage\",viewBox:\"0 0 128 64\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 64\" id=\"icon-eye\"><path d=\"M127.072 7.994c1.37-2.208.914-5.152-.914-6.87-2.056-1.717-4.797-1.226-6.396.982-.229.245-25.586 32.382-55.74 32.382-29.24 0-55.74-32.382-55.968-32.627-1.6-1.963-4.57-2.208-6.397-.49C-.17 3.086-.399 6.275 1.2 8.238c.457.736 5.94 7.36 14.62 14.72L4.17 35.96c-1.828 1.963-1.6 5.152.228 6.87.457.98 1.6 1.471 2.742 1.471s2.284-.49 3.198-1.472l12.564-13.983c5.94 4.416 13.021 8.587 20.788 11.53l-4.797 17.418c-.685 2.699.686 5.397 3.198 6.133h1.37c2.057 0 3.884-1.472 4.341-3.68L52.6 42.83c3.655.736 7.538 1.227 11.422 1.227 3.883 0 7.767-.49 11.422-1.227l4.797 17.173c.457 2.208 2.513 3.68 4.34 3.68.457 0 .914 0 1.143-.246 2.513-.736 3.883-3.434 3.198-6.133l-4.797-17.172c7.767-2.944 14.848-7.114 20.788-11.53l12.336 13.738c.913.981 2.056 1.472 3.198 1.472s2.284-.49 3.198-1.472c1.828-1.963 1.828-4.906.228-6.87l-11.65-13.001c9.366-7.36 14.849-14.474 14.849-14.474z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"4f20\":function(e,t,n){},5:function(e,t){},\"51ff\":function(e,t,n){var a={\"./captcha.svg\":\"08e8\",\"./channelManger.svg\":\"0ea9\",\"./cloudRecord.svg\":\"d604\",\"./dashboard.svg\":\"f782\",\"./device.svg\":\"2c87\",\"./example.svg\":\"30c3\",\"./eye-open.svg\":\"d7ec\",\"./eye.svg\":\"4df5\",\"./form.svg\":\"eb1b\",\"./group.svg\":\"a75f\",\"./historyLog.svg\":\"03ed\",\"./link.svg\":\"18f0\",\"./live.svg\":\"a8d9\",\"./mediaServerList.svg\":\"c742\",\"./nested.svg\":\"dcf8\",\"./operations.svg\":\"b21d\",\"./password.svg\":\"2a3d\",\"./platform.svg\":\"b9f6\",\"./realLog.svg\":\"1515\",\"./recordPlan.svg\":\"aa0f\",\"./region.svg\":\"cd5e\",\"./setting.svg\":\"05ff\",\"./streamProxy.svg\":\"c8e4\",\"./streamPush.svg\":\"4d24\",\"./systemInfo.svg\":\"ebb8\",\"./table.svg\":\"47f1\",\"./tree.svg\":\"93cd\",\"./user.svg\":\"b3b5\"};function c(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error(\"Cannot find module '\"+e+\"'\");throw t.code=\"MODULE_NOT_FOUND\",t}return a[e]}c.keys=function(){return Object.keys(a)},c.resolve=o,e.exports=c,c.id=\"51ff\"},\"56d7\":function(e,t,n){\"use strict\";n.r(t);n(\"e260\"),n(\"e6cf\"),n(\"cca6\"),n(\"a79d\");var a=n(\"2b0e\"),c=(n(\"caad\"),n(\"a15b\"),n(\"d3b7\"),n(\"ac1f\"),n(\"00b4\"),n(\"2532\"),{fetch:null,XMLHttpRequest:null,console:{log:null,warn:null,error:null,info:null,debug:null}}),o=!1;function i(e){return\"string\"===typeof e&&(e.includes(\"39.106.146.94:10001/eye/user/anchor/anchor\")||e.includes(\"anchor\")&&e.includes(\"type=info\")&&e.includes(\"app=h265web\")||e.includes(\"h265web\")&&e.includes(\"type=info\")||e.includes(\"action=build\")||e.includes(\"page=player\")||e.includes(\"block=player\"))}function r(e){return\"h265web.js loaded!\"===e||e.includes(\"build date:\")||e.includes(\"version: 1.0.1\")||/^version:\\s*\\d+\\.\\d+\\.\\d+/.test(e)||e.includes(\"/*********************************************************\")||e.includes(\"**********************************************************/\")||e.includes(\"_     ___   __ _____             _      _\")||e.includes(\"| |   |__ \\\\ / /| ____|           | |    (_)\")||e.includes(\"| |__    ) / /_| |____      _____| |__   _ ___\")||e.includes(\"| '_ \\\\  / / '_ \\\\___ \\\\ \\\\ /\\\\ / / _ \\\\ '_ \\\\ | / __|\")||e.includes(\"| | | |/ /| (_) |__) \\\\ V  V /  __/ |_) || \\\\__ \\\\\")||e.includes(\"|_| |_|____\\\\___/____/ \\\\_/\\\\_/ \\\\___|_.__(_) |___/\")||e.includes(\"                                        _/ |\")||e.includes(\"                                       |__/\")||/^\\s*_\\s+___\\s+__\\s+_____/.test(e)||/^\\s*\\|\\s+\\|\\s+\\|__\\s+\\\\/.test(e)||/^\\s*\\|\\s+\\|__\\s+\\)/.test(e)||/^\\s*\\|\\s+'_\\s+\\\\/.test(e)||/^\\s*\\|\\s+\\|\\s+\\|\\s+\\//.test(e)||/^\\s*\\|_\\|\\s+\\|____/.test(e)||/^\\s*_\\/\\s+\\|/.test(e)||/^\\s*\\|__\\//.test(e)||/\\|.*\\|.*\\|/.test(e)&&/_/.test(e)&&e.length>30||e.includes(\"h265web.js is permanent free & 本播放内核完全免费 可商业化!\")||e.includes(\"Author & 作者: Numberwolf - ChangYanlong\")||e.includes(\"QQ Group & 技术支持群: 925466059\")||e.includes(\"WeChat & 微信: numberwolf11\")||e.includes(\"Discord: numberwolf#8694\")||e.includes(\"Email & 邮箱: <EMAIL>\")||e.includes(\"Blog & 博客: https://www.jianshu.com/u/9c09c1e00fd1\")||e.includes(\"Github: https://github.com/numberwolf\")||e.includes(\"h265web.js: https://github.com/numberwolf/h265web.js\")||\"[v] all ok now\"===e||e.includes(\"*\")&&e.includes(\"[h265web.js]\")||e.includes(\"39.106.146.94\")&&e.includes(\"10001\")||e.includes(\"anchor?type=info\")||e.includes(\"app=h265web\")}function u(){c.fetch||(c.fetch=window.fetch),window.fetch=function(e,t){return i(e)?(console.log(\"[全局拦截器] 已拦截h265web统计请求:\",e),Promise.resolve(new Response('{\"code\":0,\"msg\":\"success\"}',{status:200,statusText:\"OK\",headers:{\"Content-Type\":\"application/json\",\"Access-Control-Allow-Origin\":\"*\"}}))):c.fetch.apply(this,arguments)}}function s(){c.XMLHttpRequest||(c.XMLHttpRequest=window.XMLHttpRequest);var e=c.XMLHttpRequest;for(var t in window.XMLHttpRequest=function(){var t=new e,n=t.open;return t.open=function(e,t,a,c,o){return i(t)&&(console.log(\"[全局拦截器] 已拦截h265web XHR统计请求:\",t),t='data:application/json,{\"code\":0,\"msg\":\"success\"}'),n.call(this,e,t,a,c,o)},t},e)if(e.hasOwnProperty(t))try{window.XMLHttpRequest[t]=e[t]}catch(n){}window.XMLHttpRequest.prototype=e.prototype}function d(){c.console.log||(c.console.log=console.log,c.console.warn=console.warn,c.console.error=console.error,c.console.info=console.info,c.console.debug=console.debug);var e=function(e,t){return function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];var c=n.join(\" \");if(!r(c))return e.apply(console,n)}};console.log=e(c.console.log,\"log\"),console.warn=e(c.console.warn,\"warn\"),console.error=e(c.console.error,\"error\"),console.info=e(c.console.info,\"info\"),console.debug=e(c.console.debug,\"debug\")}function l(){if(!o)try{u(),s(),d(),o=!0}catch(e){}}function f(){if(o)try{c.fetch&&(window.fetch=c.fetch),c.XMLHttpRequest&&(window.XMLHttpRequest=c.XMLHttpRequest),c.console.log&&(console.log=c.console.log,console.warn=c.console.warn,console.error=c.console.error,console.info=c.console.info,console.debug=c.console.debug),o=!1}catch(e){}}function m(){return{active:o,originalMethods:c}}l(),\"undefined\"!==typeof window&&(window.h265webInterceptor={start:l,stop:f,status:m});n(\"f5df1\");var h=n(\"5c96\"),p=n.n(h),v=(n(\"0fae\"),n(\"b2d6\"),n(\"b20f\"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{attrs:{id:\"app\"}},[n(\"router-view\")],1)}),w=[],b={name:\"App\",created:function(){this.initGlobalInterceptor()},mounted:function(){this.ensureInterceptorActive()},methods:{initGlobalInterceptor:function(){try{l()}catch(e){}},ensureInterceptorActive:function(){if(window.h265webInterceptor){var e=window.h265webInterceptor.status();e.active||window.h265webInterceptor.start()}}}},g=b,y=n(\"2877\"),O=Object(y[\"a\"])(g,v,w,!1,null,null,null),P=O.exports,j=n(\"4360\"),x=n(\"a18c\"),_=(n(\"d81d\"),n(\"0643\"),n(\"a573\"),n(\"ddb0\"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isExternal?n(\"div\",e._g({staticClass:\"svg-external-icon svg-icon\",style:e.styleExternalIcon},e.$listeners)):n(\"svg\",e._g({class:e.svgClass,attrs:{\"aria-hidden\":\"true\"}},e.$listeners),[n(\"use\",{attrs:{\"xlink:href\":e.iconName}})])}),k=[],I=n(\"61f7\"),V={name:\"SvgIcon\",props:{iconClass:{type:String,required:!0},className:{type:String,default:\"\"}},computed:{isExternal:function(){return Object(I[\"a\"])(this.iconClass)},iconName:function(){return\"#icon-\".concat(this.iconClass)},svgClass:function(){return this.className?\"svg-icon \"+this.className:\"svg-icon\"},styleExternalIcon:function(){return{mask:\"url(\".concat(this.iconClass,\") no-repeat 50% 50%\"),\"-webkit-mask\":\"url(\".concat(this.iconClass,\") no-repeat 50% 50%\")}}}},C=V,S=(n(\"2b29\"),Object(y[\"a\"])(C,_,k,!1,null,\"f9f7fefc\",null)),T=S.exports;a[\"default\"].component(\"svg-icon\",T);var L=n(\"51ff\"),z=function(e){return e.keys().map(e)};z(L);var E=n(\"c14f\"),H=n(\"1da1\"),M=(n(\"b0c0\"),n(\"323e\")),q=n.n(M),D=(n(\"a5d8\"),n(\"5f87\")),B=(n(\"99af\"),n(\"83d6\")),A=n.n(B),$=A.a.title||\"视频汇聚平台\";function R(e){return e?\"\".concat(e,\" - \").concat($):\"\".concat($)}q.a.configure({showSpinner:!1});var G=[\"/login\"];x[\"a\"].beforeEach(function(){var e=Object(H[\"a\"])(Object(E[\"a\"])().m((function e(t,n,a){var c,o;return Object(E[\"a\"])().w((function(e){while(1)switch(e.n){case 0:q.a.start(),document.title=R(t.meta.title),c=Object(D[\"c\"])(),c?\"/login\"===t.path?(a({path:\"/\"}),q.a.done()):(o=j[\"a\"].getters.name,o||(j[\"a\"].commit(\"user/SET_NAME\",Object(D[\"a\"])()),j[\"a\"].commit(\"user/SET_SERVER_ID\",Object(D[\"b\"])())),a()):-1!==G.indexOf(t.path)?a():(a(\"/login?redirect=\".concat(t.path)),q.a.done());case 1:return e.a(2)}}),e)})));return function(t,n,a){return e.apply(this,arguments)}}()),x[\"a\"].afterEach((function(){q.a.done()}));var W=n(\"af88\"),N=n(\"e5d9\"),F=n(\"4eb5\"),X=n.n(F),K=n(\"4b0f\"),U=K.mockXHR;U(),a[\"default\"].use(p.a),a[\"default\"].use(W[\"a\"]),a[\"default\"].use(N[\"a\"]),a[\"default\"].use(X.a),a[\"default\"].config.productionTip=!1,a[\"default\"].prototype.$channelTypeList={1:{id:1,name:\"国标设备\",style:{color:\"#409eff\",borderColor:\"#b3d8ff\"}},2:{id:2,name:\"推流设备\",style:{color:\"#67c23a\",borderColor:\"#c2e7b0\"}},3:{id:3,name:\"拉流代理\",style:{color:\"#e6a23c\",borderColor:\"#f5dab1\"}}},new a[\"default\"]({el:\"#app\",router:x[\"a\"],store:j[\"a\"],render:function(e){return e(P)}})},\"59d0\":function(e,t,n){\"use strict\";n(\"951a\")},\"5f87\":function(e,t,n){\"use strict\";n.d(t,\"c\",(function(){return u})),n.d(t,\"i\",(function(){return s})),n.d(t,\"f\",(function(){return d})),n.d(t,\"a\",(function(){return l})),n.d(t,\"g\",(function(){return f})),n.d(t,\"d\",(function(){return m})),n.d(t,\"b\",(function(){return h})),n.d(t,\"h\",(function(){return p})),n.d(t,\"e\",(function(){return v}));var a=n(\"a78e\"),c=n.n(a),o=\"wvp_token\",i=\"wvp_username\",r=\"wvp_server_id\";function u(){return console.log(\"Getting token...\"),c.a.get(o)}function s(e){return c.a.set(o,e)}function d(){return c.a.remove(o)}function l(){return c.a.get(i)}function f(e){return c.a.set(i,e)}function m(){return c.a.remove(i)}function h(){return c.a.get(r)}function p(e){return c.a.set(r,e)}function v(){return c.a.remove(r)}},6:function(e,t){},\"61f7\":function(e,t,n){\"use strict\";n.d(t,\"a\",(function(){return a})),n.d(t,\"b\",(function(){return c}));n(\"ac1f\"),n(\"00b4\");function a(e){return/^(https?:|mailto:|tel:)/.test(e)}function c(e){return e.length>=0}},7:function(e,t){},8:function(e,t){},\"83d6\":function(e,t){e.exports={title:\"视频汇聚平台\",fixedHeader:!1,sidebarLogo:!1,tagsView:!0}},\"88b2\":function(e,t,n){\"use strict\";n(\"3ecb\")},\"8a60\":function(e,t,n){function a(e){var t=decodeURIComponent(e.split(\"?\")[1]).replace(/\\+/g,\" \");if(!t)return{};var n={},a=t.split(\"&\");return a.forEach((function(e){var t=e.indexOf(\"=\");if(-1!==t){var a=e.substring(0,t),c=e.substring(t+1,e.length);n[a]=c}})),n}n(\"d3b7\"),n(\"ac1f\"),n(\"5319\"),n(\"0643\"),n(\"4e3e\"),n(\"159b\"),e.exports={param2Obj:a}},\"8dd0\":function(e,t,n){\"use strict\";n(\"c459\")},9:function(e,t){},\"93cd\":function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-tree\",use:\"icon-tree-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-tree\"><path d=\"M126.713 90.023c.858.985 1.287 2.134 1.287 3.447v29.553c0 1.423-.429 2.6-1.287 3.53-.858.93-1.907 1.395-3.146 1.395H97.824c-1.145 0-2.146-.465-3.004-1.395-.858-.93-1.287-2.107-1.287-3.53V93.47c0-.875.19-1.696.572-2.462.382-.766.906-1.368 1.573-1.806a3.84 3.84 0 0 1 2.146-.657h9.725V69.007a3.84 3.84 0 0 0-.43-1.806 3.569 3.569 0 0 0-1.143-1.313 2.714 2.714 0 0 0-1.573-.492h-36.47v23.149h9.725c1.144 0 2.145.492 3.004 1.478.858.985 1.287 2.134 1.287 3.447v29.553c0 .876-.191 1.696-.573 2.463-.38.766-.905 1.368-1.573 1.806a3.84 3.84 0 0 1-2.145.656H51.915a3.84 3.84 0 0 1-2.145-.656c-.668-.438-1.216-1.04-1.645-1.806a4.96 4.96 0 0 1-.644-2.463V93.47c0-1.313.43-2.462 1.288-3.447.858-.986 1.907-1.478 3.146-1.478h9.582v-23.15h-37.9c-.953 0-1.74.356-2.359 1.068-.62.711-.93 1.56-.93 2.544v19.538h9.726c1.239 0 2.264.492 3.074 1.478.81.985 1.216 2.134 1.216 3.447v29.553c0 1.423-.405 2.6-1.216 3.53-.81.93-1.835 1.395-3.074 1.395H4.29c-.476 0-.93-.082-1.358-.246a4.1 4.1 0 0 1-1.144-.657 4.658 4.658 0 0 1-.93-1.067 5.186 5.186 0 0 1-.643-1.395 5.566 5.566 0 0 1-.215-1.56V93.47c0-.437.048-.875.143-1.313a3.95 3.95 0 0 1 .429-1.15c.19-.328.429-.656.715-.984.286-.329.572-.602.858-.821.286-.22.62-.383 1.001-.493.382-.11.763-.164 1.144-.164h9.726V61.619c0-.985.31-1.833.93-2.544.619-.712 1.358-1.068 2.216-1.068h44.335V39.62h-9.582c-1.24 0-2.288-.492-3.146-1.477a5.09 5.09 0 0 1-1.287-3.448V5.14c0-1.423.429-2.627 1.287-3.612.858-.985 1.907-1.477 3.146-1.477h25.743c.763 0 1.478.246 2.145.739a5.17 5.17 0 0 1 1.573 1.888c.382.766.573 1.587.573 2.462v29.553c0 1.313-.43 2.463-1.287 3.448-.859.985-1.86 1.477-3.004 1.477h-9.725v18.389h42.762c.954 0 1.74.355 2.36 1.067.62.711.93 1.56.93 2.545v26.925h9.582c1.239 0 2.288.492 3.146 1.478z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},\"951a\":function(e,t,n){},\"9d71\":function(e,t,n){\"use strict\";n(\"4f20\")},a0bc:function(e,t,n){var a=n(\"96eb\"),c=a.mock({\"items|30\":[{id:\"@id\",title:\"@sentence(10, 20)\",\"status|1\":[\"published\",\"draft\",\"deleted\"],author:\"name\",display_time:\"@datetime\",pageviews:\"@integer(300, 5000)\"}]});e.exports=[{url:\"/vue-admin-template/table/list\",type:\"get\",response:function(e){var t=c.items;return{code:2e4,data:{total:t.length,items:t}}}}]},a18c:function(e,t,n){\"use strict\";n.d(t,\"b\",(function(){return tt}));n(\"d3b7\"),n(\"3ca3\"),n(\"ddb0\");var a,c,o=n(\"2b0e\"),i=n(\"8c4f\"),r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"app-wrapper\",class:e.classObj},[\"mobile\"===e.device&&e.sidebar.opened?n(\"div\",{staticClass:\"drawer-bg\",on:{click:e.handleClickOutside}}):e._e(),n(\"sidebar\",{staticClass:\"sidebar-container\"}),n(\"div\",{staticClass:\"main-container\"},[n(\"div\",{class:{\"fixed-header\":e.fixedHeader}},[n(\"navbar\")],1),n(\"tags-View\"),n(\"app-main\")],1)],1)},u=[],s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"navbar\"},[n(\"hamburger\",{staticClass:\"hamburger-container\",attrs:{\"is-active\":e.sidebar.opened},on:{toggleClick:e.toggleSideBar}}),n(\"breadcrumb\",{staticClass:\"breadcrumb-container\"}),n(\"div\",{staticClass:\"right-menu\"},[n(\"el-dropdown\",{staticClass:\"avatar-container\",attrs:{trigger:\"click\"}},[n(\"div\",{staticClass:\"avatar-wrapper\"},[e._v(\" 欢迎，\"+e._s(e.name)+\" \"),n(\"i\",{staticClass:\"el-icon-caret-bottom\"})]),n(\"el-dropdown-menu\",{staticClass:\"user-dropdown\",attrs:{slot:\"dropdown\"},slot:\"dropdown\"},[n(\"el-dropdown-item\",{nativeOn:{click:function(t){return e.changePassword(t)}}},[n(\"span\",{staticStyle:{display:\"block\"}},[e._v(\"修改密码\")])]),n(\"el-dropdown-item\",{nativeOn:{click:function(t){return e.logout(t)}}},[n(\"span\",{staticStyle:{display:\"block\"}},[e._v(\"注销\")])])],1)],1)],1),n(\"changePasswordDialog\",{ref:\"changePasswordDialog\"})],1)},d=[],l=n(\"c14f\"),f=n(\"1da1\"),m=n(\"5530\"),h=n(\"2f62\"),p=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"el-breadcrumb\",{staticClass:\"app-breadcrumb\",attrs:{separator:\"/\"}},[n(\"transition-group\",{attrs:{name:\"breadcrumb\"}},e._l(e.levelList,(function(t,a){return n(\"el-breadcrumb-item\",{key:t.path},[\"noRedirect\"===t.redirect||a==e.levelList.length-1?n(\"span\",{staticClass:\"no-redirect\"},[e._v(e._s(t.meta.title))]):n(\"a\",{on:{click:function(n){return n.preventDefault(),e.handleLink(t)}}},[e._v(e._s(t.meta.title))])])})),1)],1)},v=[],w=(n(\"99af\"),n(\"4de4\"),n(\"b0c0\"),n(\"498a\"),n(\"0643\"),n(\"2382\"),n(\"bd11\")),b=n.n(w),g={data:function(){return{levelList:null}},watch:{$route:function(){this.getBreadcrumb()}},created:function(){this.getBreadcrumb()},methods:{getBreadcrumb:function(){var e=this.$route.matched.filter((function(e){return e.meta&&e.meta.title})),t=e[0];this.isDashboard(t)||(e=[{path:\"/dashboard\",meta:{title:\"控制台\"}}].concat(e)),this.levelList=e.filter((function(e){return e.meta&&e.meta.title&&!1!==e.meta.breadcrumb}))},isDashboard:function(e){var t=e&&e.name;return!!t&&t.trim().toLocaleLowerCase()===\"控制台\".toLocaleLowerCase()},pathCompile:function(e){var t=this.$route.params,n=b.a.compile(e);return n(t)},handleLink:function(e){var t=e.redirect,n=e.path;t?this.$router.push(t):this.$router.push(this.pathCompile(n))}}},y=g,O=(n(\"153b\"),n(\"2877\")),P=Object(O[\"a\"])(y,p,v,!1,null,\"3253c438\",null),j=P.exports,x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticStyle:{padding:\"0 15px\"},on:{click:e.toggleClick}},[n(\"svg\",{staticClass:\"hamburger\",class:{\"is-active\":e.isActive},attrs:{viewBox:\"0 0 1024 1024\",xmlns:\"http://www.w3.org/2000/svg\",width:\"64\",height:\"64\"}},[n(\"path\",{attrs:{d:\"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z\"}})])])},_=[],k={name:\"Hamburger\",props:{isActive:{type:Boolean,default:!1}},methods:{toggleClick:function(){this.$emit(\"toggleClick\")}}},I=k,V=(n(\"8dd0\"),Object(O[\"a\"])(I,x,_,!1,null,\"49e15297\",null)),C=V.exports,S=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.isLoging,expression:\"isLoging\"}],attrs:{id:\"changePassword\"}},[n(\"el-dialog\",{attrs:{title:\"修改密码\",width:\"40%\",top:\"2rem\",\"close-on-click-modal\":!1,visible:e.showDialog,\"destroy-on-close\":!0},on:{\"update:visible\":function(t){e.showDialog=t},close:function(t){return e.close()}}},[n(\"div\",{staticStyle:{\"margin-right\":\"20px\"},attrs:{id:\"shared\"}},[n(\"el-form\",{ref:\"passwordForm\",attrs:{rules:e.rules,\"status-icon\":\"\",\"label-width\":\"80px\"}},[n(\"el-form-item\",{attrs:{label:\"旧密码\",prop:\"oldPassword\"}},[n(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.oldPassword,callback:function(t){e.oldPassword=t},expression:\"oldPassword\"}})],1),n(\"el-form-item\",{attrs:{label:\"新密码\",prop:\"newPassword\"}},[n(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.newPassword,callback:function(t){e.newPassword=t},expression:\"newPassword\"}})],1),n(\"el-form-item\",{attrs:{label:\"确认密码\",prop:\"confirmPassword\"}},[n(\"el-input\",{attrs:{autocomplete:\"off\"},model:{value:e.confirmPassword,callback:function(t){e.confirmPassword=t},expression:\"confirmPassword\"}})],1),n(\"el-form-item\",[n(\"div\",{staticStyle:{float:\"right\"}},[n(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.onSubmit}},[e._v(\"保存\")]),n(\"el-button\",{on:{click:e.close}},[e._v(\"取消\")])],1)])],1)],1)])],1)},T=[],L=n(\"1c46\"),z=n.n(L),E={name:\"ChangePassword\",props:{},data:function(){var e=this,t=function(e,t,n){\"\"===t?n(new Error(\"请输入旧密码\")):n()},n=function(t,n,a){\"\"===n?a(new Error(\"请输入新密码\")):(\"\"!==e.confirmPassword&&e.$refs.passwordForm.validateField(\"confirmPassword\"),a())},a=function(t,n,a){\"\"===e.confirmPassword?a(new Error(\"请再次输入密码\")):e.confirmPassword!==e.newPassword?a(new Error(\"两次输入密码不一致!\")):a()};return{oldPassword:null,newPassword:null,confirmPassword:null,showDialog:!1,callback:null,isLoging:!1,rules:{oldPassword:[{required:!0,validator:t,trigger:\"blur\"}],newPassword:[{required:!0,validator:n,trigger:\"blur\"},{pattern:/^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[~!@#$%^&*()_+`\\-={}:\";'<>?,.\\/]).{8,20}$/,message:\"密码长度在8-20位之间,由字母+数字+特殊字符组成\"}],confirmPassword:[{required:!0,validator:a,trigger:\"blur\"}]}}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.callback=e},onSubmit:function(){var e=this;this.$store.dispatch(\"user/changePassword\",{oldPassword:z.a.createHash(\"md5\").update(this.oldPassword,\"utf8\").digest(\"hex\"),password:this.newPassword}).then((function(t){e.$message({showClose:!0,message:\"修改成功，请重新登录\",type:\"success\"}),e.showDialog=!1,e.callback&&e.callback()})).catch((function(e){console.error(e)}))},close:function(){this.showDialog=!1,this.oldPassword=null,this.newPassword=null,this.confirmPassword=null}}},H=E,M=Object(O[\"a\"])(H,S,T,!1,null,null,null),q=M.exports,D={components:{Breadcrumb:j,Hamburger:C,changePasswordDialog:q},computed:Object(m[\"a\"])({},Object(h[\"b\"])([\"sidebar\",\"name\"])),methods:{toggleSideBar:function(){this.$store.dispatch(\"app/toggleSideBar\")},logout:function(){var e=this;return Object(f[\"a\"])(Object(l[\"a\"])().m((function t(){return Object(l[\"a\"])().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch(\"user/logout\");case 1:console.log(\"logout\"),e.$router.push(\"/login?redirect=\".concat(e.$route.fullPath));case 2:return t.a(2)}}),t)})))()},changePassword:function(){this.$refs.changePasswordDialog.openDialog(this.logout)}}},B=D,A=(n(\"d749\"),Object(O[\"a\"])(B,s,d,!1,null,\"ca0a306e\",null)),$=A.exports,R=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{class:{\"has-logo\":e.showLogo}},[e.showLogo?n(\"logo\",{attrs:{collapse:e.isCollapse}}):e._e(),n(\"el-scrollbar\",{attrs:{\"wrap-class\":\"scrollbar-wrapper\"}},[n(\"el-menu\",{attrs:{\"default-active\":e.activeMenu,collapse:e.isCollapse,\"background-color\":e.variables.menuBg,\"text-color\":e.variables.menuText,\"unique-opened\":!1,\"active-text-color\":e.variables.menuActiveText,\"collapse-transition\":!1,mode:\"vertical\"}},e._l(e.routes,(function(e){return n(\"sidebar-item\",{key:e.path,attrs:{item:e,\"base-path\":e.path}})})),1)],1)],1)},G=[],W=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"sidebar-logo-container\",class:{collapse:e.collapse}},[n(\"transition\",{attrs:{name:\"sidebarLogoFade\"}},[e.collapse?n(\"router-link\",{key:\"collapse\",staticClass:\"sidebar-logo-link\",attrs:{to:\"/\"}},[e.logo?n(\"img\",{staticClass:\"sidebar-logo\",attrs:{src:e.logo}}):n(\"h1\",{staticClass:\"sidebar-title\"},[e._v(e._s(e.title)+\" \")])]):n(\"router-link\",{key:\"expand\",staticClass:\"sidebar-logo-link\",attrs:{to:\"/\"}},[e.logo?n(\"img\",{staticClass:\"sidebar-logo\",attrs:{src:e.logo}}):e._e(),n(\"h1\",{staticClass:\"sidebar-title\"},[e._v(e._s(e.title)+\" \")])])],1)],1)},N=[],F={name:\"SidebarLogo\",props:{collapse:{type:Boolean,required:!0}},data:function(){return{title:\"视频汇聚平台\",logo:\"https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png\"}}},X=F,K=(n(\"a94c\"),Object(O[\"a\"])(X,W,N,!1,null,\"2e0065ce\",null)),U=K.exports,J=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.item.hidden?e._e():n(\"div\",[!e.hasOneShowingChild(e.item.children,e.item)||e.onlyOneChild.children&&!e.onlyOneChild.noShowingChildren||e.item.alwaysShow?n(\"el-submenu\",{ref:\"subMenu\",attrs:{index:e.resolvePath(e.item.path),\"popper-append-to-body\":\"\"}},[n(\"template\",{slot:\"title\"},[e.item.meta?n(\"item\",{attrs:{icon:e.item.meta&&e.item.meta.icon,title:e.item.meta.title}}):e._e()],1),e._l(e.item.children,(function(t){return n(\"sidebar-item\",{key:t.path,staticClass:\"nest-menu\",attrs:{\"is-nest\":!0,item:t,\"base-path\":e.resolvePath(t.path)}})}))],2):[e.onlyOneChild.meta?n(\"app-link\",{attrs:{to:e.resolvePath(e.onlyOneChild.path)}},[n(\"el-menu-item\",{class:{\"submenu-title-noDropdown\":!e.isNest},attrs:{index:e.resolvePath(e.onlyOneChild.path)}},[n(\"item\",{attrs:{icon:e.onlyOneChild.meta.icon||e.item.meta&&e.item.meta.icon,title:e.onlyOneChild.meta.title}})],1)],1):e._e()]],2)},Y=[],Q=n(\"df7c\"),Z=n.n(Q),ee=n(\"61f7\"),te=(n(\"caad\"),n(\"2532\"),{name:\"MenuItem\",functional:!0,props:{icon:{type:String,default:\"\"},title:{type:String,default:\"\"}},render:function(e,t){var n=t.props,a=n.icon,c=n.title,o=[];return a&&(a.includes(\"el-icon\")?o.push(e(\"i\",{class:[a,\"sub-el-icon\"]})):o.push(e(\"svg-icon\",{attrs:{\"icon-class\":a}}))),c&&o.push(e(\"span\",{slot:\"title\"},[c])),o}}),ne=te,ae=(n(\"f87e\"),Object(O[\"a\"])(ne,a,c,!1,null,\"18eeea00\",null)),ce=ae.exports,oe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.type,e._b({tag:\"component\"},\"component\",e.linkProps(e.to),!1),[e._t(\"default\")],2)},ie=[],re={props:{to:{type:String,required:!0}},computed:{isExternal:function(){return Object(ee[\"a\"])(this.to)},type:function(){return this.isExternal?\"a\":\"router-link\"}},methods:{linkProps:function(e){return this.isExternal?{href:e,target:\"_blank\",rel:\"noopener\"}:{to:e}}}},ue=re,se=Object(O[\"a\"])(ue,oe,ie,!1,null,null,null),de=se.exports,le={computed:{device:function(){return this.$store.state.app.device}},mounted:function(){this.fixBugIniOS()},methods:{fixBugIniOS:function(){var e=this,t=this.$refs.subMenu;if(t){var n=t.handleMouseleave;t.handleMouseleave=function(t){\"mobile\"!==e.device&&n(t)}}}}},fe={name:\"SidebarItem\",components:{Item:ce,AppLink:de},mixins:[le],props:{item:{type:Object,required:!0},isNest:{type:Boolean,default:!1},basePath:{type:String,default:\"\"}},data:function(){return this.onlyOneChild=null,{}},methods:{hasOneShowingChild:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0;if(n.onlyIndex>=0)return this.onlyOneChild=t[n.onlyIndex],!0;var a=t.filter((function(t){return!t.hidden&&(e.onlyOneChild=t,!0)}));return 1===a.length||0===a.length&&(this.onlyOneChild=Object(m[\"a\"])(Object(m[\"a\"])({},n),{},{path:\"\",noShowingChildren:!0}),!0)},resolvePath:function(e){return Object(ee[\"a\"])(e)?e:Object(ee[\"a\"])(this.basePath)?this.basePath:Z.a.resolve(this.basePath,e)}}},me=fe,he=Object(O[\"a\"])(me,J,Y,!1,null,null,null),pe=he.exports,ve=n(\"cf1e\"),we=n.n(ve),be={components:{SidebarItem:pe,Logo:U},computed:Object(m[\"a\"])(Object(m[\"a\"])({},Object(h[\"b\"])([\"sidebar\"])),{},{routes:function(){return this.$router.options.routes},activeMenu:function(){var e=this.$route,t=e.meta,n=e.path;return t.activeMenu?t.activeMenu:n},showLogo:function(){return this.$store.state.settings.sidebarLogo},variables:function(){return we.a},isCollapse:function(){return!this.sidebar.opened}})},ge=be,ye=Object(O[\"a\"])(ge,R,G,!1,null,null,null),Oe=ye.exports,Pe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"section\",{staticClass:\"app-main\"},[n(\"transition\",{attrs:{name:\"fade-transform\",mode:\"out-in\"}},[n(\"KeepAlive\",{attrs:{include:e.cachedViews}},[n(\"router-view\",{key:e.key})],1)],1)],1)},je=[],xe={name:\"AppMain\",computed:{key:function(){return this.$route.path},cachedViews:function(){return this.$store.state.tagsView.cachedViews}}},_e=xe,ke=(n(\"88b2\"),n(\"9d71\"),Object(O[\"a\"])(_e,Pe,je,!1,null,\"3bd981b1\",null)),Ie=ke.exports,Ve=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"tags-view-container\",attrs:{id:\"tags-view-container\"}},[n(\"scroll-pane\",{ref:\"scrollPane\",staticClass:\"tags-view-wrapper\",on:{scroll:e.handleScroll}},e._l(e.visitedViews,(function(t){return n(\"router-link\",{key:t.path,ref:\"tag\",refInFor:!0,staticClass:\"tags-view-item\",class:e.isActive(t)?\"active\":\"\",attrs:{to:{path:t.path,query:t.query,fullPath:t.fullPath},tag:\"span\"},nativeOn:{mouseup:function(n){if(\"button\"in n&&1!==n.button)return null;!e.isAffix(t)&&e.closeSelectedTag(t)},contextmenu:function(n){return n.preventDefault(),e.openMenu(t,n)}}},[e._v(\" \"+e._s(t.title)+\" \"),e.isAffix(t)?e._e():n(\"span\",{staticClass:\"el-icon-close\",on:{click:function(n){return n.preventDefault(),n.stopPropagation(),e.closeSelectedTag(t)}}})])})),1),n(\"ul\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.visible,expression:\"visible\"}],staticClass:\"contextmenu\",style:{left:e.left+\"px\",top:e.top+\"px\"}},[n(\"li\",{on:{click:function(t){return e.refreshSelectedTag(e.selectedTag)}}},[e._v(\"Refresh\")]),e.isAffix(e.selectedTag)?e._e():n(\"li\",{on:{click:function(t){return e.closeSelectedTag(e.selectedTag)}}},[e._v(\"Close\")]),n(\"li\",{on:{click:e.closeOthersTags}},[e._v(\"Close Others\")]),n(\"li\",{on:{click:function(t){return e.closeAllTags(e.selectedTag)}}},[e._v(\"Close All\")])])],1)},Ce=[],Se=n(\"b85c\"),Te=n(\"2909\"),Le=(n(\"fb6a\"),n(\"ac1f\"),n(\"5319\"),n(\"4e3e\"),n(\"9a9a\"),n(\"159b\"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"el-scrollbar\",{ref:\"scrollContainer\",staticClass:\"scroll-container\",attrs:{vertical:!1},nativeOn:{wheel:function(t){return t.preventDefault(),e.handleScroll(t)}}},[e._t(\"default\")],2)}),ze=[],Ee=(n(\"c740\"),4),He={name:\"ScrollPane\",data:function(){return{left:0}},computed:{scrollWrapper:function(){return this.$refs.scrollContainer.$refs.wrap}},mounted:function(){this.scrollWrapper.addEventListener(\"scroll\",this.emitScroll,!0)},beforeDestroy:function(){this.scrollWrapper.removeEventListener(\"scroll\",this.emitScroll)},methods:{handleScroll:function(e){var t=e.wheelDelta||40*-e.deltaY,n=this.scrollWrapper;n.scrollLeft=n.scrollLeft+t/4},emitScroll:function(){this.$emit(\"scroll\")},moveToTarget:function(e){var t=this.$refs.scrollContainer.$el,n=t.offsetWidth,a=this.scrollWrapper,c=this.$parent.$refs.tag,o=null,i=null;if(c.length>0&&(o=c[0],i=c[c.length-1]),o===e)a.scrollLeft=0;else if(i===e)a.scrollLeft=a.scrollWidth-n;else{var r=c.findIndex((function(t){return t===e})),u=c[r-1],s=c[r+1],d=s.$el.offsetLeft+s.$el.offsetWidth+Ee,l=u.$el.offsetLeft-Ee;d>a.scrollLeft+n?a.scrollLeft=d-n:l<a.scrollLeft&&(a.scrollLeft=l)}}}},Me=He,qe=(n(\"de06\"),Object(O[\"a\"])(Me,Le,ze,!1,null,\"41421bb2\",null)),De=qe.exports,Be={components:{ScrollPane:De},data:function(){return{visible:!1,top:0,left:0,selectedTag:{},affixTags:[]}},computed:{visitedViews:function(){return this.$store.state.tagsView.visitedViews}},watch:{$route:function(){this.addTags(),this.moveToCurrentTag()},visible:function(e){e?document.body.addEventListener(\"click\",this.closeMenu):document.body.removeEventListener(\"click\",this.closeMenu)}},mounted:function(){this.initTags(),this.addTags()},methods:{isActive:function(e){return e.path===this.$route.path},isAffix:function(e){return e.meta&&e.meta.affix},filterAffixTags:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"/\",a=[];return this.routes&&e.forEach((function(e){if(e.meta&&e.meta.affix){var c=Z.a.resolve(n,e.path);a.push({fullPath:c,path:c,name:e.name,meta:Object(m[\"a\"])({},e.meta)})}if(e.children){var o=t.filterAffixTags(e.children,e.path);o.length>=1&&(a=[].concat(Object(Te[\"a\"])(a),Object(Te[\"a\"])(o)))}})),a},initTags:function(){var e,t=this.affixTags=this.filterAffixTags(this.routes),n=Object(Se[\"a\"])(t);try{for(n.s();!(e=n.n()).done;){var a=e.value;a.name&&this.$store.dispatch(\"tagsView/addVisitedView\",a)}}catch(c){n.e(c)}finally{n.f()}},addTags:function(){var e=this.$route.name;return e&&this.$store.dispatch(\"tagsView/addView\",this.$route),!1},moveToCurrentTag:function(){var e=this,t=this.$refs.tag;this.$nextTick((function(){var n,a=Object(Se[\"a\"])(t);try{for(a.s();!(n=a.n()).done;){var c=n.value;if(c.to.path===e.$route.path){e.$refs.scrollPane.moveToTarget(c),c.to.fullPath!==e.$route.fullPath&&e.$store.dispatch(\"tagsView/updateVisitedView\",e.$route);break}}}catch(o){a.e(o)}finally{a.f()}}))},refreshSelectedTag:function(e){var t=this;this.$store.dispatch(\"tagsView/delCachedView\",e).then((function(){var n=e.fullPath;t.$nextTick((function(){t.$router.replace({path:\"/redirect\"+n})}))}))},closeSelectedTag:function(e){var t=this;this.$store.dispatch(\"tagsView/delView\",e).then((function(n){var a=n.visitedViews;t.isActive(e)&&t.toLastView(a,e)}))},closeOthersTags:function(){var e=this;this.$router.push(this.selectedTag),this.$store.dispatch(\"tagsView/delOthersViews\",this.selectedTag).then((function(){e.moveToCurrentTag()}))},closeAllTags:function(e){var t=this;this.$store.dispatch(\"tagsView/delAllViews\").then((function(n){var a=n.visitedViews;t.affixTags.some((function(t){return t.path===e.path}))||t.toLastView(a,e)}))},toLastView:function(e,t){var n=e.slice(-1)[0];n?this.$router.push(n.fullPath):\"Dashboard\"===t.name?this.$router.replace({path:\"/redirect\"+t.fullPath}):this.$router.push(\"/\")},openMenu:function(e,t){var n=105,a=this.$el.getBoundingClientRect().left,c=this.$el.offsetWidth,o=c-n,i=t.clientX-a+15;this.left=i>o?o:i,this.top=t.clientY,this.visible=!0,this.selectedTag=e},closeMenu:function(){this.visible=!1},handleScroll:function(){this.closeMenu()}}},Ae=Be,$e=(n(\"19a6\"),n(\"f900\"),Object(O[\"a\"])(Ae,Ve,Ce,!1,null,\"f93ed2d2\",null)),Re=$e.exports,Ge=n(\"4360\"),We=document,Ne=We.body,Fe=992,Xe={watch:{$route:function(e){\"mobile\"===this.device&&this.sidebar.opened&&Ge[\"a\"].dispatch(\"app/closeSideBar\",{withoutAnimation:!1})}},beforeMount:function(){window.addEventListener(\"resize\",this.$_resizeHandler)},beforeDestroy:function(){window.removeEventListener(\"resize\",this.$_resizeHandler)},mounted:function(){var e=this.$_isMobile();e&&(Ge[\"a\"].dispatch(\"app/toggleDevice\",\"mobile\"),Ge[\"a\"].dispatch(\"app/closeSideBar\",{withoutAnimation:!0}))},methods:{$_isMobile:function(){var e=Ne.getBoundingClientRect();return e.width-1<Fe},$_resizeHandler:function(){if(!document.hidden){var e=this.$_isMobile();Ge[\"a\"].dispatch(\"app/toggleDevice\",e?\"mobile\":\"desktop\"),e&&Ge[\"a\"].dispatch(\"app/closeSideBar\",{withoutAnimation:!0})}}}},Ke={name:\"Layout\",components:{Navbar:$,Sidebar:Oe,AppMain:Ie,TagsView:Re},mixins:[Xe],computed:{sidebar:function(){return this.$store.state.app.sidebar},device:function(){return this.$store.state.app.device},fixedHeader:function(){return this.$store.state.settings.fixedHeader},classObj:function(){return{hideSidebar:!this.sidebar.opened,openSidebar:this.sidebar.opened,withoutAnimation:this.sidebar.withoutAnimation,mobile:\"mobile\"===this.device}}},methods:{handleClickOutside:function(){this.$store.dispatch(\"app/closeSideBar\",{withoutAnimation:!1})}}},Ue=Ke,Je=(n(\"59d0\"),Object(O[\"a\"])(Ue,r,u,!1,null,\"30084642\",null)),Ye=Je.exports;o[\"default\"].use(i[\"a\"]);var Qe=[{path:\"/login\",component:function(){return n.e(\"chunk-e97c97f2\").then(n.bind(null,\"9ed6\"))},hidden:!0},{path:\"/404\",component:function(){return n.e(\"chunk-159c7f2c\").then(n.bind(null,\"8cdb\"))},hidden:!0},{path:\"/\",component:Ye,redirect:\"/dashboard\",children:[{path:\"dashboard\",name:\"控制台\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-759aed6d\"),n.e(\"chunk-016992d1\")]).then(n.bind(null,\"9406\"))},meta:{title:\"控制台\",icon:\"dashboard\",affix:!0}}]},{path:\"/live\",component:Ye,redirect:\"/live\",children:[{path:\"\",name:\"Live\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-3985bb13\"),n.e(\"chunk-4a945e1e\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-4ff9b6b9\")]).then(n.bind(null,\"1502\"))},meta:{title:\"分屏监控\",icon:\"live\"}}]},{path:\"/device\",component:Ye,redirect:\"/device\",onlyIndex:0,children:[{path:\"\",name:\"Device\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-baa1c8c2\"),n.e(\"chunk-aad3070e\"),n.e(\"chunk-27cdfc42\")]).then(n.bind(null,\"26d6\"))},meta:{title:\"国标设备\",icon:\"device\"}},{path:\"/device/record/:deviceId/:channelDeviceId\",name:\"DeviceRecord\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-3985bb13\"),n.e(\"chunk-076318fc\"),n.e(\"chunk-197ea2e8\")]).then(n.bind(null,\"8c08\"))},meta:{title:\"国标录像\"}}]},{path:\"/push\",component:Ye,redirect:\"/push\",children:[{path:\"\",name:\"PushList\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-baa1c8c2\"),n.e(\"chunk-aad3070e\"),n.e(\"chunk-53584e02\")]).then(n.bind(null,\"9751\"))},meta:{title:\"推流列表\",icon:\"streamPush\"}}]},{path:\"/proxy\",component:Ye,redirect:\"/proxy\",children:[{path:\"\",name:\"Proxy\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-baa1c8c2\"),n.e(\"chunk-aad3070e\"),n.e(\"chunk-2d0c08c1\")]).then(n.bind(null,\"41f4\"))},meta:{title:\"拉流代理\",icon:\"streamProxy\"}}]},{path:\"/commonChannel\",component:Ye,redirect:\"/commonChannel/region\",name:\"组织结构\",meta:{title:\"组织结构\",icon:\"tree\"},children:[{path:\"region\",name:\"Region\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-2d0bd647\")]).then(n.bind(null,\"2c99\"))},meta:{title:\"行政区划\",icon:\"region\"}},{path:\"group\",name:\"Group\",component:function(){return Promise.all([n.e(\"chunk-2d0e4fee\"),n.e(\"chunk-2126d0bc\"),n.e(\"chunk-baa1c8c2\"),n.e(\"chunk-2d21023e\")]).then(n.bind(null,\"b72e\"))},meta:{title:\"业务分组\",icon:\"tree\"}}]},{path:\"/recordPlan\",component:Ye,redirect:\"/recordPlan\",children:[{path:\"\",name:\"RecordPlan\",component:function(){return n.e(\"chunk-0057cdc6\").then(n.bind(null,\"d439\"))},meta:{title:\"录制计划\",icon:\"recordPlan\"}}]},{path:\"/cloudRecord\",component:Ye,redirect:\"/cloudRecord\",onlyIndex:0,children:[{path:\"/cloudRecord\",name:\"CloudRecord\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-74f9d1c0\")]).then(n.bind(null,\"ebf3\"))},meta:{title:\"云端录像\",icon:\"cloudRecord\"}},{path:\"/cloudRecord/detail/:app/:stream\",name:\"CloudRecordDetail\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-076318fc\"),n.e(\"chunk-14ce87f4\")]).then(n.bind(null,\"5d60\"))},meta:{title:\"云端录像详情\"}}]},{path:\"/mediaServer\",component:Ye,redirect:\"/mediaServer\",children:[{path:\"\",name:\"MediaServer\",component:function(){return n.e(\"chunk-cc032ec0\").then(n.bind(null,\"3779\"))},meta:{title:\"媒体节点\",icon:\"mediaServerList\"}}]},{path:\"/platform\",component:Ye,redirect:\"/platform\",children:[{path:\"\",name:\"Platform\",component:function(){return n.e(\"chunk-111f5d69\").then(n.bind(null,\"c8bf\"))},meta:{title:\"国标级联\",icon:\"platform\"}}]},{path:\"/user\",component:Ye,redirect:\"/user\",children:[{path:\"\",name:\"User\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-675011ec\")]).then(n.bind(null,\"e382\"))},meta:{title:\"用户管理\",icon:\"user\"}}]},{path:\"/operations\",component:Ye,meta:{title:\"运维中心\",icon:\"operations\"},redirect:\"/operations/systemInfo\",children:[{path:\"/operations/systemInfo\",name:\"OperationsSystemInfo\",component:function(){return n.e(\"chunk-2d0d03f9\").then(n.bind(null,\"66be\"))},meta:{title:\"平台信息\",icon:\"systemInfo\"}},{path:\"/operations/historyLog\",name:\"OperationsHistoryLog\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-3985bb13\"),n.e(\"chunk-46f68df1\"),n.e(\"chunk-14a51b8e\")]).then(n.bind(null,\"908b\"))},meta:{title:\"历史日志\",icon:\"historyLog\"}},{path:\"/operations/realLog\",name:\"OperationsRealLog\",component:function(){return Promise.all([n.e(\"chunk-0ff5d20c\"),n.e(\"chunk-46f68df1\"),n.e(\"chunk-0180ce12\")]).then(n.bind(null,\"963f\"))},meta:{title:\"实时日志\",icon:\"realLog\"}}]},{path:\"/play/wasm/:url\",name:\"wasmPlayer\",hidden:!0,component:function(){return n.e(\"chunk-b3c5ace6\").then(n.bind(null,\"2655\"))}},{path:\"/play/rtc/:url\",name:\"rtcPlayer\",component:function(){return n.e(\"chunk-3b4a2238\").then(n.bind(null,\"bbf2\"))}},{path:\"*\",redirect:\"/404\",hidden:!0}],Ze=function(){return new i[\"a\"]({scrollBehavior:function(){return{y:0}},routes:Qe})},et=Ze();function tt(){var e=Ze();et.matcher=e.matcher}t[\"a\"]=et},a75f:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-group\",use:\"icon-group-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-group\"><path d=\"M863.008 384C916.576 384 960 341.024 960 288V160c0-53.024-43.424-96-96.992-96H160.992C107.424 64 64 106.976 64 160v128c0 53.024 43.424 96 96.992 96H320v128a32 32 0 0 0 32 32h288v96H160.992C107.424 640 64 682.976 64 736v128c0 53.024 43.424 96 96.992 96h702.016C916.576 960 960 917.024 960 864v-128c0-53.024-43.424-96-96.992-96H704v-128a32 32 0 0 0-32-32h-288v-96h479.008z m0 320c17.856 0 32.32 14.336 32.32 32v128c0 17.664-14.464 32-32.32 32H160.992c-17.856 0-32.32-14.336-32.32-32v-128c0-17.664 14.464-32 32.32-32h702.016zM128.672 288V160c0-17.664 14.464-32 32.32-32h702.016c17.856 0 32.32 14.336 32.32 32v128c0 17.664-14.464 32-32.32 32H160.992c-17.856 0-32.32-14.336-32.32-32z\" p-id=\"3657\" /><path d=\"M320 832h384a32 32 0 0 0 0-64H320a32 32 0 0 0 0 64zM736 224a32 32 0 0 0-32-32H320a32 32 0 0 0 0 64h384a32 32 0 0 0 32-32z\" p-id=\"3658\" /></symbol>'});i.a.add(r);t[\"default\"]=r},a8d9:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-live\",use:\"icon-live-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-live\"><path d=\"M728.096891 1023.886235v-295.789357h295.789357v295.789357h-295.789357z m0-659.837796h295.789357v295.789357h-295.789357v-295.789357z m0-364.048439h295.789357v295.732474h-295.789357V0z m-364.048439 728.096878h295.789356v295.789357h-295.789356v-295.789357z m0-364.048439h295.789356v295.789357h-295.789356v-295.789357z m0-364.048439h295.789356v295.732474h-295.789356V0z m-364.048439 728.096878h295.789356v295.789357h-295.789356v-295.789357z m0-364.048439h295.789356v295.789357h-295.789356v-295.789357z m0-364.048439h295.789356v295.732474h-295.789356V0z\" p-id=\"34267\" /></symbol>'});i.a.add(r);t[\"default\"]=r},a94c:function(e,t,n){\"use strict\";n(\"f160\")},aa0f:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-recordPlan\",use:\"icon-recordPlan-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-recordPlan\"><path d=\"M960 904.03H679.9C831.22 831.22 960 691.3 960 512c0-246.41-201.59-448-448-448S64 265.59 64 512s201.59 448 448 448h448v-55.97zM836.92 512c0 67.11-55.97 123.34-123.34 123.34-67.11 0-123.34-55.97-123.34-123.34s55.97-123.34 123.34-123.34S836.92 444.89 836.92 512zM512 187.08c67.11 0 123.34 55.97 123.34 123.34 0 67.11-55.97 123.34-123.34 123.34s-123.34-55.97-123.34-123.34S444.89 187.08 512 187.08zM187.08 512c0-67.11 55.97-123.34 123.34-123.34 67.11 0 123.34 55.97 123.34 123.34s-55.97 123.34-123.34 123.34S187.08 579.11 187.08 512z m201.58 201.59c0-67.11 55.97-123.34 123.34-123.34s123.34 55.97 123.34 123.34c0 67.11-55.97 123.34-123.34 123.34s-123.34-55.98-123.34-123.34z\" p-id=\"10288\" /></symbol>'});i.a.add(r);t[\"default\"]=r},b20f:function(e,t,n){e.exports={menuText:\"#bfcbd9\",menuActiveText:\"#409eff\",subMenuActiveText:\"#f4f4f5\",menuBg:\"#304156\",menuHover:\"#263445\",subMenuBg:\"#1f2d3d\",subMenuHover:\"#001528\",sideBarWidth:\"210px\"}},b21d:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-operations\",use:\"icon-operations-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-operations\"><path d=\"M512 0l448 256v512l-448 256L64 768V256L512 0z m0 147.84L193.28 329.856v364.16L512 876.224 830.72 694.08V329.92L512 147.776z m124.416 163.712L509.76 429.76l87.296 93.632L721.6 407.232a192 192 0 0 1-271.168 240.384l-75.84 70.592-87.232-93.568 78.336-73.088a192 192 0 0 1 270.784-240z\" p-id=\"12803\" /></symbol>'});i.a.add(r);t[\"default\"]=r},b21f:function(e,t,n){},b3b5:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-user\",use:\"icon-user-usage\",viewBox:\"0 0 130 130\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 130 130\" id=\"icon-user\"><path d=\"M63.444 64.996c20.633 0 37.359-14.308 37.359-31.953 0-17.649-16.726-31.952-37.359-31.952-20.631 0-37.36 14.303-37.358 31.952 0 17.645 16.727 31.953 37.359 31.953zM80.57 75.65H49.434c-26.652 0-48.26 18.477-48.26 41.27v2.664c0 9.316 21.608 9.325 48.26 9.325H80.57c26.649 0 48.256-.344 48.256-9.325v-2.663c0-22.794-21.605-41.271-48.256-41.271z\" stroke=\"#979797\" /></symbol>'});i.a.add(r);t[\"default\"]=r},b775:function(e,t,n){\"use strict\";n(\"caad\"),n(\"d3b7\"),n(\"2532\");var a=n(\"bc3a\"),c=n.n(a),o=n(\"5c96\"),i=n(\"4360\"),r=n(\"5f87\"),u=c.a.create({baseURL:\"\",timeout:3e4});u.interceptors.request.use((function(e){return i[\"a\"].getters.token&&e.url.indexOf(\"api/user/login\")<0&&(e.headers[\"access-token\"]=Object(r[\"c\"])()),e}),(function(e){return console.log(e),Promise.reject(e)})),u.interceptors.response.use((function(e){if(!(e.config.url.indexOf(\"/api/user/logout\")>=0)){var t=e.data;return e.config.url.includes(\"/api/front-end/preset/query/\")?t.code&&0!==t.code?(console.warn(\"静默处理 /api/front-end/preset/query/ 错误:\",t.msg),t):t:t.code&&0!==t.code?void Object(o[\"Message\"])({message:t.msg,type:\"error\",duration:5e3}):t}}),(function(e){if(console.log(e),e.config&&e.config.url.includes(\"/api/front-end/preset/query/\"))return e.message.includes(\"timeout\")?void console.warn(\"静默处理 /api/front-end/preset/query/ 超时错误:\",e.message):void console.warn(\"静默处理 /api/front-end/preset/query/ 请求错误:\",e.message);401===e.response.status?o[\"MessageBox\"].confirm(\"登录已经到期， 是否重新登录\",\"登录确认\",{confirmButtonText:\"重新登录\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){i[\"a\"].dispatch(\"user/resetToken\").then((function(){location.reload()}))})):Object(o[\"Message\"])({message:e.message,type:\"error\",duration:5e3})})),t[\"a\"]=u},b9f6:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-platform\",use:\"icon-platform-usage\",viewBox:\"0 0 1152 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1152 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-platform\"><path d=\"M576 631.168c-12.16 0-24.192-2.752-34.944-8L40.512 378.112A70.08 70.08 0 0 1 0 315.584c0-26.24 15.616-50.304 40.512-62.528L540.992 8.064a80.256 80.256 0 0 1 70.08 0l500.48 244.992c24.832 12.16 40.448 36.288 40.448 62.528 0 26.24-15.616 50.368-40.512 62.528l-500.48 245.056c-10.816 5.248-22.784 8-35.008 8z m-0.768 200.512c-18.688 0-37.184-4.224-53.76-12.352L58.88 592.768a44.032 44.032 0 0 1-26.048-37.376 43.328 43.328 0 0 1 22.144-39.488 49.92 49.92 0 0 1 47.616-1.216l462.592 226.56a23.104 23.104 0 0 0 20.224 0l462.528-226.56a49.92 49.92 0 0 1 47.68 1.216 43.328 43.328 0 0 1 22.144 39.488 44.032 44.032 0 0 1-26.112 37.312l-462.464 226.56c-16.64 8.192-35.2 12.416-53.952 12.416z m0 192.32c-18.752 0-37.248-4.288-53.824-12.416L58.88 785.024a44.032 44.032 0 0 1-26.048-37.312 43.328 43.328 0 0 1 22.144-39.552 49.92 49.92 0 0 1 47.616-1.152l462.592 226.56a23.104 23.104 0 0 0 20.16 0l462.592-226.56a49.92 49.92 0 0 1 47.68 1.152 43.328 43.328 0 0 1 22.144 39.552 44.032 44.032 0 0 1-26.112 37.312l-462.464 226.56c-16.64 8.192-35.136 12.416-53.888 12.416z\" p-id=\"6076\" /></symbol>'});i.a.add(r);t[\"default\"]=r},c459:function(e,t,n){},c742:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-mediaServerList\",use:\"icon-mediaServerList-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-mediaServerList\"><path d=\"M853.312 85.312c44.352 0 80.832 33.92 84.992 77.12l0.384 8.256v682.624c0 44.352-33.92 80.832-77.12 84.992l-8.256 0.384H170.688a85.312 85.312 0 0 1-84.992-77.12l-0.384-8.256V170.688c0-44.352 33.92-80.832 77.12-84.992l8.256-0.384h682.624zM345.6 162.112H170.688a8.512 8.512 0 0 0-8.32 6.4l-0.256 2.176v682.624c0 3.84 2.496 7.04 5.888 8.128l2.688 0.448h174.848V161.984z m256 0H422.4v699.712h179.2V162.048z m251.776 0H678.4v699.712h174.976a8.512 8.512 0 0 0 8.128-5.76l0.448-2.752V170.688a8.512 8.512 0 0 0-8.576-8.576z m-59.712 501.376a38.4 38.4 0 1 1 0 76.8h-51.2a38.4 38.4 0 0 1 0-76.8h51.2z m-512 0a38.4 38.4 0 1 1 0 76.8h-51.2a38.4 38.4 0 0 1 0-76.8h51.2z m512-192a38.4 38.4 0 0 1 0 76.8h-51.2a38.4 38.4 0 0 1 0-76.8h51.2z m-512 0a38.4 38.4 0 1 1 0 76.8h-51.2a38.4 38.4 0 1 1 0-76.8h51.2z m512-192a38.4 38.4 0 0 1 0 76.8h-51.2a38.4 38.4 0 0 1 0-76.8h51.2z m-512 0a38.4 38.4 0 1 1 0 76.8h-51.2a38.4 38.4 0 1 1 0-76.8h51.2z\" p-id=\"11483\" /></symbol>'});i.a.add(r);t[\"default\"]=r},c8e4:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-streamProxy\",use:\"icon-streamProxy-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-streamProxy\"><path d=\"M998.8096 795.648a61.44 61.44 0 0 0-56.7296-37.888h-61.44v-207.6672c-61.44 36.7104-61.44 63.5392-122.88 76.0832v131.584h-61.44c-24.832 0-47.2576 15.0016-56.832 37.888-9.4208 23.04-4.096 49.408 13.3632 67.0208l122.88 122.88a61.2864 61.2864 0 0 0 86.8864 0l122.88-122.88c17.5616-17.6128 22.784-44.032 13.312-66.9696M327.68 757.76H266.24v-122.88c0-33.9456 26.3168-61.44 60.3136-61.44h368.64c101.7856 0 185.4464-82.5344 185.4464-184.32v-307.2a61.44 61.44 0 1 0-122.88 0v307.2c0 33.8944-28.672 61.44-62.5664 61.44h-368.64A183.3472 183.3472 0 0 0 143.36 634.88v122.88H81.92c-24.832 0-47.2576 14.9504-56.832 37.888-9.4208 22.9376-4.096 49.3568 13.3632 66.9696l122.88 122.88a61.2864 61.2864 0 0 0 86.8864 0l122.88-122.88A61.44 61.44 0 0 0 327.68 757.76\" p-id=\"1494\" /></symbol>'});i.a.add(r);t[\"default\"]=r},cd5e:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-region\",use:\"icon-region-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-region\"><path d=\"M928 832h-32V384c0-70.4-57.6-128-128-128H576v-64c0-70.4-57.6-128-128-128H192c-70.4 0-128 57.6-128 128v640H32c-17.92 0-32 14.08-32 32s14.08 32 32 32h896c17.92 0 32-14.08 32-32s-14.08-32-32-32zM512 256v576H128V192c0-35.2 28.8-64 64-64h256c35.2 0 64 28.8 64 64v64z m320 576H576V320h192c35.2 0 64 28.8 64 64v448zM416 256H224c-17.92 0-32 14.08-32 32s14.08 32 32 32h192c17.92 0 32-14.08 32-32s-14.08-32-32-32z m0 192H224c-17.92 0-32 14.08-32 32s14.08 32 32 32h192c17.92 0 32-14.08 32-32s-14.08-32-32-32z m0 192H224c-17.92 0-32 14.08-32 32s14.08 32 32 32h192c17.92 0 32-14.08 32-32s-14.08-32-32-32z m320-192h-64c-17.92 0-32 14.08-32 32s14.08 32 32 32h64c17.92 0 32-14.08 32-32s-14.08-32-32-32z m0 192h-64c-17.92 0-32 14.08-32 32s14.08 32 32 32h64c17.92 0 32-14.08 32-32s-14.08-32-32-32z\" p-id=\"2639\" /></symbol>'});i.a.add(r);t[\"default\"]=r},cf1e:function(e,t,n){e.exports={menuText:\"#bfcbd9\",menuActiveText:\"#409eff\",subMenuActiveText:\"#f4f4f5\",menuBg:\"#304156\",menuHover:\"#263445\",subMenuBg:\"#1f2d3d\",subMenuHover:\"#001528\",sideBarWidth:\"210px\"}},cf5d:function(e,t,n){},d604:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-cloudRecord\",use:\"icon-cloudRecord-usage\",viewBox:\"0 0 1127 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1127 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-cloudRecord\"><path d=\"M1074.12176 136.4v0.7l-0.5-0.3 0.5-0.4zM1020.82176 170v0.6l-0.5-0.3 0.5-0.3z m0 0\" p-id=\"2692\" /><path d=\"M1127.52176 95.9v0.8l-0.6-0.3 0.6-0.5z m-59 37.2v0.7l-0.6-0.3 0.6-0.4z m0 0M645.52176 963.3c-72.2 0.1-131.6-56.9-134.3-129V690.9c2.7-72.2 62-129.3 134.3-129.3h7.3c10.6 0.2 20.6 4.9 27.7 13 9.1 10.7 11.1 25.8 5.1 38.5S666.92176 634 652.82176 634h-0.3c-16.1-1-31.9 4.4-44 15.2a60.444 60.444 0 0 0-20.3 41.9v142.8c1.3 32 27.7 57.1 59.7 57.1h244.6l2-20c1.1-31 26.6-55.7 57.7-55.6 7.5-0.1 15.1 1 22.3 3.3l73.6 25.7V680.9l-73.3 25.5c-6.6 2.4-13.7 3.7-20.8 3.7-24 0-45.7-14.1-55.6-36-2.5-6.1-3.9-12.6-4-19.3V636l-104.5-1.6c-19-1.9-33.4-18.3-32.7-37.4 0.8-19.2 16.5-34.3 35.7-34.7h99.7c40.1-1.1 74.3 28.8 78.5 68.7l70-24.3c6.9-2.3 14-3.5 21.1-3.5 32.9-1.1 60.7 24.1 62.8 56.9V864c0 7-1.3 14-4 20.5-6.1 14.8-17.8 26.7-32.7 32.7-15.1 6.3-31.9 6.9-47.4 1.6l-70-24.3c-1.3 18.2-9.6 35-23 47.3-14.9 13.7-34.7 21.4-55 21.4l-247.4 0.1z\" p-id=\"2693\" /><path d=\"M297.42176 949.8C196.52176 949.1 103.12176 895.9 50.92176 809.4c-44.4-70.9-60.6-155.7-45.3-237.9 13.8-81.3 58.6-153.9 125-202.7 40.3-29.7 87.8-48.1 137.6-53.4 41.5-97.4 117.8-176 214.1-220.1 101-46.4 217-47.2 318.7-2.1 160.3 73.6 261.9 235.3 258.7 411.8 0.8 19.5-10.9 37.4-28.9 44.7-18.1 7.3-38.9 2.4-51.8-12.1-8.2-9.1-12.5-21.1-11.7-33.4 2.3-137.6-76.8-263.8-201.9-321.2-78.6-34.7-168.3-34-246.4 1.9C455.52176 214 403.12176 262.5 368.82176 323.3c72.9 19.7 135.5 66.6 175 131 14 22 8.6 51.2-12.1 66.9-7.7 5.5-17.1 8.5-26.6 8.7-15.8-0.1-30.5-8.4-38.6-21.9-29-47.3-76.1-80.6-130.2-92.5-53.4-11.2-109.1 1-152.9 33.4-46.1 33.8-77.2 84.3-86.7 140.6C85.92176 647 97.12176 706.1 128.12176 755.4c35.8 59.6 100 96.1 169.4 96.7h101.2c26 1.1 46.7 22.5 46.8 48.5 0.2 26.1-20.2 47.7-46.2 49.2h-102z\" p-id=\"2694\" /></symbol>'});i.a.add(r);t[\"default\"]=r},d749:function(e,t,n){\"use strict\";n(\"cf5d\")},d7ec:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-eye-open\",use:\"icon-eye-open-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" id=\"icon-eye-open\"><defs><style></style></defs><path d=\"M512 128q69.675 0 135.51 21.163t115.498 54.997 93.483 74.837 73.685 82.006 51.67 74.837 32.17 54.827L1024 512q-2.347 4.992-6.315 13.483T998.87 560.17t-31.658 51.669-44.331 59.99-56.832 64.34-69.504 60.16-82.347 51.5-94.848 34.687T512 896q-69.675 0-135.51-21.163t-115.498-54.826-93.483-74.326-73.685-81.493-51.67-74.496-32.17-54.997L0 513.707q2.347-4.992 6.315-13.483t18.816-34.816 31.658-51.84 44.331-60.33 56.832-64.683 69.504-60.331 82.347-51.84 94.848-34.816T512 128.085zm0 85.333q-46.677 0-91.648 12.331t-81.152 31.83-70.656 47.146-59.648 54.485-48.853 57.686-37.675 52.821-26.325 43.99q12.33 21.674 26.325 43.52t37.675 52.351 48.853 57.003 59.648 53.845T339.2 767.02t81.152 31.488T512 810.667t91.648-12.331 81.152-31.659 70.656-46.848 59.648-54.186 48.853-57.344 37.675-52.651T927.957 512q-12.33-21.675-26.325-43.648t-37.675-52.65-48.853-57.345-59.648-54.186-70.656-46.848-81.152-31.659T512 213.334zm0 128q70.656 0 120.661 50.006T682.667 512 632.66 632.661 512 682.667 391.339 632.66 341.333 512t50.006-120.661T512 341.333zm0 85.334q-35.328 0-60.33 25.002T426.666 512t25.002 60.33T512 597.334t60.33-25.002T597.334 512t-25.002-60.33T512 426.666z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},dcf8:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-nested\",use:\"icon-nested-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-nested\"><path d=\"M.002 9.2c0 5.044 3.58 9.133 7.998 9.133 4.417 0 7.997-4.089 7.997-9.133 0-5.043-3.58-9.132-7.997-9.132S.002 4.157.002 9.2zM31.997.066h95.981V18.33H31.997V.066zm0 45.669c0 5.044 3.58 9.132 7.998 9.132 4.417 0 7.997-4.088 7.997-9.132 0-3.263-1.524-6.278-3.998-7.91-2.475-1.63-5.524-1.63-7.998 0-2.475 1.632-4 4.647-4 7.91zM63.992 36.6h63.986v18.265H63.992V36.6zm-31.995 82.2c0 5.043 3.58 9.132 7.998 9.132 4.417 0 7.997-4.089 7.997-9.132 0-5.044-3.58-9.133-7.997-9.133s-7.998 4.089-7.998 9.133zm31.995-9.131h63.986v18.265H63.992V109.67zm0-27.404c0 5.044 3.58 9.133 7.998 9.133 4.417 0 7.997-4.089 7.997-9.133 0-3.263-1.524-6.277-3.998-7.909-2.475-1.631-5.524-1.631-7.998 0-2.475 1.632-4 4.646-4 7.91zm31.995-9.13h31.991V91.4H95.987V73.135z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},de06:function(e,t,n){\"use strict\";n(\"2bb1\")},e66f:function(e,t,n){},eb1b:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-form\",use:\"icon-form-usage\",viewBox:\"0 0 128 128\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\" id=\"icon-form\"><path d=\"M84.068 23.784c-1.02 0-1.877-.32-2.572-.96a8.588 8.588 0 0 1-1.738-2.237 11.524 11.524 0 0 1-1.042-2.621c-.232-.895-.348-1.641-.348-2.238V0h.278c.834 0 1.622.085 2.363.256.742.17 1.645.575 2.711 1.214 1.066.64 2.363 1.535 3.892 2.686 1.53 1.15 3.453 2.664 5.77 4.54 2.502 2.045 4.494 3.771 5.977 5.178 1.483 1.406 2.618 2.6 3.406 3.58.787.98 1.274 1.812 1.46 2.494.185.682.277 1.278.277 1.79v2.046H84.068zM127.3 84.01c.278.682.464 1.535.556 2.558.093 1.023-.37 2.003-1.39 2.94-.463.427-.88.832-1.25 1.215-.372.384-.696.704-.974.96a6.69 6.69 0 0 1-.973.767l-11.816-10.741a44.331 44.331 0 0 0 1.877-1.535 31.028 31.028 0 0 1 1.737-1.406c1.112-.938 2.317-1.343 3.615-1.215 1.297.128 2.363.405 3.197.83.927.427 1.923 1.173 2.989 2.239 1.065 1.065 1.876 2.195 2.432 3.388zM78.23 95.902c2.038 0 3.752-.511 5.143-1.534l-26.969 25.83H18.037c-1.761 0-3.684-.47-5.77-1.407a24.549 24.549 0 0 1-5.838-3.709 21.373 21.373 0 0 1-4.518-5.306c-1.204-2.003-1.807-4.07-1.807-6.202V16.495c0-1.79.44-3.665 1.32-5.626A18.41 18.41 0 0 1 5.04 5.562a21.798 21.798 0 0 1 5.213-3.964C12.198.533 14.237 0 16.37 0h53.24v15.984c0 1.62.278 3.367.834 5.242a16.704 16.704 0 0 0 2.572 5.179c1.159 1.577 2.665 2.898 4.518 3.964 1.853 1.066 4.078 1.598 6.673 1.598h20.295v42.325L85.458 92.45c1.02-1.364 1.529-2.856 1.529-4.476 0-2.216-.857-4.113-2.572-5.69-1.714-1.577-3.776-2.366-6.186-2.366H26.1c-2.409 0-4.448.789-6.116 2.366-1.668 1.577-2.502 3.474-2.502 5.69 0 2.217.834 4.092 2.502 5.626 1.668 1.535 3.707 2.302 6.117 2.302h52.13zM26.1 47.951c-2.41 0-4.449.789-6.117 2.366-1.668 1.577-2.502 3.473-2.502 5.69 0 2.216.834 4.092 2.502 5.626 1.668 1.534 3.707 2.302 6.117 2.302h52.13c2.409 0 4.47-.768 6.185-2.302 1.715-1.534 2.572-3.41 2.572-5.626 0-2.217-.857-4.113-2.572-5.69-1.714-1.577-3.776-2.366-6.186-2.366H26.1zm52.407 64.063l1.807-1.663 3.476-3.196a479.75 479.75 0 0 0 4.587-4.284 500.757 500.757 0 0 1 5.004-4.667c3.985-3.666 8.48-7.758 13.485-12.276l11.677 10.741-13.485 12.404-5.004 4.603-4.587 4.22a179.46 179.46 0 0 0-3.267 3.068c-.88.853-1.367 1.322-1.46 1.407-.463.341-.973.703-1.529 1.087-.556.383-1.112.703-1.668.959-.556.256-1.413.575-2.572.959a83.5 83.5 0 0 1-3.545 1.087 72.2 72.2 0 0 1-3.475.895c-1.112.256-1.946.426-2.502.511-1.112.17-1.854.043-2.224-.383-.371-.426-.464-1.151-.278-2.174.092-.511.278-1.279.556-2.302.278-1.023.602-2.067.973-3.132l1.042-3.005c.325-.938.58-1.577.765-1.918a10.157 10.157 0 0 1 2.224-2.941z\" /></symbol>'});i.a.add(r);t[\"default\"]=r},ebb8:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-systemInfo\",use:\"icon-systemInfo-usage\",viewBox:\"0 0 1024 1024\",content:'<symbol class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" id=\"icon-systemInfo\"><path d=\"M578.752 261.376m-69.312 0a1.083 1.083 0 1 0 138.624 0 1.083 1.083 0 1 0-138.624 0Z\" p-id=\"10661\" /><path d=\"M533.824 730.88c24.64-96.896 76.608-310.784 61.44-340.16C591.616 383.488 584.32 379.392 575.232 379.392c-40.448 0-168.896 102.592-194.24 123.136L375.872 506.752l0 28.224 94.72-23.744c-11.2 56.256-41.344 205.12-52.8 238.08-9.024 25.984-8 48.768 2.944 64C426.752 821.888 438.656 832 461.056 832c9.216 0 19.584-1.728 30.784-5.12 61.184-18.752 114.56-107.648 116.8-111.488l32.384-54.528-52.288 35.904C561.536 715.264 544.448 725.376 533.824 730.88z\" p-id=\"10662\" /><path d=\"M512 64C264.576 64 64 264.576 64 512c0 247.488 200.576 448 448 448 247.36 0 448-200.512 448-448C960 264.576 759.36 64 512 64zM512 889.728c-208.576 0-377.728-169.152-377.728-377.728 0-208.64 169.152-377.728 377.728-377.728 208.64 0 377.728 169.152 377.728 377.728C889.728 720.64 720.64 889.728 512 889.728z\" p-id=\"10663\" /></symbol>'});i.a.add(r);t[\"default\"]=r},f160:function(e,t,n){},f6c3:function(e,t,n){},f782:function(e,t,n){\"use strict\";n.r(t);var a=n(\"e017\"),c=n.n(a),o=n(\"21a1\"),i=n.n(o),r=new c.a({id:\"icon-dashboard\",use:\"icon-dashboard-usage\",viewBox:\"0 0 128 100\",content:'<symbol xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 100\" id=\"icon-dashboard\">\\r\\n  <path d=\"M27.429 63.638c0-2.508-.893-4.65-2.679-6.424-1.786-1.775-3.94-2.662-6.464-2.662-2.524 0-4.679.887-6.465 2.662-1.785 1.774-2.678 3.916-2.678 6.424 0 2.508.893 4.65 2.678 6.424 1.786 1.775 3.94 2.662 6.465 2.662 2.524 0 4.678-.887 6.464-2.662 1.786-1.775 2.679-3.916 2.679-6.424zm13.714-31.801c0-2.508-.893-4.65-2.679-6.424-1.785-1.775-3.94-2.662-6.464-2.662-2.524 0-4.679.887-6.464 2.662-1.786 1.774-2.679 3.916-2.679 6.424 0 2.508.893 4.65 2.679 6.424 1.785 1.774 3.94 2.662 6.464 2.662 2.524 0 4.679-.888 6.464-2.662 1.786-1.775 2.679-3.916 2.679-6.424zM71.714 65.98l7.215-27.116c.285-1.23.107-2.378-.536-3.443-.643-1.064-1.56-1.762-2.75-2.094-1.19-.33-2.333-.177-3.429.462-1.095.639-1.81 1.573-2.143 2.804l-7.214 27.116c-2.857.237-5.405 1.266-7.643 3.088-2.238 1.822-3.738 4.152-4.5 6.992-.952 3.644-.476 7.098 1.429 10.364 1.905 3.265 4.69 5.37 8.357 6.317 3.667.947 7.143.474 10.429-1.42 3.285-1.892 5.404-4.66 6.357-8.305.762-2.84.619-5.607-.429-8.305-1.047-2.697-2.762-4.85-5.143-6.46zm47.143-2.342c0-2.508-.893-4.65-2.678-6.424-1.786-1.775-3.94-2.662-6.465-2.662-2.524 0-4.678.887-6.464 2.662-1.786 1.774-2.679 3.916-2.679 6.424 0 2.508.893 4.65 2.679 6.424 1.786 1.775 3.94 2.662 6.464 2.662 2.524 0 4.679-.887 6.465-2.662 1.785-1.775 2.678-3.916 2.678-6.424zm-45.714-45.43c0-2.509-.893-4.65-2.679-6.425C68.68 10.01 66.524 9.122 64 9.122c-2.524 0-4.679.887-6.464 2.661-1.786 1.775-2.679 3.916-2.679 6.425 0 2.508.893 4.65 2.679 6.424 1.785 1.774 3.94 2.662 6.464 2.662 2.524 0 4.679-.888 6.464-2.662 1.786-1.775 2.679-3.916 2.679-6.424zm32 13.629c0-2.508-.893-4.65-2.679-6.424-1.785-1.775-3.94-2.662-6.464-2.662-2.524 0-4.679.887-6.464 2.662-1.786 1.774-2.679 3.916-2.679 6.424 0 2.508.893 4.65 2.679 6.424 1.785 1.774 3.94 2.662 6.464 2.662 2.524 0 4.679-.888 6.464-2.662 1.786-1.775 2.679-3.916 2.679-6.424zM128 63.638c0 12.351-3.357 23.78-10.071 34.286-.905 1.372-2.19 2.058-3.858 2.058H13.93c-1.667 0-2.953-.686-3.858-2.058C3.357 87.465 0 76.037 0 63.638c0-8.613 1.69-16.847 5.071-24.703C8.452 31.08 13 24.312 18.714 18.634c5.715-5.68 12.524-10.199 20.429-13.559C47.048 1.715 55.333.035 64 .035c8.667 0 16.952 1.68 24.857 5.04 7.905 3.36 14.714 7.88 20.429 13.559 5.714 5.678 10.262 12.446 13.643 20.301 3.38 7.856 5.071 16.09 5.071 24.703z\" />\\r\\n</symbol>'});i.a.add(r);t[\"default\"]=r},f87e:function(e,t,n){\"use strict\";n(\"279e\")},f900:function(e,t,n){\"use strict\";n(\"b21f\")}},[[0,\"runtime\",\"chunk-elementUI\",\"chunk-libs\"]]]);", "extractedComments": []}