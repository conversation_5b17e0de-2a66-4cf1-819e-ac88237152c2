0 verbose cli C:\Driver-D\tools\nodejs\node.exe C:\Driver-D\tools\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.15.0
3 silly config load:file:C:\Driver-D\tools\nodejs\node_modules\npm\npmrc
4 silly config load:file:C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Driver-E\工作\代码文件\nodejs\node_global\etc\npmrc
7 verbose title npm install @types/core-js@ts5.8 @types/js-cookie@ts5.8 @types/nprogress@ts5.8 @types/connect@ts5.8 @types/html-webpack-plugin@ts5.8 @types/mockjs@ts5.8 @types/sass-loader@ts5.8 @types/script-ext-html-webpack-plugin@ts5.8 @types/serve-static@ts5.8 @types/svg-sprite-loader@ts5.8 @types/request@ts5.8 @types/platform@ts5.8 @types/uuid@ts5.8
8 verbose argv "install" "--ignore-scripts" "@types/core-js@ts5.8" "@types/js-cookie@ts5.8" "@types/nprogress@ts5.8" "@types/connect@ts5.8" "@types/html-webpack-plugin@ts5.8" "@types/mockjs@ts5.8" "@types/sass-loader@ts5.8" "@types/script-ext-html-webpack-plugin@ts5.8" "@types/serve-static@ts5.8" "@types/svg-sprite-loader@ts5.8" "@types/request@ts5.8" "@types/platform@ts5.8" "@types/uuid@ts5.8" "--save-dev" "--user-agent" "typesInstaller/5.8.3"
9 verbose logfile logs-max:10 dir:C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_53_04_956Z-
10 verbose logfile C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_53_04_956Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly logfile done cleaning log files
14 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fcore-js cache-miss
15 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fjs-cookie cache-miss
16 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fnprogress cache-miss
17 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fconnect cache-miss
18 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fhtml-webpack-plugin cache-miss
19 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmockjs cache-miss
20 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fsass-loader cache-miss
21 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin cache-miss
22 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fserve-static cache-miss
23 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fsvg-sprite-loader cache-miss
24 silly packumentCache corgi:https://registry.npmmirror.com/@types%2frequest cache-miss
25 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fplatform cache-miss
26 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fuuid cache-miss
27 http fetch GET 200 https://registry.npmmirror.com/@types%2fserve-static 86ms (cache miss)
28 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fserve-static set size:7406 disposed:false
29 http fetch GET 200 https://registry.npmmirror.com/@types%2fcore-js 89ms (cache miss)
30 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fcore-js set size:5412 disposed:false
31 http fetch GET 200 https://registry.npmmirror.com/@types%2fjs-cookie 89ms (cache miss)
32 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fjs-cookie set size:6373 disposed:false
33 http fetch GET 200 https://registry.npmmirror.com/@types%2frequest 90ms (cache miss)
34 silly packumentCache corgi:https://registry.npmmirror.com/@types%2frequest set size:9249 disposed:false
35 http fetch GET 200 https://registry.npmmirror.com/@types%2fnprogress 96ms (cache miss)
36 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fnprogress set size:2978 disposed:false
37 http fetch GET 200 https://registry.npmmirror.com/@types%2fsvg-sprite-loader 96ms (cache miss)
38 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fsvg-sprite-loader set size:5702 disposed:false
39 http fetch GET 200 https://registry.npmmirror.com/@types%2fuuid 96ms (cache miss)
40 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fuuid set size:13139 disposed:false
41 http fetch GET 200 https://registry.npmmirror.com/@types%2fhtml-webpack-plugin 99ms (cache miss)
42 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fhtml-webpack-plugin set size:3924 disposed:false
43 http fetch GET 200 https://registry.npmmirror.com/@types%2fconnect 111ms (cache miss)
44 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fconnect set size:3271 disposed:false
45 http fetch GET 200 https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin 116ms (cache miss)
46 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin set size:4112 disposed:false
47 http fetch GET 200 https://registry.npmmirror.com/@types%2fplatform 117ms (cache miss)
48 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fplatform set size:2893 disposed:false
49 http fetch GET 200 https://registry.npmmirror.com/@types%2fmockjs 119ms (cache miss)
50 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fmockjs set size:4231 disposed:false
51 http fetch GET 200 https://registry.npmmirror.com/@types%2fsass-loader 181ms (cache miss)
52 silly packumentCache corgi:https://registry.npmmirror.com/@types%2fsass-loader set size:3817 disposed:false
53 silly idealTree buildDeps
54 silly fetch manifest @types/connect@3.4.38
55 silly packumentCache full:https://registry.npmmirror.com/@types%2fconnect cache-miss
56 http fetch GET 200 https://registry.npmmirror.com/@types%2fconnect 12ms (cache miss)
57 silly packumentCache full:https://registry.npmmirror.com/@types%2fconnect set size:6467 disposed:false
58 silly fetch manifest @types/core-js@2.5.8
59 silly packumentCache full:https://registry.npmmirror.com/@types%2fcore-js cache-miss
60 http fetch GET 200 https://registry.npmmirror.com/@types%2fcore-js 12ms (cache miss)
61 silly packumentCache full:https://registry.npmmirror.com/@types%2fcore-js set size:11130 disposed:false
62 silly fetch manifest @types/html-webpack-plugin@3.2.9
63 silly packumentCache full:https://registry.npmmirror.com/@types%2fhtml-webpack-plugin cache-miss
64 http fetch GET 200 https://registry.npmmirror.com/@types%2fhtml-webpack-plugin 23ms (cache miss)
65 silly packumentCache full:https://registry.npmmirror.com/@types%2fhtml-webpack-plugin set size:8286 disposed:false
66 silly fetch manifest @types/js-cookie@3.0.6
67 silly packumentCache full:https://registry.npmmirror.com/@types%2fjs-cookie cache-miss
68 http fetch GET 200 https://registry.npmmirror.com/@types%2fjs-cookie 69ms (cache miss)
69 silly packumentCache full:https://registry.npmmirror.com/@types%2fjs-cookie set size:11601 disposed:false
70 silly fetch manifest @types/mockjs@1.0.10
71 silly packumentCache full:https://registry.npmmirror.com/@types%2fmockjs cache-miss
72 http fetch GET 200 https://registry.npmmirror.com/@types%2fmockjs 23ms (cache miss)
73 silly packumentCache full:https://registry.npmmirror.com/@types%2fmockjs set size:6457 disposed:false
74 silly fetch manifest @types/nprogress@0.2.3
75 silly packumentCache full:https://registry.npmmirror.com/@types%2fnprogress cache-miss
76 http fetch GET 200 https://registry.npmmirror.com/@types%2fnprogress 26ms (cache miss)
77 silly packumentCache full:https://registry.npmmirror.com/@types%2fnprogress set size:6280 disposed:false
78 silly fetch manifest @types/platform@1.3.6
79 silly packumentCache full:https://registry.npmmirror.com/@types%2fplatform cache-miss
80 http fetch GET 200 https://registry.npmmirror.com/@types%2fplatform 24ms (cache miss)
81 silly packumentCache full:https://registry.npmmirror.com/@types%2fplatform set size:6057 disposed:false
82 silly fetch manifest @types/request@2.48.12
83 silly packumentCache full:https://registry.npmmirror.com/@types%2frequest cache-miss
84 http fetch GET 200 https://registry.npmmirror.com/@types%2frequest 25ms (cache miss)
85 silly packumentCache full:https://registry.npmmirror.com/@types%2frequest set size:19231 disposed:false
86 silly fetch manifest @types/sass-loader@8.0.9
87 silly packumentCache full:https://registry.npmmirror.com/@types%2fsass-loader cache-miss
88 http fetch GET 200 https://registry.npmmirror.com/@types%2fsass-loader 61ms (cache miss)
89 silly packumentCache full:https://registry.npmmirror.com/@types%2fsass-loader set size:5879 disposed:false
90 silly fetch manifest @types/script-ext-html-webpack-plugin@2.1.6
91 silly packumentCache full:https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin cache-miss
92 http fetch GET 200 https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin 23ms (cache miss)
93 silly packumentCache full:https://registry.npmmirror.com/@types%2fscript-ext-html-webpack-plugin set size:6525 disposed:false
94 silly fetch manifest @types/serve-static@1.15.8
95 silly packumentCache full:https://registry.npmmirror.com/@types%2fserve-static cache-miss
96 http fetch GET 200 https://registry.npmmirror.com/@types%2fserve-static 13ms (cache miss)
97 silly packumentCache full:https://registry.npmmirror.com/@types%2fserve-static set size:13344 disposed:false
98 silly fetch manifest @types/svg-sprite-loader@3.9.9
99 silly packumentCache full:https://registry.npmmirror.com/@types%2fsvg-sprite-loader cache-miss
100 http fetch GET 200 https://registry.npmmirror.com/@types%2fsvg-sprite-loader 32ms (cache miss)
101 silly packumentCache full:https://registry.npmmirror.com/@types%2fsvg-sprite-loader set size:7795 disposed:false
102 silly fetch manifest @types/uuid@10.0.0
103 silly packumentCache full:https://registry.npmmirror.com/@types%2fuuid cache-miss
104 http fetch GET 200 https://registry.npmmirror.com/@types%2fuuid 68ms (cache miss)
105 silly packumentCache full:https://registry.npmmirror.com/@types%2fuuid set size:23232 disposed:false
106 silly placeDep ROOT @types/connect@3.4.38 REPLACE for:  want: 3.4.38
107 silly placeDep ROOT @types/core-js@2.5.8 REPLACE for:  want: 2.5.8
108 silly placeDep ROOT @types/html-webpack-plugin@3.2.9 REPLACE for:  want: 3.2.9
109 silly placeDep ROOT @types/js-cookie@3.0.6 REPLACE for:  want: 3.0.6
110 silly placeDep ROOT @types/mockjs@1.0.10 REPLACE for:  want: 1.0.10
111 silly placeDep ROOT @types/nprogress@0.2.3 REPLACE for:  want: 0.2.3
112 silly placeDep ROOT @types/platform@1.3.6 REPLACE for:  want: 1.3.6
113 silly placeDep ROOT @types/request@2.48.12 REPLACE for:  want: 2.48.12
114 silly placeDep ROOT @types/sass-loader@8.0.9 REPLACE for:  want: 8.0.9
115 silly placeDep ROOT @types/script-ext-html-webpack-plugin@2.1.6 REPLACE for:  want: 2.1.6
116 silly placeDep ROOT @types/serve-static@1.15.8 REPLACE for:  want: 1.15.8
117 silly placeDep ROOT @types/svg-sprite-loader@3.9.9 REPLACE for:  want: 3.9.9
118 silly placeDep ROOT @types/uuid@10.0.0 REPLACE for:  want: 10.0.0
119 silly reify moves {}
120 silly audit bulk request {
120 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
120 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
120 silly audit   '@babel/parser': [ '7.27.5' ],
120 silly audit   '@babel/types': [ '7.27.6' ],
120 silly audit   '@parcel/watcher': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-android-arm64': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-darwin-arm64': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-darwin-x64': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-freebsd-x64': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-arm-glibc': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-arm-musl': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-arm64-glibc': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-arm64-musl': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-x64-glibc': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-linux-x64-musl': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-win32-arm64': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-win32-ia32': [ '2.5.1' ],
120 silly audit   '@parcel/watcher-win32-x64': [ '2.5.1' ],
120 silly audit   '@types/babel__generator': [ '7.27.0' ],
120 silly audit   '@types/babel__template': [ '7.4.4' ],
120 silly audit   '@types/babel__traverse': [ '7.20.7' ],
120 silly audit   '@types/body-parser': [ '1.19.5' ],
120 silly audit   '@types/caseless': [ '0.12.5' ],
120 silly audit   '@types/clean-css': [ '4.2.11' ],
120 silly audit   '@types/color-convert': [ '2.0.4' ],
120 silly audit   '@types/color-name': [ '1.1.5' ],
120 silly audit   '@types/html-minifier': [ '4.0.5' ],
120 silly audit   '@types/http-errors': [ '2.0.4' ],
120 silly audit   '@types/mime': [ '1.3.5' ],
120 silly audit   '@types/node': [ '22.15.30' ],
120 silly audit   '@types/node-sass': [ '4.11.8' ],
120 silly audit   '@types/react': [ '19.1.8' ],
120 silly audit   '@types/relateurl': [ '0.2.33' ],
120 silly audit   '@types/send': [ '0.17.4' ],
120 silly audit   '@types/source-list-map': [ '0.1.6' ],
120 silly audit   '@types/tapable': [ '1.0.12' ],
120 silly audit   '@types/tough-cookie': [ '4.0.5' ],
120 silly audit   '@types/uglify-js': [ '3.17.5' ],
120 silly audit   '@types/webpack': [ '4.41.40' ],
120 silly audit   '@types/webpack-sources': [ '3.2.3' ],
120 silly audit   'source-map': [ '0.7.4', '0.6.1' ],
120 silly audit   anymatch: [ '3.1.3' ],
120 silly audit   asynckit: [ '0.4.0' ],
120 silly audit   braces: [ '3.0.3' ],
120 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
120 silly audit   chokidar: [ '4.0.3' ],
120 silly audit   'combined-stream': [ '1.0.8' ],
120 silly audit   csstype: [ '3.1.3' ],
120 silly audit   'delayed-stream': [ '1.0.0' ],
120 silly audit   'detect-libc': [ '1.0.3' ],
120 silly audit   'dunder-proto': [ '1.0.1' ],
120 silly audit   'es-define-property': [ '1.0.1' ],
120 silly audit   'es-errors': [ '1.3.0' ],
120 silly audit   'es-object-atoms': [ '1.1.1' ],
120 silly audit   'es-set-tostringtag': [ '2.1.0' ],
120 silly audit   'fill-range': [ '7.1.1' ],
120 silly audit   'form-data': [ '2.5.3' ],
120 silly audit   'function-bind': [ '1.1.2' ],
120 silly audit   'get-intrinsic': [ '1.3.0' ],
120 silly audit   'get-proto': [ '1.0.1' ],
120 silly audit   gopd: [ '1.2.0' ],
120 silly audit   'has-symbols': [ '1.1.0' ],
120 silly audit   'has-tostringtag': [ '1.0.2' ],
120 silly audit   hasown: [ '2.0.2' ],
120 silly audit   immutable: [ '5.1.2' ],
120 silly audit   'is-extglob': [ '2.1.1' ],
120 silly audit   'is-glob': [ '4.0.3' ],
120 silly audit   'is-number': [ '7.0.0' ],
120 silly audit   'math-intrinsics': [ '1.1.0' ],
120 silly audit   micromatch: [ '4.0.8' ],
120 silly audit   'mime-db': [ '1.52.0' ],
120 silly audit   'mime-types': [ '2.1.35' ],
120 silly audit   'node-addon-api': [ '7.1.1' ],
120 silly audit   'normalize-path': [ '3.0.0' ],
120 silly audit   picomatch: [ '2.3.1' ],
120 silly audit   readdirp: [ '4.1.2' ],
120 silly audit   'safe-buffer': [ '5.2.1' ],
120 silly audit   sass: [ '1.88.0' ],
120 silly audit   'source-map-js': [ '1.2.1' ],
120 silly audit   'to-regex-range': [ '5.0.1' ],
120 silly audit   'types-registry': [ '0.1.724' ],
120 silly audit   'undici-types': [ '6.21.0' ],
120 silly audit   '@types/connect': [ '3.4.38' ],
120 silly audit   '@types/core-js': [ '2.5.8' ],
120 silly audit   '@types/html-webpack-plugin': [ '3.2.9' ],
120 silly audit   '@types/js-cookie': [ '3.0.6' ],
120 silly audit   '@types/mockjs': [ '1.0.10' ],
120 silly audit   '@types/nprogress': [ '0.2.3' ],
120 silly audit   '@types/platform': [ '1.3.6' ],
120 silly audit   '@types/request': [ '2.48.12' ],
120 silly audit   '@types/sass-loader': [ '8.0.9' ],
120 silly audit   '@types/script-ext-html-webpack-plugin': [ '2.1.6' ],
120 silly audit   '@types/serve-static': [ '1.15.8' ],
120 silly audit   '@types/svg-sprite-loader': [ '3.9.9' ],
120 silly audit   '@types/uuid': [ '10.0.0' ]
120 silly audit }
121 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-win32-ia32
122 silly reify mark deleted [
122 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-win32-ia32'
122 silly reify ]
123 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-win32-arm64
124 silly reify mark deleted [
124 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-win32-arm64'
124 silly reify ]
125 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-x64-musl
126 silly reify mark deleted [
126 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-x64-musl'
126 silly reify ]
127 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-x64-glibc
128 silly reify mark deleted [
128 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-x64-glibc'
128 silly reify ]
129 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm64-musl
130 silly reify mark deleted [
130 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm64-musl'
130 silly reify ]
131 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm64-glibc
132 silly reify mark deleted [
132 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm64-glibc'
132 silly reify ]
133 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm-musl
134 silly reify mark deleted [
134 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm-musl'
134 silly reify ]
135 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm-glibc
136 silly reify mark deleted [
136 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm-glibc'
136 silly reify ]
137 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-freebsd-x64
138 silly reify mark deleted [
138 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-freebsd-x64'
138 silly reify ]
139 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-darwin-x64
140 silly reify mark deleted [
140 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-darwin-x64'
140 silly reify ]
141 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-darwin-arm64
142 silly reify mark deleted [
142 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-darwin-arm64'
142 silly reify ]
143 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-android-arm64
144 silly reify mark deleted [
144 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-android-arm64'
144 silly reify ]
145 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 16ms
146 silly audit bulk request failed [object Object]
147 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 20ms
148 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
148 verbose audit error     at C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\npm-registry-fetch\lib\check-response.js:103:15
148 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
148 verbose audit error     at async [getReport] (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:336:21)
148 verbose audit error     at async AuditReport.run (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:106:19)
148 verbose audit error     at async Arborist.reify (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\reify.js:268:24)
148 verbose audit error     at async Install.exec (C:\Driver-D\tools\nodejs\node_modules\npm\lib\commands\install.js:150:5)
148 verbose audit error     at async Npm.exec (C:\Driver-D\tools\nodejs\node_modules\npm\lib\npm.js:207:9)
148 verbose audit error     at async module.exports (C:\Driver-D\tools\nodejs\node_modules\npm\lib\cli\entry.js:74:5) {
148 verbose audit error   headers: [Object: null prototype] {
148 verbose audit error     server: [ 'Tengine' ],
148 verbose audit error     date: [ 'Fri, 20 Jun 2025 12:53:06 GMT' ],
148 verbose audit error     'content-type': [ 'application/json' ],
148 verbose audit error     'transfer-encoding': [ 'chunked' ],
148 verbose audit error     connection: [ 'keep-alive' ],
148 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
148 verbose audit error     via: [ 'kunlun8.cn8434[,404666]' ],
148 verbose audit error     'timing-allow-origin': [ '*' ],
148 verbose audit error     eagleid: [ '76b7f16017504239860234291e' ],
148 verbose audit error     'x-fetch-attempts': [ '1' ]
148 verbose audit error   },
148 verbose audit error   statusCode: 404,
148 verbose audit error   code: 'E404',
148 verbose audit error   method: 'POST',
148 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
148 verbose audit error   body: {
148 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
148 verbose audit error   },
148 verbose audit error   pkgid: undefined
148 verbose audit error }
149 silly audit error [object Object]
150 silly audit report null
151 silly ADD
152 silly ADD
153 silly ADD
154 silly ADD
155 silly ADD
156 silly ADD
157 silly ADD
158 silly ADD
159 silly ADD
160 silly ADD
161 silly ADD
162 silly ADD
163 verbose cwd C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8
164 verbose os Windows_NT 10.0.26100
165 verbose node v22.15.0
166 verbose npm  v10.9.2
167 verbose exit 0
168 info ok
