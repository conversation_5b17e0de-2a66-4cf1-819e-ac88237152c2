(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0057cdc6"],{"201a":function(t,e,n){"use strict";n("aa25")},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("1c0b"),l=n("7b0b"),r=n("d039"),o=n("a640"),s=[],c=s.sort,d=r((function(){s.sort(void 0)})),u=r((function(){s.sort(null)})),h=o("sort"),p=d||!u||!h;a({target:"Array",proto:!0,forced:p},{sort:function(t){return void 0===t?c.call(l(this)):c.call(l(this),i(t))}})},5743:function(t,e,n){},"7d41":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.getDeviceListLoading,expression:"getDeviceListLoading"}],attrs:{id:"addUser"}},[n("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加国标设备通道",width:"60%",top:"2rem","close-on-click-modal":!1,visible:t.showDialog,"destroy-on-close":!0,"append-to-body":""},on:{"update:visible":function(e){t.showDialog=e},close:function(e){return t.close()}}},[n("el-form",{attrs:{inline:!0,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索"}},[n("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:t.getDeviceList},model:{value:t.searchSrt,callback:function(e){t.searchSrt=e},expression:"searchSrt"}})],1),n("el-form-item",{attrs:{label:"在线状态"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:t.getDeviceList},model:{value:t.online,callback:function(e){t.online=e},expression:"online"}},[n("el-option",{attrs:{label:"全部",value:""}}),n("el-option",{attrs:{label:"在线",value:"true"}}),n("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),n("el-form-item",{staticStyle:{float:"right"}},[n("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(e){return t.getDeviceList()}}}),n("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("确 定")])],1)],1),n("el-table",{staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"medium",data:t.deviceList,height:t.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"160"}}),n("el-table-column",{attrs:{prop:"deviceId",label:"设备编号","min-width":"200"}}),n("el-table-column",{attrs:{prop:"channelCount",label:"通道数","min-width":"120"}}),n("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"120"}}),n("el-table-column",{attrs:{label:"地址","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[e.row.hostAddress?n("el-tag",{attrs:{size:"medium"}},[t._v(t._s(e.row.hostAddress))]):t._e(),e.row.hostAddress?t._e():n("el-tag",{attrs:{size:"medium"}},[t._v("未知")])],1)]}}])}),n("el-table-column",{attrs:{label:"状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[e.row.onLine?n("el-tag",{attrs:{size:"medium"}},[t._v("在线")]):t._e(),e.row.onLine?t._e():n("el-tag",{attrs:{size:"medium",type:"info"}},[t._v("离线")])],1)]}}])})],1),n("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":t.currentPage,"page-size":t.count,"page-sizes":[10,25,35,50,200,1e3,5e4],layout:"total, sizes, prev, pager, next",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.currentChange}})],1)],1)},i=[],l=(n("d3b7"),n("a888")),r={name:"GbDeviceSelect",directives:{elDragDialog:l["a"]},props:{},data:function(){return{showDialog:!1,deviceList:[],currentDevice:{},searchSrt:"",online:null,videoComponentList:[],updateLooper:0,currentDeviceChannelsLenth:0,winHeight:580,currentPage:1,count:10,total:0,getDeviceListLoading:!1,multipleSelection:[]}},computed:{},mounted:function(){this.initData()},methods:{initData:function(){this.getDeviceList()},currentChange:function(t){this.currentPage=t,this.getDeviceList()},handleSizeChange:function(t){this.count=t,this.getDeviceList()},handleSelectionChange:function(t){this.multipleSelection=t},getDeviceList:function(){var t=this;this.getDeviceListLoading=!0,this.$store.dispatch("device/queryDevices",{page:this.currentPage,count:this.count,query:this.searchSrt,status:this.online}).then((function(e){t.total=e.total,t.deviceList=e.list})).finally((function(){return[t.getDeviceListLoading=!1]}))},openDialog:function(t){this.listChangeCallback=t,this.showDialog=!0},onSubmit:function(){this.listChangeCallback&&this.listChangeCallback(this.multipleSelection),this.showDialog=!1},close:function(){this.showDialog=!1}}},o=r,s=(n("201a"),n("2877")),c=Object(s["a"])(o,a,i,!1,null,null,null);e["a"]=c.exports},a888:function(t,e,n){"use strict";n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319");var a={bind:function(t,e,n){var a=t.querySelector(".el-dialog__header"),i=t.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",i.style.cssText+=";top:0px;";var l=function(){return window.document.currentStyle?function(t,e){return t.currentStyle[e]}:function(t,e){return getComputedStyle(t,!1)[e]}}();a.onmousedown=function(t){var e=t.clientX-a.offsetLeft,r=t.clientY-a.offsetTop,o=i.offsetWidth,s=i.offsetHeight,c=document.body.clientWidth,d=document.body.clientHeight,u=i.offsetLeft,h=c-i.offsetLeft-o,p=i.offsetTop,f=d-i.offsetTop-s,m=l(i,"left"),g=l(i,"top");m.includes("%")?(m=+document.body.clientWidth*(+m.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(m=+m.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(t){var a=t.clientX-e,l=t.clientY-r;-a>u?a=-u:a>h&&(a=h),-l>p?l=-p:l>f&&(l=f),i.style.cssText+=";left:".concat(a+m,"px;top:").concat(l+g,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(t){document.onmousemove=null,document.onmouseup=null}}}},i=function(t){t.directive("el-drag-dialog",a)};window.Vue&&(window["el-drag-dialog"]=a,Vue.use(i)),a.install=i;e["a"]=a},aa25:function(t,e,n){},cd4b:function(t,e,n){"use strict";n("d584")},d439:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container",attrs:{id:"recordPLan"}},[n("div",{staticStyle:{height:"calc(100vh - 124px)"}},[n("el-form",{attrs:{inline:!0,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索"}},[n("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:t.search},model:{value:t.searchSrt,callback:function(e){t.searchSrt=e},expression:"searchSrt"}})],1),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.add()}}},[t._v(" 添加 ")])],1),n("el-form-item",{staticStyle:{float:"right"}},[n("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",size:"mini"},on:{click:function(e){return t.getRecordPlanList()}}})],1)],1),n("el-table",{ref:"recordPlanListTable",staticStyle:{width:"100%"},attrs:{size:"small",data:t.recordPlanList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"name",label:"名称"}}),n("el-table-column",{attrs:{prop:"channelCount",label:"关联通道"}}),n("el-table-column",{attrs:{prop:"updateTime",label:"更新时间"}}),n("el-table-column",{attrs:{prop:"createTime",label:"创建时间"}}),n("el-table-column",{attrs:{label:"操作",width:"300",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"medium",icon:"el-icon-link",type:"text"},on:{click:function(n){return t.link(e.row)}}},[t._v("关联通道")]),n("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(n){return t.edit(e.row)}}},[t._v("编辑")]),n("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(n){return t.deletePlan(e.row)}}},[t._v("删除")])]}}])})],1),n("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":t.currentPage,"page-size":t.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.currentChange}})],1),n("editRecordPlan",{ref:"editRecordPlan"}),n("LinkChannelRecord",{ref:"linkChannelRecord"})],1)},i=[],l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{"text-align":"left"},attrs:{id:"editRecordPlan"}},[n("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"录制计划",width:"900px",top:"2rem","close-on-click-modal":!1,visible:t.showDialog,"destroy-on-close":!0},on:{"update:visible":function(e){t.showDialog=e},close:function(e){return t.close()}}},[n("div",{staticClass:"edit-record-plan",attrs:{id:"shared"}},[n("el-form",{staticStyle:{padding:"0 20px"},attrs:{size:"small"}},[n("el-form-item",[n("el-input",{attrs:{type:"text",placeholder:"请输入计划名称"},model:{value:t.planName,callback:function(e){t.planName=e},expression:"planName"}})],1),n("el-form-item",[n("div",{staticClass:"content"},[n("weekTimePicker",{ref:"weekTimePicker",attrs:{"plan-array":t.planArray}})],1)]),n("el-form-item",[n("div",{staticStyle:{float:"right","margin-top":"20px"}},[n("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("保存")]),n("el-button",{on:{click:t.close}},[t._v("取消")])],1)])],1)],1)])],1)},r=[],o=(n("b0c0"),n("d3b7"),n("a888")),s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"week-time-picker",attrs:{id:"weekTimePicker"}},[n("el-row",{staticStyle:{"margin-left":"0"}},[n("el-col",[n("div",[n("el-row",{staticStyle:{"margin-left":"0"}},[n("el-col",{attrs:{span:24}},[n("div",{staticClass:"time-select-header"},[n("el-button",{on:{click:function(e){return t.selectAll()}}},[t._v("全选")]),n("el-button",{on:{click:function(e){return t.clearTrack()}}},[t._v("清空")]),n("el-button",{on:{click:function(e){return t.removeSelectedTrack()}}},[t._v("删除")])],1),n("el-row",[n("el-col",{attrs:{span:20,offset:2}},[n("div",{staticClass:"time-plan-ruler",staticStyle:{width:"100%"}},t._l(96,(function(e){return n("div",{key:e,class:t.rulerClass(e-1),staticStyle:{width:"1.04167%"}},[0===e||(e-1)%4===0?n("span",{staticClass:"ruler-text"},[t._v(t._s((e-1)/4))]):t._e(),96===e?n("span",{staticClass:"ruler-text"},[t._v("24")]):t._e()])})),0)])],1),t._l(t.weekData,(function(e,a){return n("el-row",{key:a,staticClass:"time-select-main-container"},[n("el-col",{staticClass:"label",attrs:{span:2}},[t._v(t._s(e.name))]),n("el-col",{attrs:{span:20}},[n("div",{staticClass:"day-plan",on:{mousedown:function(e){return t.dayPlanMousedown(e,a)}}},[t._l(e.data,(function(e,i){return n("div",{key:i,staticClass:"track",style:t.getTrackStyle(e),on:{click:function(e){return e.stopPropagation(),t.selectTrack(i,a)},mousedown:function(t){t.stopPropagation()}}},[n("el-tooltip",{directives:[{name:"show",rawName:"v-show",value:t.checkSelected(i,a),expression:"checkSelected(trackIndex, index)"}],ref:"startPointToolTip-"+a+"-"+i,refInFor:!0,attrs:{content:t.getTooltip(e.start),placement:e.end-e.start<100?"bottom":"top",manual:!0,value:t.checkSelected(i,a),effect:"light",transition:"el-zoom-in-top"}},[n("div",{ref:"startPoint",refInFor:!0,staticClass:"hand",staticStyle:{left:"0%"},on:{mousedown:function(e){return e.stopPropagation(),t.startPointMousedown(e,a,i)}}})]),n("el-tooltip",{directives:[{name:"show",rawName:"v-show",value:t.checkSelected(i,a),expression:"checkSelected(trackIndex, index)"}],ref:"endPointToolTip-"+a+"-"+i,refInFor:!0,attrs:{content:t.getTooltip(e.end),placement:"top",manual:!0,value:t.checkSelected(i,a),effect:"light",transition:"el-zoom-in-top"}},[n("div",{staticClass:"hand",staticStyle:{left:"100%"},on:{mousedown:function(e){return e.stopPropagation(),t.endPointMousedown(e,a,i)}}})])],1)})),t.tempTrack.index===a?n("div",{staticClass:"track",style:t.getTrackStyle(t.tempTrack)}):t._e()],2)]),n("el-col",{staticClass:"operate",attrs:{span:2}},[n("el-popover",{ref:"copyBox"+a,refInFor:!0,attrs:{placement:"right",width:"400",trigger:"click"}},[n("div",[n("el-form",{attrs:{size:"mini",inline:!0}},[t._l(t.weekDataForCopy(a),(function(e,a){return n("el-form-item",{key:a,attrs:{label:e.weekData.name}},[n("el-checkbox",{model:{value:t.weekDataCheckBox[e.index],callback:function(n){t.$set(t.weekDataCheckBox,e.index,n)},expression:"weekDataCheckBox[data.index]"}})],1)})),n("el-form-item",[n("div",{staticStyle:{float:"right"}},[n("el-button",{on:{click:function(e){return t.weekDataCheckBoxForAll(a)}}},[t._v("全选")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmitCopy(a)}}},[t._v("确认")]),n("el-button",{on:{click:function(e){return t.closeCopyBox(a)}}},[t._v("取消")])],1)])],2)],1),n("el-button",{attrs:{slot:"reference",type:"text",size:"medium"},slot:"reference"},[t._v("复制")])],1)],1)],1)}))],2)],1)],1)])],1)],1)},c=[],d=(n("99af"),n("4e82"),n("a434"),n("e9c4"),n("b64b"),{name:"WeekTimePicker",props:["planArray"],emits:["update:planArray"],data:function(){return{weekData:[{name:"星期一",data:[]},{name:"星期二",data:[]},{name:"星期三",data:[]},{name:"星期四",data:[]},{name:"星期五",data:[]},{name:"星期六",data:[]},{name:"星期天",data:[]}],weekDataCheckBox:[!1,!1,!1,!1,!1,!1,!1],selectedTrack:{trackIndex:null,index:null},tempTrack:{index:null,start:null,end:null,x:null,clientWidth:null},startPointTrack:{index:null,trackIndex:null,x:null,clientWidth:null,target:null},endPointTrack:{index:null,trackIndex:null,x:null,clientWidth:null,target:null}}},computed:{value:{get:function(){return this.modelValue},set:function(t){this.$emit("update:modelValue",t)}}},watch:{planArray:function(t){for(var e=0;e<t.length;e++)this.weekData[e].data=t[e].data}},created:function(){var t=this;document.addEventListener("click",(function(){t.selectedTrack.trackIndex=null,t.selectedTrack.index=null})),document.addEventListener("mousemove",this.dayPlanMousemove),document.addEventListener("mouseup",this.dayPlanMouseup)},methods:{rulerClass:function(t){return 0===t||t%4===0?"hour ruler-section":"ruler-section"},checkSelected:function(t,e){return e===this.selectedTrack.index&&t===this.selectedTrack.trackIndex},selectTrack:function(t,e){console.log(e),this.selectedTrack!==1e3*e+t&&(this.selectedTrack.index=e,this.selectedTrack.trackIndex=t)},getTrackStyle:function(t){var e=100/24/60*(t.end-t.start),n=100/24/60*t.start;return"left: ".concat(n,"%; width: ").concat(e,"%;")},getTooltip:function(t){var e=Math.floor(t/60),n=e<10?"0"+e:e,a=t-60*e<10?"0"+Math.floor(t-60*e):Math.floor(t-60*e);return n+":"+a},dayPlanMousedown:function(t,e){this.tempTrack.index=e,this.tempTrack.start=t.offsetX/t.target.clientWidth*24*60,this.tempTrack.x=t.screenX,this.tempTrack.clientWidth=t.target.clientWidth,this.selectedTrack.index=null,this.selectedTrack.trackIndex=null},startPointMousedown:function(t,e,n){this.startPointTrack.index=e,this.startPointTrack.trackIndex=n,this.startPointTrack.x=t.screenX,this.startPointTrack.clientWidth=t.target.parentNode.parentNode.clientWidth,this.startPointTrack.target=t.target},endPointMousedown:function(t,e,n){this.endPointTrack.index=e,this.endPointTrack.trackIndex=n,this.endPointTrack.x=t.screenX,this.endPointTrack.clientWidth=t.target.parentNode.parentNode.clientWidth,this.endPointTrack.target=t.target},dayPlanMousemove:function(t){if(null!==this.tempTrack.index){if(t.screenX-this.tempTrack.x===0)return;var e=(t.screenX-this.tempTrack.x)/this.tempTrack.clientWidth*24*60+this.tempTrack.start;e>1440&&(e=1440),this.tempTrack.end=e}else if(null!==this.startPointTrack.trackIndex){if(t.screenX-this.startPointTrack.x===0)return;var n=(t.screenX-this.startPointTrack.x)/this.startPointTrack.clientWidth*24*60+this.weekData[this.startPointTrack.index].data[this.startPointTrack.trackIndex].start;n<0&&(n=0),this.weekData[this.startPointTrack.index].data[this.startPointTrack.trackIndex].start=n,this.startPointTrack.x=t.screenX,this.$refs["startPointToolTip-".concat(this.startPointTrack.index,"-").concat(this.startPointTrack.trackIndex)][0].popperElm.style.left=this.startPointTrack.target.getBoundingClientRect().left-20+"px",this.updateValue()}else if(null!==this.endPointTrack.trackIndex){if(t.screenX-this.endPointTrack.x===0)return;var a=(t.screenX-this.endPointTrack.x)/this.endPointTrack.clientWidth*24*60+this.weekData[this.endPointTrack.index].data[this.endPointTrack.trackIndex].end;a>1440&&(a=1440),this.weekData[this.endPointTrack.index].data[this.endPointTrack.trackIndex].end=a,this.endPointTrack.x=t.screenX,this.$refs["endPointToolTip-".concat(this.endPointTrack.index,"-").concat(this.endPointTrack.trackIndex)][0].popperElm.style.left=this.endPointTrack.target.getBoundingClientRect().left-20+"px",this.updateValue()}},dayPlanMouseup:function(t){if(null!==this.startPointTrack.index){var e=this.weekData[this.startPointTrack.index].data[this.startPointTrack.trackIndex];return this.trackHandler(this.startPointTrack.index,e.start,e.end),this.startPointTrack.index=null,this.startPointTrack.trackIndex=null,this.startPointTrack.x=null,void(this.startPointTrack.clientWidth=null)}if(null!==this.endPointTrack.index){var n=this.weekData[this.endPointTrack.index].data[this.endPointTrack.trackIndex];return this.trackHandler(this.endPointTrack.index,n.start,n.end),this.endPointTrack.index=null,this.endPointTrack.trackIndex=null,this.endPointTrack.x=null,void(this.endPointTrack.clientWidth=null)}if(null!==this.tempTrack.index){if(this.tempTrack.end-this.tempTrack.start<10)return this.tempTrack.index=null,this.tempTrack.start=null,void(this.tempTrack.end=null);var a=this.tempTrack.index;this.weekData[a].data.push({start:this.tempTrack.start,end:this.tempTrack.end}),this.trackHandler(a,this.tempTrack.start,this.tempTrack.end),this.tempTrack.index=null,this.tempTrack.start=null,this.tempTrack.end=null,this.updateValue()}},trackHandler:function(t,e,n){var a=this;this.weekData[t].data=this.checkTrack(this.weekData[t].data),this.selectedTrack.trackIndex=null,setTimeout((function(){a.selectedTrack.index=t;for(var i=0;i<a.weekData[t].data.length;i++){var l=a.weekData[t].data[i];if(l.start<=e&&l.end>=n)return void(a.selectedTrack.trackIndex=i)}}),100)},removeSelectedTrack:function(){this.weekData[this.selectedTrack.index].data.splice(this.selectedTrack.trackIndex,1),this.updateValue()},clearTrack:function(){for(var t=0;t<this.weekData.length;t++){var e=this.weekData[t];e.data.splice(0,e.data.length)}this.updateValue()},selectAll:function(){this.clearTrack();for(var t=0;t<this.weekData.length;t++){var e=this.weekData[t];e.data.push({start:0,end:1440})}this.updateValue()},checkTrack:function(t){if(0===t.length)return[];t.sort((function(t,e){return t.start-e.start}));for(var e=[t[0]],n=1;n<t.length;n++){var a=t[n],i=e[e.length-1];a.start<=i.end?i.end=Math.max(i.end,a.end):e.push(a)}return e},updateValue:function(){this.$emit("update:planArray",this.weekData)},weekDataForCopy:function(t){for(var e=[],n=0;n<this.weekData.length;n++)n!==t&&e.push({weekData:this.weekData[n],index:n});return e},weekDataCheckBoxForAll:function(t){for(var e=0;e<this.weekDataCheckBox.length;e++)e!==t&&this.$set(this.weekDataCheckBox,e,!0)},onSubmitCopy:function(t){for(var e=this.weekData[t].data,n=0;n<this.weekDataCheckBox.length;n++)this.weekDataCheckBox[n]&&this.$set(this.weekData[n],"data",JSON.parse(JSON.stringify(e)));this.closeCopyBox(t)},closeCopyBox:function(t){this.weekDataCheckBox=[!1,!1,!1,!1,!1,!1,!1],this.$refs["copyBox"+t][0].doClose()}}}),u=d,h=(n("dce7"),n("2877")),p=Object(h["a"])(u,s,c,!1,null,"7a9b90cf",null),f=p.exports,m={name:"EditRecordPlan",directives:{elDragDialog:o["a"]},components:{weekTimePicker:f},props:{},data:function(){return{options:[],loading:!1,edit:!1,planName:null,id:null,showDialog:!1,endCallback:"",planArray:[{data:[]},{data:[]},{data:[]},{data:[]},{data:[]},{data:[]},{data:[]}]}},created:function(){},methods:{openDialog:function(t,e){var n=this;this.endCallback=e,this.showDialog=!0,this.edit=!1,this.planArray=[{data:[]},{data:[]},{data:[]},{data:[]},{data:[]},{data:[]},{data:[]}],t&&(console.log(t),this.edit=!0,this.planName=t.name,this.id=t.id,this.$store.dispatch("recordPlan/getPlan",t.id).then((function(t){t&&t.planItemList&&n.handPlanData(t.planItemList)})).catch((function(t){console.log(t)})))},onSubmit:function(){var t=this,e=this.handPlanArray();console.log(e),this.edit?this.$store.dispatch("recordPlan/update",{id:this.id,name:this.planName,planList:e}).then((function(e){t.$message({showClose:!0,message:"更新成功",type:"success"}),t.endCallback()})).catch((function(t){console.error(t)})).finally((function(){t.showDialog=!1})):this.$store.dispatch("recordPlan/addPlan",{name:this.planName,planList:e}).then((function(e){t.$message({showClose:!0,message:"添加成功",type:"success"}),t.endCallback()})).catch((function(e){t.$message({showClose:!0,message:e,type:"error"})})).finally((function(){t.showDialog=!1}))},handPlanData:function(t){for(var e=0;e<t.length;e++){var n=t[e];console.log(n),this.planArray[n.weekDay-1].data.push({start:n.start,end:n.stop})}},handPlanArray:function(){for(var t=[],e=0;e<this.planArray.length;e++)for(var n=this.planArray[e],a=0;a<n.data.length;a++){var i=n.data[a];t.push({start:Math.floor(i.start),stop:Math.floor(i.end),weekDay:e+1,planId:this.id})}return console.log(t),t},close:function(){this.showDialog=!1,this.id=null,this.planName=null,this.byteTime="",this.endCallback="",this.endCallback&&this.endCallback()}}},g=m,k=(n("cd4b"),Object(h["a"])(g,l,r,!1,null,"232c0c38",null)),v=k.exports,b=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{width:"100%","background-color":"#FFFFFF",display:"grid","grid-template-columns":"200px auto"},attrs:{id:"linkChannelRecord"}},[t.showDialog?n("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"},{name:"loading",rawName:"v-loading",value:t.dialogLoading,expression:"dialogLoading"}],attrs:{title:"通道关联",top:"2rem",width:"80%","close-on-click-modal":!1,visible:t.showDialog,"destroy-on-close":!0},on:{"update:visible":function(e){t.showDialog=e},close:function(e){return t.close()}}},[n("div",{staticStyle:{display:"grid","grid-template-columns":"100px auto"}},[n("el-tabs",{attrs:{"tab-position":"left"},on:{"tab-click":t.search},model:{value:t.hasLink,callback:function(e){t.hasLink=e},expression:"hasLink"}},[n("el-tab-pane",{attrs:{label:"未关联",name:"false"}}),n("el-tab-pane",{attrs:{label:"已关联",name:"true"}})],1),n("div",[n("el-form",{attrs:{inline:!0,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索"}},[n("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:t.search},model:{value:t.searchSrt,callback:function(e){t.searchSrt=e},expression:"searchSrt"}})],1),n("el-form-item",{attrs:{label:"在线状态"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:t.search},model:{value:t.online,callback:function(e){t.online=e},expression:"online"}},[n("el-option",{attrs:{label:"全部",value:""}}),n("el-option",{attrs:{label:"在线",value:"true"}}),n("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),n("el-form-item",{attrs:{label:"类型"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:t.search},model:{value:t.channelType,callback:function(e){t.channelType=e},expression:"channelType"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(Object.values(t.$channelTypeList),(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}))],2)],1),n("el-form-item",["true"!==t.hasLink?n("div",[n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.add()}}},[t._v("添加")]),"true"!==t.hasLink?n("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.addByDevice()}}},[t._v("按设备添加")]):t._e(),"true"!==t.hasLink?n("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.addAll()}}},[t._v("添加所有通道")]):t._e()],1):n("div",["true"===t.hasLink?n("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.remove()}}},[t._v("移除")]):t._e(),"true"===t.hasLink?n("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.removeByDevice()}}},[t._v("按设备移除")]):t._e(),"true"===t.hasLink?n("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.removeAll()}}},[t._v("移除所有通道")]):t._e()],1)]),n("el-form-item",{staticStyle:{float:"right"}},[n("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",size:"mini"},on:{click:function(e){return t.getChannelList()}}})],1)],1),n("el-table",{ref:"channelListTable",attrs:{size:"small",data:t.channelList,height:"calc(100vh - 250px)","header-row-class-name":"table-header"},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbDeviceId",label:"编号","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbManufacturer",label:"厂家","min-width":"100"}}),n("el-table-column",{attrs:{label:"类型","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[n("el-tag",{style:t.$channelTypeList[e.row.dataType].style,attrs:{size:"medium",effect:"plain",type:"success"}},[t._v(t._s(t.$channelTypeList[e.row.dataType].name))])],1)]}}],null,!1,3268672348)}),n("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===e.row.gbStatus?n("el-tag",{attrs:{size:"medium"}},[t._v("在线")]):t._e(),"ON"!==e.row.gbStatus?n("el-tag",{attrs:{size:"medium",type:"info"}},[t._v("离线")]):t._e()],1)]}}],null,!1,752981929)})],1),n("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":t.currentPage,"page-size":t.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.currentChange}}),n("gbDeviceSelect",{ref:"gbDeviceSelect"})],1)],1)]):t._e()],1)},w=[],y=n("7d41"),x={name:"LinkChannelRecord",directives:{elDragDialog:o["a"]},components:{gbDeviceSelect:y["a"]},data:function(){return{dialogLoading:!1,showDialog:!1,chooseData:{},channelList:[],searchSrt:"",channelType:"",online:"",hasLink:"false",currentPage:1,count:15,total:0,loading:!1,planId:null,loadSnap:{},multipleSelection:[]}},created:function(){},destroyed:function(){},methods:{openDialog:function(t,e){this.planId=t,this.showDialog=!0,this.closeCallback=e,this.initData()},initData:function(){this.currentPage=1,this.count=15,this.total=0,this.getChannelList()},currentChange:function(t){this.currentPage=t,this.initData()},handleSizeChange:function(t){this.count=t,this.getChannelList()},getChannelList:function(){var t=this;this.$store.dispatch("recordPlan/queryChannelList",{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType,planId:this.planId,hasLink:this.hasLink}).then((function(e){t.total=e.total,t.channelList=e.list,t.$nextTick((function(){t.$refs.channelListTable.doLayout()}))})).catch((function(t){console.log(t)}))},handleSelectionChange:function(t){this.multipleSelection=t},linkPlan:function(t){var e=this;return this.loading=!0,this.$store.dispatch("recordPlan/linkPlan",t).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.getChannelList()})).catch((function(t){e.$message.error({showClose:!0,message:t})})).finally((function(){e.loading=!1}))},add:function(t){for(var e=[],n=0;n<this.multipleSelection.length;n++)e.push(this.multipleSelection[n].gbId);0!==e.length?this.linkPlan({planId:this.planId,channelIds:e}):this.$message.info({showClose:!0,message:"请选择通道"})},addAll:function(t){var e=this;this.$confirm("添加所有通道将包括已经添加到其他计划的通道，确定添加所有通道？","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.linkPlan({planId:e.planId,allLink:!0})})).catch((function(){}))},addByDevice:function(t){var e=this;this.$refs.gbDeviceSelect.openDialog((function(t){for(var n=[],a=0;a<t.length;a++)n.push(t[a].id);e.linkPlan({planId:e.planId,deviceDbIds:n})}))},removeByDevice:function(t){var e=this;this.$refs.gbDeviceSelect.openDialog((function(t){for(var n=[],a=0;a<t.length;a++)n.push(t[a].id);e.linkPlan({deviceDbIds:n})}))},remove:function(t){for(var e=[],n=0;n<this.multipleSelection.length;n++)e.push(this.multipleSelection[n].gbId);0!==e.length?this.linkPlan({channelIds:e}):this.$message.info({showClose:!0,message:"请选择通道"})},removeAll:function(t){var e=this;this.$confirm("确定移除所有通道？","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.linkPlan({planId:e.planId,allLink:!1})})).catch((function(){}))},search:function(){this.currentPage=1,this.total=0,this.initData()},refresh:function(){this.initData()}}},T=x,P=Object(h["a"])(T,b,w,!1,null,null,null),D=P.exports,S={name:"RecordPlan",components:{EditRecordPlan:v,LinkChannelRecord:D},data:function(){return{recordPlanList:[],searchSrt:"",currentPage:1,count:15,total:0,loading:!1}},created:function(){this.initData()},destroyed:function(){},methods:{initData:function(){this.getRecordPlanList()},currentChange:function(t){this.currentPage=t,this.initData()},handleSizeChange:function(t){this.count=t,this.getRecordPlanList()},getRecordPlanList:function(){var t=this;this.$store.dispatch("recordPlan/queryList",{page:this.currentPage,count:this.count,query:this.searchSrt}).then((function(e){t.total=e.total,t.recordPlanList=e.list,t.$nextTick((function(){t.$refs.recordPlanListTable.doLayout()}))})).catch((function(t){console.log(t)}))},getSnap:function(t){var e=window.baseUrl?window.baseUrl:"";return e+"/api/device/query/snap/"+this.deviceId+"/"+t.deviceId},search:function(){this.currentPage=1,this.total=0,this.initData()},refresh:function(){this.initData()},add:function(){var t=this;this.$refs.editRecordPlan.openDialog(null,(function(){t.initData()}))},edit:function(t){var e=this;this.$refs.editRecordPlan.openDialog(t,(function(){e.initData()}))},link:function(t){var e=this;this.$refs.linkChannelRecord.openDialog(t.id,(function(){e.initData()}))},deletePlan:function(t){var e=this;this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$store.dispatch("recordPlan/deletePlan",t.id).then((function(){e.$message({showClose:!0,message:"删除成功",type:"success"}),e.initData()})).catch((function(t){console.error(t)}))})).catch((function(){}))}}},C=S,L=Object(h["a"])(C,a,i,!1,null,null,null);e["default"]=L.exports},d584:function(t,e,n){},dce7:function(t,e,n){"use strict";n("5743")},e9c4:function(t,e,n){var a=n("23e7"),i=n("d066"),l=n("d039"),r=i("JSON","stringify"),o=/[\uD800-\uDFFF]/g,s=/^[\uD800-\uDBFF]$/,c=/^[\uDC00-\uDFFF]$/,d=function(t,e,n){var a=n.charAt(e-1),i=n.charAt(e+1);return s.test(t)&&!c.test(i)||c.test(t)&&!s.test(a)?"\\u"+t.charCodeAt(0).toString(16):t},u=l((function(){return'"\\udf06\\ud834"'!==r("\udf06\ud834")||'"\\udead"'!==r("\udead")}));r&&a({target:"JSON",stat:!0,forced:u},{stringify:function(t,e,n){var a=r.apply(null,arguments);return"string"==typeof a?a.replace(o,d):a}})}}]);