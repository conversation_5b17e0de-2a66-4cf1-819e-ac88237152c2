0 verbose cli C:\Driver-D\tools\nodejs\node.exe C:\Driver-D\tools\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.15.0
3 silly config load:file:C:\Driver-D\tools\nodejs\node_modules\npm\npmrc
4 silly config load:file:C:\Driver-D\tools\Microsoft VS Code\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Driver-E\工作\代码文件\nodejs\node_global\etc\npmrc
7 verbose title npm config get prefix
8 verbose argv "config" "get" "prefix"
9 verbose logfile logs-max:10 dir:C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_53_04_175Z-
10 verbose logfile C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_53_04_175Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 verbose cwd C:\Driver-D\tools\Microsoft VS Code
13 verbose os Windows_NT 10.0.26100
14 verbose node v22.15.0
15 verbose npm  v10.9.2
16 verbose exit 0
17 info ok
