﻿#include <Windows.h>

int WINAPI WinMain(
    HINSTANCE hInstance,      // 当前实例句柄
    HINSTANCE hPrevInstance,  // 前一个实例句柄（已弃用）
    LPSTR     lpCmdLine,      // 命令行参数
    int       nCmdShow        // 窗口显示方式
) {
    // 隐藏控制台窗口（如果存在）
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    
    // 无限等待 - 保持进程运行但不消耗CPU
    while (true) {
        ::Sleep(INFINITE);  // 永久休眠，不占用CPU资源
    }
    
    return 0;
}