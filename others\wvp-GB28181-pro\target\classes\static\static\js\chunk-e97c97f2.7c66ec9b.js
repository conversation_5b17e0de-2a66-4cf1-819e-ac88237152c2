(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e97c97f2"],{"0cb1":function(e,t,a){"use strict";a("2e8c2")},"2e8c2":function(e,t,a){},"9ed6":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"login-container"},[a("div",{staticClass:"background"},[a("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"auto-complete":"on","label-position":"left"}},[a("div",{staticStyle:{display:"none"}},[a("input",{attrs:{type:"text",name:"fakeusernameremembered"}}),a("input",{attrs:{type:"password",name:"fakepasswordremembered"}})]),a("div",{staticClass:"title-container"},[a("h3",{staticClass:"title"},[e._v("视频汇聚平台")])]),a("el-form-item",{attrs:{prop:"username"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"user"}})],1),a("el-input",{ref:"username",attrs:{placeholder:"用户名",name:"username",type:"text",tabindex:"1","auto-complete":"on"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),a("el-form-item",{attrs:{prop:"password"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"password"}})],1),a("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:"密码",name:"password",tabindex:"2","auto-complete":"on"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}}),a("span",{staticClass:"show-pwd",on:{click:e.togglePasswordVisibility}},[a("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1),a("el-form-item",{attrs:{prop:"captcha"}},[a("div",{staticClass:"captcha-container"},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"captcha"}})],1),a("el-input",{attrs:{placeholder:"验证码",name:"captcha",tabindex:"3","auto-complete":"off"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.captcha,callback:function(t){e.$set(e.loginForm,"captcha",t)},expression:"loginForm.captcha"}}),a("img",{staticClass:"captcha-image",attrs:{src:e.captchaSrc,alt:"验证码"},on:{click:e.refreshCaptcha}})],1)]),a("el-button",{staticClass:"login-button",attrs:{loading:e.loading,type:"primary"},on:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v(" 登录 ")])],1)],1)])},o=[],s=(a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("61f7")),r={name:"Login",data:function(){var e=function(e,t,a){Object(s["b"])(t)?a():a(new Error("请输入用户名"))},t=function(e,t,a){t?a():a(new Error("请输入密码"))},a=function(e,t,a){t?a():a(new Error("请输入验证码"))};return{loginForm:{username:"",password:"",captcha:""},loginRules:{username:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:t}],captcha:[{required:!0,trigger:"blur",validator:a}]},loading:!1,passwordType:"password",captchaSrc:"/api/captcha?"+(new Date).getTime()}},mounted:function(){var e=this;this.$nextTick((function(){setTimeout((function(){document.querySelectorAll('input[type="text"], input[type="password"]').forEach((function(e){e.value=""})),e.loginForm.username="",e.loginForm.password="",e.loginForm.captcha=""}),100)}))},methods:{togglePasswordVisibility:function(){var e=this;this.passwordType="password"===this.passwordType?"text":"password",this.$nextTick((function(){e.$refs.password.focus()}))},refreshCaptcha:function(){this.captchaSrc="/api/captcha?"+(new Date).getTime()},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){if(!t)return console.log("表单验证失败"),!1;e.loading=!0,e.$store.dispatch("user/login",e.loginForm).then((function(){e.$router.push({path:e.redirect||"/"}),e.loading=!1})).catch((function(){e.loading=!1,e.refreshCaptcha()}))}))}}},i=r,c=(a("0cb1"),a("2877")),l=Object(c["a"])(i,n,o,!1,null,null,null);t["default"]=l.exports}}]);