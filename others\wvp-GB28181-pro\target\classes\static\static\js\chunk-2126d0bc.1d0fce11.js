(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2126d0bc"],{"0da9":function(e,t,i){"use strict";i("9787")},1322:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.getChannelListLoading,expression:"getChannelListLoading"}],attrs:{id:"gbChannelSelect"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加国标通道",width:"60%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"append-to-body":""},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getChannelList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getChannelList},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",{attrs:{label:"类型"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getChannelList},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[i("el-option",{attrs:{label:"全部",value:""}}),e._l(Object.values(e.$channelTypeList),(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),i("el-form-item",[i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确 定")])],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",loading:e.getChannelListLoading},on:{click:function(t){return e.getChannelList()}}})],1)],1),i("el-table",{ref:"channelListTable",staticStyle:{width:"100%"},attrs:{size:"small",data:e.channelList,height:e.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),i("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"180"}}),i("el-table-column",{attrs:{prop:"gbDeviceId",label:"编号","min-width":"180"}}),i("el-table-column",{attrs:{prop:"gbManufacturer",label:"厂家","min-width":"100"}}),i("el-table-column",{attrs:{label:"类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[i("el-tag",{style:e.$channelTypeList[t.row.dataType].style,attrs:{size:"medium",effect:"plain",type:"success"}},[e._v(e._s(e.$channelTypeList[t.row.dataType].name))])],1)]}}])}),i("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.gbStatus?i("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.gbStatus?i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}])})],1),i("div",{staticStyle:{display:"grid","grid-template-columns":"1fr 1fr"}},[i("div",{staticStyle:{"text-align":"left","line-height":"32px"}},[i("i",{staticClass:"el-icon-info"}),e._v(" 未找到通道，可在国标设备/通道中选择编辑按钮， 选择"+e._s("civilCode"===e.dataType?"行政区划":"父节点编码")+" ")]),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[10,25,35,50,200,1e3,5e4],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1)],1)],1)},l=[],n=(i("d3b7"),i("a888")),o={name:"GbChannelSelect",directives:{elDragDialog:n["a"]},props:["dataType","selected"],data:function(){return{showDialog:!1,channelList:[],currentDevice:{},searchSrt:"",online:null,channelType:"",videoComponentList:[],updateLooper:0,currentDeviceChannelsLenth:0,winHeight:580,currentPage:1,count:10,total:0,getChannelListLoading:!1,multipleSelection:[]}},computed:{},methods:{initData:function(){this.getChannelList()},currentChange:function(e){this.currentPage=e,this.getChannelList()},handleSizeChange:function(e){this.count=e,this.getChannelList()},handleSelectionChange:function(e){this.multipleSelection=e},getChannelList:function(){var e=this;this.getChannelListLoading=!0,"civilCode"===this.dataType?this.$store.dispatch("commonChanel/getCivilCodeList",{page:this.currentPage,count:this.count,channelType:this.channelType,query:this.searchSrt,online:this.online}).then((function(t){e.total=t.total,e.channelList=t.list})).finally((function(){e.getChannelListLoading=!1})):this.$store.dispatch("commonChanel/getParentList",{page:this.currentPage,count:this.count,query:this.searchSrt,channelType:this.channelType,online:this.online}).then((function(t){e.total=t.total,e.channelList=t.list})).finally((function(){e.getChannelListLoading=!1}))},openDialog:function(e){this.listChangeCallback=e,this.showDialog=!0,this.initData()},onSubmit:function(){this.listChangeCallback&&this.listChangeCallback(this.multipleSelection),this.showDialog=!1},close:function(){this.showDialog=!1}}},s=o,c=i("2877"),r=Object(c["a"])(s,a,l,!1,null,null,null);t["a"]=r.exports},"1e05":function(e,t,i){},"201a":function(e,t,i){"use strict";i("aa25")},"7d41":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.getDeviceListLoading,expression:"getDeviceListLoading"}],attrs:{id:"addUser"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加国标设备通道",width:"60%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"append-to-body":""},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getDeviceList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.getDeviceList},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.getDeviceList()}}}),i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确 定")])],1)],1),i("el-table",{staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"medium",data:e.deviceList,height:e.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"160"}}),i("el-table-column",{attrs:{prop:"deviceId",label:"设备编号","min-width":"200"}}),i("el-table-column",{attrs:{prop:"channelCount",label:"通道数","min-width":"120"}}),i("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"120"}}),i("el-table-column",{attrs:{label:"地址","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.hostAddress?i("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.hostAddress))]):e._e(),t.row.hostAddress?e._e():i("el-tag",{attrs:{size:"medium"}},[e._v("未知")])],1)]}}])}),i("el-table-column",{attrs:{label:"状态","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.onLine?i("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),t.row.onLine?e._e():i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")])],1)]}}])})],1),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[10,25,35,50,200,1e3,5e4],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1)],1)},l=[],n=(i("d3b7"),i("a888")),o={name:"GbDeviceSelect",directives:{elDragDialog:n["a"]},props:{},data:function(){return{showDialog:!1,deviceList:[],currentDevice:{},searchSrt:"",online:null,videoComponentList:[],updateLooper:0,currentDeviceChannelsLenth:0,winHeight:580,currentPage:1,count:10,total:0,getDeviceListLoading:!1,multipleSelection:[]}},computed:{},mounted:function(){this.initData()},methods:{initData:function(){this.getDeviceList()},currentChange:function(e){this.currentPage=e,this.getDeviceList()},handleSizeChange:function(e){this.count=e,this.getDeviceList()},handleSelectionChange:function(e){this.multipleSelection=e},getDeviceList:function(){var e=this;this.getDeviceListLoading=!0,this.$store.dispatch("device/queryDevices",{page:this.currentPage,count:this.count,query:this.searchSrt,status:this.online}).then((function(t){e.total=t.total,e.deviceList=t.list})).finally((function(){return[e.getDeviceListLoading=!1]}))},openDialog:function(e){this.listChangeCallback=e,this.showDialog=!0},onSubmit:function(){this.listChangeCallback&&this.listChangeCallback(this.multipleSelection),this.showDialog=!1},close:function(){this.showDialog=!1}}},s=o,c=(i("201a"),i("2877")),r=Object(c["a"])(s,a,l,!1,null,null,null);t["a"]=r.exports},"94b9":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"border-right":"1px solid #EBEEF5",padding:"0 20px"},attrs:{id:"DeviceTree"}},[e.showHeader?i("div",{staticClass:"page-header"},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{staticStyle:{visibility:"hidden"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"12rem"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"显示编号"}},[i("el-checkbox",{model:{value:e.showCode,callback:function(t){e.showCode=t},expression:"showCode"}})],1)],1)],1):e._e(),i("div",[e.showAlert&&e.edit?i("el-alert",{staticStyle:{"text-align":"left"},attrs:{title:"操作提示",description:"你可以使用右键菜单管理节点",type:"info"}}):e._e(),i("vue-easy-tree",{ref:"veTree",staticClass:"flow-tree",attrs:{"node-key":"treeId",height:e.treeHeight?e.treeHeight:"78vh",lazy:"",load:e.loadNode,data:e.treeData,props:e.props,"default-expanded-keys":[""]},on:{"node-contextmenu":e.contextmenuEventHandler,"node-click":e.nodeClickHandler},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node;t.data;return[i("span",{staticClass:"custom-tree-node"},[0===a.data.type&&e.chooseId!==a.data.deviceId?i("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#409EFF"}}):e._e(),0===a.data.type&&e.chooseId===a.data.deviceId?i("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#c60135"}}):e._e(),1===a.data.type&&"ON"===a.data.status?i("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#409EFF"}}):e._e(),1===a.data.type&&"ON"!==a.data.status?i("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#808181"}}):e._e(),""!==a.data.deviceId&&e.showCode?i("span",{staticStyle:{"padding-left":"1px"},attrs:{title:a.data.deviceId}},[e._v(e._s(a.label)+"（编号："+e._s(a.data.deviceId)+"）")]):e._e(),""!==a.data.deviceId&&e.showCode?e._e():i("span",{staticStyle:{"padding-left":"1px"},attrs:{title:a.data.deviceId}},[e._v(e._s(a.label))])])]}}])})],1),i("regionEdit",{ref:"regionEdit"}),i("gbDeviceSelect",{ref:"gbDeviceSelect"}),i("GbChannelSelect",{ref:"gbChannelSelect",attrs:{"data-type":"civilCode"}})],1)},l=[],n=(i("d3b7"),i("9331")),o=i.n(n),s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"生成行政区划编码",width:"65rem",top:"2rem",center:"","append-to-body":!0,"close-on-click-modal":!1,visible:e.showVideoDialog,"destroy-on-close":!1},on:{"update:visible":function(t){e.showVideoDialog=t}}},[i("el-tabs",{staticStyle:{padding:"0 1rem",margin:"auto 0"},on:{"tab-click":e.getRegionList},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[i("el-tab-pane",{attrs:{name:"0"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[0].val))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[0].meaning))])]),e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{name:t.name,label:t.deviceId},on:{input:function(i){return e.deviceChange(t)}},model:{value:e.allVal[0].val,callback:function(t){e.$set(e.allVal[0],"val",t)},expression:"allVal[0].val"}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId)+" ")])}))],2),i("el-tab-pane",{attrs:{name:"1"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[1].val?e.allVal[1].val:"--"))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[1].meaning))])]),i("el-radio",{key:-1,staticStyle:{"line-height":"2rem"},attrs:{label:""},on:{input:e.deviceChange},model:{value:e.allVal[1].val,callback:function(t){e.$set(e.allVal[1],"val",t)},expression:"allVal[1].val"}},[e._v(" 不添加 ")]),e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(2)},on:{input:function(i){return e.deviceChange(t)}},model:{value:e.allVal[1].val,callback:function(t){e.$set(e.allVal[1],"val",t)},expression:"allVal[1].val"}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(2))+" ")])}))],2),i("el-tab-pane",{attrs:{name:"2"}},[i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[2].val?e.allVal[2].val:"--"))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[2].meaning))])]),i("el-radio",{key:-1,staticStyle:{"line-height":"2rem"},attrs:{label:""},on:{input:e.deviceChange},model:{value:e.allVal[2].val,callback:function(t){e.$set(e.allVal[2],"val",t)},expression:"allVal[2].val"}},[e._v(" 不添加 ")]),e._l(e.regionList,(function(t){return i("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(4)},on:{input:function(i){return e.deviceChange(t)}},model:{value:e.allVal[2].val,callback:function(t){e.$set(e.allVal[2],"val",t)},expression:"allVal[2].val"}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(4))+" ")])}))],2),i("el-tab-pane",{attrs:{name:"3"}},[e._v(" 请手动输入基层接入单位编码,两位数字 "),i("div",{attrs:{slot:"label"},slot:"label"},[i("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[3].val?e.allVal[3].val:"--"))]),i("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[3].meaning))])]),i("el-input",{attrs:{type:"text",placeholder:"请输入内容",maxlength:"2",disabled:e.allVal[3].lock,"show-word-limit":""},on:{input:e.deviceChange},model:{value:e.allVal[3].val,callback:function(t){e.$set(e.allVal[3],"val",t)},expression:"allVal[3].val"}})],1)],1),i("el-form",{ref:"form",staticStyle:{display:"grid",padding:"1rem 2rem 0 2rem","grid-template-columns":"1fr 1fr 1fr",gap:"1rem"}},[i("el-form-item",{attrs:{label:"名称",prop:"name",size:"mini"}},[i("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),i("el-form-item",{attrs:{label:"编号",prop:"deviceId",size:"mini"}},[i("el-input",{attrs:{autocomplete:"off"},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}})],1),i("el-form-item",{staticStyle:{"margin-top":"22px","margin-bottom":"0"}},[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("保存")]),i("el-button",{on:{click:e.closeModel}},[e._v("取消")])],1)])],1)],1)},c=[],r=(i("b0c0"),i("a888")),d={directives:{elDragDialog:r["a"]},props:{},data:function(){return{showVideoDialog:!1,activeKey:"0",form:{name:"",deviceId:"",parentId:""},allVal:[{id:[1,2],meaning:"省级编码",val:"11",type:"中心编码",lock:!1},{id:[3,4],meaning:"市级编码",val:"",type:"中心编码",lock:!1},{id:[5,6],meaning:"区级编码",val:"",type:"中心编码",lock:!1},{id:[7,8],meaning:"基层接入单位编码",val:"",type:"中心编码",lock:!1}],regionList:[],deviceTypeList:[],industryCodeTypeList:[],networkIdentificationTypeList:[],endCallBck:null}},computed:{},methods:{openDialog:function(e,t,i,a){this.showVideoDialog=!0,this.activeKey="0",this.regionList=[],this.form=t,this.allVal=[{id:[1,2],meaning:"省级编码",val:"11",type:"中心编码",lock:!1},{id:[3,4],meaning:"市级编码",val:"",type:"中心编码",lock:!1},{id:[5,6],meaning:"区级编码",val:"",type:"中心编码",lock:!1},{id:[7,8],meaning:"基层接入单位编码",val:"",type:"中心编码",lock:!1}],this.form.deviceId?(this.form.deviceId.length>=2&&(this.allVal[0].val=this.form.deviceId.substring(0,2),this.activeKey="0"),this.form.deviceId.length>=4&&(this.allVal[1].val=this.form.deviceId.substring(2,4),this.activeKey="1"),this.form.deviceId.length>=6&&(this.allVal[2].val=this.form.deviceId.substring(4,6),this.activeKey="2"),8===this.form.deviceId.length&&(this.allVal[3].val=this.form.deviceId.substring(6,8),this.activeKey="3")):this.form.parentDeviceId&&(this.form.parentDeviceId.length>=2&&(this.allVal[0].val=this.form.parentDeviceId.substring(0,2),this.activeKey="1"),this.form.parentDeviceId.length>=4&&(this.allVal[1].val=this.form.parentDeviceId.substring(2,4),this.activeKey="2"),this.form.parentDeviceId.length>=6&&(this.allVal[2].val=this.form.parentDeviceId.substring(4,6),this.activeKey="3")),this.getRegionList(),this.endCallBck=e},getRegionList:function(){if(console.log("getRegionList"),"0"===this.activeKey)this.queryChildList();else if("1"===this.activeKey||"2"===this.activeKey){var e="";"1"===this.activeKey&&(e=this.allVal[0].val),"2"===this.activeKey&&(e=""===this.allVal[1].val?"":this.allVal[0].val+this.allVal[1].val),"0"!==this.activeKey&&""===e&&this.$message.error({showClose:!0,message:"请先选择上级行政区划"}),""!==e?this.queryChildList(e):this.regionList=[]}},queryChildList:function(e){var t=this;console.log("queryChildList"),this.regionList=[],this.$store.dispatch("region/queryChildListInBase",e).then((function(e){t.regionList=e})).catch((function(e){t.$message.error({showClose:!0,message:e})}))},closeModel:function(){this.showVideoDialog=!1},deviceChange:function(e){console.log(e);var t=this.allVal[0].val;this.allVal[1].val?(t+=this.allVal[1].val,this.allVal[2].val?(t+=this.allVal[2].val,this.allVal[3].val&&(t+=this.allVal[3].val)):this.allVal[3].val=""):(this.allVal[2].val="",this.allVal[3].val=""),this.form.deviceId=t,e&&(this.form.name=e.name)},handleOk:function(){var e=this;this.form.id?this.$store.dispatch("region/update",this.form).then((function(t){"function"===typeof e.endCallBck&&e.endCallBck(e.form),e.showVideoDialog=!1})).catch((function(t){e.$message.error({showClose:!0,message:t})})):this.$store.dispatch("region/add",this.form).then((function(t){"function"===typeof e.endCallBck&&e.endCallBck(e.form),e.showVideoDialog=!1})).catch((function(t){e.$message.error({showClose:!0,message:t})}))}}},h=d,u=(i("a360"),i("2877")),g=Object(u["a"])(h,s,c,!1,null,null,null),v=g.exports,f=i("7d41"),m=i("1322"),p={name:"DeviceTree",components:{GbChannelSelect:m["a"],VueEasyTree:o.a,regionEdit:v,gbDeviceSelect:f["a"]},props:["edit","enableAddChannel","clickEvent","onChannelChange","showHeader","hasChannel","addChannelToCivilCode","treeHeight"],data:function(){return{props:{label:"name"},showCode:!1,showAlert:!0,searchSrt:"",chooseId:"",treeData:[]}},created:function(){},destroyed:function(){},methods:{search:function(){},loadNode:function(e,t){var i=this;if(0===e.level)t([{treeId:"",deviceId:"",name:"根资源组",isLeaf:!1,type:0}]);else if(e.data.deviceId.length<=8){if(e.data.leaf)return void t([]);this.$store.dispatch("region/getTreeList",{query:this.searchSrt,parent:e.data.id,hasChannel:this.hasChannel}).then((function(e){e.length>0&&(i.showAlert=!1),t(e)})).finally((function(){i.locading=!1}))}else t([])},reset:function(){this.$forceUpdate()},contextmenuEventHandler:function(e,t,i,a){var l=this;if(this.edit){if(console.log(i.level),0===i.data.type){var n=[{label:"刷新节点",icon:"el-icon-refresh",disabled:!1,onClick:function(){l.refreshNode(i)}},{label:"新建节点",icon:"el-icon-plus",disabled:!1,onClick:function(){l.addRegion(t.id,i)}},{label:"编辑节点",icon:"el-icon-edit",disabled:1===i.level,onClick:function(){l.editCatalog(t,i)}},{label:"删除节点",icon:"el-icon-delete",disabled:1===i.level,divided:!0,onClick:function(){l.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.removeRegion(t.id,i)})).catch((function(){}))}}];this.enableAddChannel&&(n.push({label:"添加设备",icon:"el-icon-plus",disabled:1===i.level,onClick:function(){l.addChannelFormDevice(t.id,i)}}),n.push({label:"移除设备",icon:"el-icon-delete",disabled:1===i.level,divided:!0,onClick:function(){l.removeChannelFormDevice(t.id,i)}}),n.push({label:"添加通道",icon:"el-icon-plus",disabled:1===i.level,onClick:function(){l.addChannel(t.id,i)}})),this.$contextmenu({items:n,event:e,customClass:"custom-class",zIndex:3e3})}return!1}},removeRegion:function(e,t){this.$store.dispatch("region/deleteRegion",t.data.id).then((function(e){console.log("移除成功"),t.parent.loaded=!1,t.parent.expand()})).catch((function(e){console.log(e)}))},addChannelFormDevice:function(e,t){var i=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],l=0;l<e.length;l++)a.push(e[l].id);i.$store.dispatch("commonChanel/addDeviceToRegion",{civilCode:t.data.deviceId,deviceIds:a}).then((function(e){i.$message.success({showClose:!0,message:"保存成功"}),i.onChannelChange&&i.onChannelChange(),t.loaded=!1,t.expand()})).catch((function(e){console.log(e)})).finally((function(){i.loading=!1}))}))},removeChannelFormDevice:function(e,t){var i=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],l=0;l<e.length;l++)a.push(e[l].id);i.$store.dispatch("commonChanel/deleteDeviceFromRegion",a).then((function(e){i.$message.success({showClose:!0,message:"保存成功"}),i.onChannelChange&&i.onChannelChange(t.data.deviceId),t.loaded=!1,t.expand()})).catch((function(e){console.log(e)})).finally((function(){i.loading=!1}))}))},addChannel:function(e,t){var i=this;this.$refs.gbChannelSelect.openDialog((function(e){console.log("选择的数据"),console.log(e),i.addChannelToCivilCode(t.data.deviceId,e)}))},refreshNode:function(e){e.loaded=!1,e.expand()},refresh:function(e){console.log(e);var t=this.$refs.veTree.getNode(e);t&&(t.loaded=!1,t.expand())},addRegion:function(e,t){console.log(t),this.$refs.regionEdit.openDialog((function(e){t.loaded=!1,t.expand()}),{deviceId:"",name:"",parentId:t.data.id,parentDeviceId:t.data.deviceId})},editCatalog:function(e,t){this.$refs.regionEdit.openDialog((function(e){t.loaded=!1,t.expand()}),t.data)},nodeClickHandler:function(e,t,i){this.chooseId=e.deviceId,this.clickEvent&&this.clickEvent(e)}}},b=p,y=(i("0da9"),Object(u["a"])(b,a,l,!1,null,null,null));t["a"]=y.exports},9787:function(e,t,i){},a360:function(e,t,i){"use strict";i("1e05")},a888:function(e,t,i){"use strict";i("99af"),i("caad"),i("ac1f"),i("2532"),i("5319");var a={bind:function(e,t,i){var a=e.querySelector(".el-dialog__header"),l=e.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",l.style.cssText+=";top:0px;";var n=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();a.onmousedown=function(e){var t=e.clientX-a.offsetLeft,o=e.clientY-a.offsetTop,s=l.offsetWidth,c=l.offsetHeight,r=document.body.clientWidth,d=document.body.clientHeight,h=l.offsetLeft,u=r-l.offsetLeft-s,g=l.offsetTop,v=d-l.offsetTop-c,f=n(l,"left"),m=n(l,"top");f.includes("%")?(f=+document.body.clientWidth*(+f.replace(/\%/g,"")/100),m=+document.body.clientHeight*(+m.replace(/\%/g,"")/100)):(f=+f.replace(/\px/g,""),m=+m.replace(/\px/g,"")),document.onmousemove=function(e){var a=e.clientX-t,n=e.clientY-o;-a>h?a=-h:a>u&&(a=u),-n>g?n=-g:n>v&&(n=v),l.style.cssText+=";left:".concat(a+f,"px;top:").concat(n+m,"px;"),i.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}},l=function(e){e.directive("el-drag-dialog",a)};window.Vue&&(window["el-drag-dialog"]=a,Vue.use(l)),a.install=l;t["a"]=a},aa25:function(e,t,i){}}]);