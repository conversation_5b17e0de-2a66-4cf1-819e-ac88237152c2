(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-19690c36"],{"114e":function(t,e,i){},"2db4":function(t,e,i){"use strict";i("114e")},4678:function(t,e,i){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3b","./en-ie.js":"e1d3b","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function s(t){var e=r(t);return i(e)}function r(t){if(!i.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}s.keys=function(){return Object.keys(n)},s.resolve=r,t.exports=s,s.id="4678"},"480a":function(t,e,i){"use strict";i("cc81")},"4f91":function(t,e,i){"use strict";var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"container",staticStyle:{"background-color":"#000000"},attrs:{id:"h265Player"},on:{dblclick:t.fullscreenSwich}},[i("div",{ref:"playerBox",staticStyle:{width:"100%",height:"100%",margin:"0 auto"},attrs:{id:"glplayer"}}),t.playerLoading?i("div",{staticClass:"player-loading"},[i("i",{staticClass:"el-icon-loading"}),i("span",[t._v("视频加载中")])]):t._e(),t.showButton?i("div",{staticClass:"buttons-box",attrs:{id:"buttonsBox"}},[i("div",{staticClass:"buttons-box-left"},[t.playing?t._e():i("i",{staticClass:"iconfont icon-play h265web-btn",on:{click:t.unPause}}),t.playing?i("i",{staticClass:"iconfont icon-pause h265web-btn",on:{click:t.pause}}):t._e(),i("i",{staticClass:"iconfont icon-stop h265web-btn",on:{click:t.destroy}}),t.isNotMute?i("i",{staticClass:"iconfont icon-audio-high h265web-btn",on:{click:function(e){return t.mute()}}}):t._e(),t.isNotMute?t._e():i("i",{staticClass:"iconfont icon-audio-mute h265web-btn",on:{click:function(e){return t.cancelMute()}}})]),i("div",{staticClass:"buttons-box-right"},[i("i",{staticClass:"iconfont icon-camera1196054easyiconnet h265web-btn",staticStyle:{"font-size":"1rem !important"},on:{click:t.screenshot}}),i("i",{staticClass:"iconfont icon-shuaxin11 h265web-btn",on:{click:t.playBtnClick}}),t.fullscreen?t._e():i("i",{staticClass:"iconfont icon-weibiaoti10 h265web-btn",on:{click:t.fullscreenSwich}}),t.fullscreen?i("i",{staticClass:"iconfont icon-weibiaoti11 h265web-btn",on:{click:t.fullscreenSwich}}):t._e()])]):t._e()])},s=[],r=(i("b0c0"),i("ac1f"),i("5319"),{}),o="base64:********************************************************************************************************************************************************************************************************************************************************************************",a={name:"H265web",props:["videoUrl","error","hasAudio","height","showButton"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1,playerWidth:0,playerHeight:0,inited:!1,playerLoading:!1,mediaInfo:null}},watch:{videoUrl:function(t,e){this.play(t)},playing:function(t,e){this.$emit("playStatusChange",t)},immediate:!0},mounted:function(){var t=this,e=decodeURIComponent(this.$route.params.url);window.onresize=function(){t.updatePlayerDomSize()},this.btnDom=document.getElementById("buttonsBox"),this.ensureGlobalInterceptorActive(),console.log("初始化时的地址为: "+e),e&&this.play(this.videoUrl)},destroyed:function(){r[this._uid]&&r[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.playerLoading=!1},methods:{updatePlayerDomSize:function(){var t=this,e=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(e){t.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(e.parentNode));var i=e.parentNode.clientWidth,n=e.parentNode.clientHeight,s=i,o=9/16*s;n>0&&i>n/9*16&&(o=n,s=n/9*16);var a=Math.min(document.body.clientHeight,document.documentElement.clientHeight);o>a&&(o=a,s=16/9*o),this.$refs.playerBox.style.width=s+"px",this.$refs.playerBox.style.height=o+"px",this.playerWidth=s,this.playerHeight=o,this.playing&&r[this._uid].resize(this.playerWidth,this.playerHeight)},resize:function(t,e){this.playerWidth=t,this.playerHeight=e,this.$refs.playerBox.style.width=t+"px",this.$refs.playerBox.style.height=e+"px",this.playing&&r[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(t){var e=this;this.playerLoading=!0;var i={};r[this._uid]=new window.new265webjs(t,Object.assign({player:"glplayer",width:this.playerWidth,height:this.playerHeight,token:o,extInfo:{coreProbePart:.4,probeSize:8192,ignoreAudio:null==this.hasAudio||this.hasAudio?0:1},debug:!1,debugLevel:0,logLevel:0,disableStats:!0,disableAnalytics:!0,noStats:!0,noLog:!0,silent:!0},i));var n=r[this._uid];n.onOpenFullScreen=function(){e.fullscreen=!0},n.onCloseFullScreen=function(){e.fullscreen=!1},n.onReadyShowDone=function(){var t=n.play();e.playing=t,e.playerLoading=!1},n.onLoadFinish=function(){try{e.loaded=!0,n.mediaInfo&&"function"===typeof n.mediaInfo?e.mediaInfo=n.mediaInfo():console.warn("播放器不支持mediaInfo方法")}catch(t){console.warn("获取媒体信息时出现错误:",t),e.loaded=!0}},n.onPlayTime=function(t){try{null===t||void 0===t||isNaN(t)?console.warn("播放器返回无效的时间值:",t):e.$emit("playTimeChange",t)}catch(i){console.warn("播放器时间回调出现错误:",i)}},n.onSeekFinish=function(){try{console.log("播放器seek完成"),e.$emit("seekFinish")}catch(t){console.warn("播放器seek完成回调出现错误:",t)}},n.do()},screenshot:function(){if(r[this._uid]){var t=document.createElement("canvas");console.log(this.mediaInfo),t.width=this.mediaInfo.meta.size.width,t.height=this.mediaInfo.meta.size.height,r[this._uid].snapshot(t);var e=document.createElement("a");e.download="screenshot.png",e.href=t.toDataURL("image/png").replace("image/png","image/octet-stream"),e.click()}},playBtnClick:function(t){this.play(this.videoUrl)},play:function(t){var e=this;return r[this._uid]?(this.destroy(),void setTimeout((function(){e.play(t)}),100)):t?0===this.playerWidth||0===this.playerHeight?(this.updatePlayerDomSize(),void setTimeout((function(){e.play(t)}),300)):void this.create(t):void 0},unPause:function(){try{if(r[this._uid]&&r[this._uid].play){var t=r[this._uid].play();t&&"function"===typeof t.catch&&t.catch((function(t){"AbortError"!==t.name&&console.warn("恢复播放时出现错误:",t)})),this.playing=r[this._uid].isPlaying()}}catch(e){console.warn("恢复播放时出现错误:",e),this.playing=!1}this.err=""},pause:function(){try{if(r[this._uid]&&r[this._uid].pause){var t=r[this._uid].pause();t&&"function"===typeof t.catch&&t.catch((function(t){"AbortError"!==t.name&&console.warn("暂停播放时出现错误:",t)})),this.playing=r[this._uid].isPlaying()}}catch(e){console.warn("暂停播放时出现错误:",e),this.playing=!1}this.err=""},mute:function(){r[this._uid]&&(r[this._uid].setVoice(0),this.isNotMute=!1)},cancelMute:function(){r[this._uid]&&(r[this._uid].setVoice(1),this.isNotMute=!0)},destroy:function(){var t=this;if(this.playing=!1,this.loaded=!1,this.playerLoading=!1,this.err="",r[this._uid])try{r[this._uid].pause&&r[this._uid].pause(),setTimeout((function(){try{r[t._uid]&&r[t._uid].release&&r[t._uid].release()}catch(e){console.warn("释放播放器资源时出现错误:",e)}finally{r[t._uid]=null,t.clearPlayerDOM()}}),100)}catch(e){console.warn("销毁播放器时出现错误:",e),r[this._uid]=null,this.clearPlayerDOM()}else this.clearPlayerDOM()},clearPlayerDOM:function(){try{if(this.$refs.playerBox)while(this.$refs.playerBox.firstChild)this.$refs.playerBox.removeChild(this.$refs.playerBox.firstChild)}catch(t){console.warn("清理DOM容器时出现错误:",t)}},fullscreenSwich:function(){var t=this.isFullscreen();t?r[this._uid].closeFullScreen():r[this._uid].fullScreen(),this.fullscreen=!t},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1},setPlaybackRate:function(t){try{r[this._uid]&&r[this._uid].setPlaybackRate&&r[this._uid].setPlaybackRate(t)}catch(e){console.warn("设置播放倍速时出现错误:",e)}},seek:function(t){var e=this;try{if(console.log("h265web播放器seek方法被调用，目标时间:",t,"秒"),console.log("播放器状态:",{playerExists:!!r[this._uid],seekMethodExists:!(!r[this._uid]||!r[this._uid].seek),playerUid:this._uid,loaded:this.loaded,playing:this.playing}),r[this._uid]&&r[this._uid].seek){console.log("执行播放器seek操作到:",t,"秒"),t<0&&(console.warn("seek时间小于0，调整为0"),t=0);var i=this.playing;return r[this._uid].seek(t),console.log("播放器seek操作已执行，之前播放状态:",i),setTimeout((function(){try{if(e.$emit("seekFinish"),console.log("h265web播放器seek操作完成"),i&&r[e._uid]&&r[e._uid].play){console.log("尝试恢复播放状态");try{var t=r[e._uid].play();t&&"function"===typeof t.catch&&t.catch((function(t){"AbortError"!==t.name&&console.warn("seek后恢复播放时出现错误:",t)})),e.playing=r[e._uid].isPlaying()}catch(n){console.warn("seek后恢复播放时出现错误:",n)}}}catch(n){console.warn("触发seek完成事件时出现错误:",n)}}),200),!0}return console.warn("播放器未准备好或不支持seek操作",{playerExists:!!r[this._uid],seekMethodExists:!(!r[this._uid]||!r[this._uid].seek)}),!1}catch(n){return console.error("播放器seek时出现错误:",n),!1}},getCurrentTime:function(){try{if(r[this._uid]){if(r[this._uid].getCurrentTime)return r[this._uid].getCurrentTime();if(r[this._uid].getTime)return r[this._uid].getTime();if(void 0!==r[this._uid].currentTime)return r[this._uid].currentTime}return null}catch(t){return console.warn("获取播放器当前时间时出现错误:",t),null}},getPlayerStatus:function(){try{return r[this._uid]?{playing:this.playing,loaded:this.loaded,playerExists:!0,hasSeekMethod:!!r[this._uid].seek,hasTimeMethod:!(!r[this._uid].getCurrentTime&&!r[this._uid].getTime)}:{playing:!1,loaded:!1,playerExists:!1,hasSeekMethod:!1,hasTimeMethod:!1}}catch(t){return console.warn("获取播放器状态时出现错误:",t),null}},ensureGlobalInterceptorActive:function(){try{if(window.h265webInterceptor){var t=window.h265webInterceptor.status();t.active||window.h265webInterceptor.start()}}catch(e){}}}},c=a,h=(i("2db4"),i("2877")),u=Object(h["a"])(c,n,s,!1,null,null,null);e["a"]=u.exports},"5a0c":function(t,e,i){!function(e,i){t.exports=i()}(0,(function(){"use strict";var t=1e3,e=6e4,i=36e5,n="millisecond",s="second",r="minute",o="hour",a="day",c="week",h="month",u="quarter",l="year",d="date",m="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,w={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],i=t%100;return"["+t+(e[(i-20)%10]||e[i]||e[0])+"]"}},b=function(t,e,i){var n=String(t);return!n||n.length>=e?t:""+Array(e+1-n.length).join(i)+t},p={s:b,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),n=Math.floor(i/60),s=i%60;return(e<=0?"+":"-")+b(n,2,"0")+":"+b(s,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var n=12*(i.year()-e.year())+(i.month()-e.month()),s=e.clone().add(n,h),r=i-s<0,o=e.clone().add(n+(r?-1:1),h);return+(-(n+(i-s)/(r?s-o:o-s))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:h,y:l,w:c,d:a,D:d,h:o,m:r,s:s,ms:n,Q:u}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},v="en",y={};y[v]=w;var T="$isDayjsObject",M=function(t){return t instanceof k||!(!t||!t[T])},j=function t(e,i,n){var s;if(!e)return v;if("string"==typeof e){var r=e.toLowerCase();y[r]&&(s=r),i&&(y[r]=i,s=r);var o=e.split("-");if(!s&&o.length>1)return t(o[0])}else{var a=e.name;y[a]=e,s=a}return!n&&s&&(v=s),s||!n&&v},S=function(t,e){if(M(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new k(i)},x=p;x.l=j,x.i=M,x.w=function(t,e){return S(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var k=function(){function w(t){this.$L=j(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[T]=!0}var b=w.prototype;return b.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(x.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var n=e.match(f);if(n){var s=n[2]-1||0,r=(n[7]||"0").substring(0,3);return i?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,r)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,r)}}return new Date(e)}(t),this.init()},b.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},b.$utils=function(){return x},b.isValid=function(){return!(this.$d.toString()===m)},b.isSame=function(t,e){var i=S(t);return this.startOf(e)<=i&&i<=this.endOf(e)},b.isAfter=function(t,e){return S(t)<this.startOf(e)},b.isBefore=function(t,e){return this.endOf(e)<S(t)},b.$g=function(t,e,i){return x.u(t)?this[e]:this.set(i,t)},b.unix=function(){return Math.floor(this.valueOf()/1e3)},b.valueOf=function(){return this.$d.getTime()},b.startOf=function(t,e){var i=this,n=!!x.u(e)||e,u=x.p(t),m=function(t,e){var s=x.w(i.$u?Date.UTC(i.$y,e,t):new Date(i.$y,e,t),i);return n?s:s.endOf(a)},f=function(t,e){return x.w(i.toDate()[t].apply(i.toDate("s"),(n?[0,0,0,0]:[23,59,59,999]).slice(e)),i)},g=this.$W,w=this.$M,b=this.$D,p="set"+(this.$u?"UTC":"");switch(u){case l:return n?m(1,0):m(31,11);case h:return n?m(1,w):m(0,w+1);case c:var v=this.$locale().weekStart||0,y=(g<v?g+7:g)-v;return m(n?b-y:b+(6-y),w);case a:case d:return f(p+"Hours",0);case o:return f(p+"Minutes",1);case r:return f(p+"Seconds",2);case s:return f(p+"Milliseconds",3);default:return this.clone()}},b.endOf=function(t){return this.startOf(t,!1)},b.$set=function(t,e){var i,c=x.p(t),u="set"+(this.$u?"UTC":""),m=(i={},i[a]=u+"Date",i[d]=u+"Date",i[h]=u+"Month",i[l]=u+"FullYear",i[o]=u+"Hours",i[r]=u+"Minutes",i[s]=u+"Seconds",i[n]=u+"Milliseconds",i)[c],f=c===a?this.$D+(e-this.$W):e;if(c===h||c===l){var g=this.clone().set(d,1);g.$d[m](f),g.init(),this.$d=g.set(d,Math.min(this.$D,g.daysInMonth())).$d}else m&&this.$d[m](f);return this.init(),this},b.set=function(t,e){return this.clone().$set(t,e)},b.get=function(t){return this[x.p(t)]()},b.add=function(n,u){var d,m=this;n=Number(n);var f=x.p(u),g=function(t){var e=S(m);return x.w(e.date(e.date()+Math.round(t*n)),m)};if(f===h)return this.set(h,this.$M+n);if(f===l)return this.set(l,this.$y+n);if(f===a)return g(1);if(f===c)return g(7);var w=(d={},d[r]=e,d[o]=i,d[s]=t,d)[f]||1,b=this.$d.getTime()+n*w;return x.w(b,this)},b.subtract=function(t,e){return this.add(-1*t,e)},b.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||m;var n=t||"YYYY-MM-DDTHH:mm:ssZ",s=x.z(this),r=this.$H,o=this.$m,a=this.$M,c=i.weekdays,h=i.months,u=i.meridiem,l=function(t,i,s,r){return t&&(t[i]||t(e,n))||s[i].slice(0,r)},d=function(t){return x.s(r%12||12,t,"0")},f=u||function(t,e,i){var n=t<12?"AM":"PM";return i?n.toLowerCase():n};return n.replace(g,(function(t,n){return n||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return x.s(e.$y,4,"0");case"M":return a+1;case"MM":return x.s(a+1,2,"0");case"MMM":return l(i.monthsShort,a,h,3);case"MMMM":return l(h,a);case"D":return e.$D;case"DD":return x.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return l(i.weekdaysMin,e.$W,c,2);case"ddd":return l(i.weekdaysShort,e.$W,c,3);case"dddd":return c[e.$W];case"H":return String(r);case"HH":return x.s(r,2,"0");case"h":return d(1);case"hh":return d(2);case"a":return f(r,o,!0);case"A":return f(r,o,!1);case"m":return String(o);case"mm":return x.s(o,2,"0");case"s":return String(e.$s);case"ss":return x.s(e.$s,2,"0");case"SSS":return x.s(e.$ms,3,"0");case"Z":return s}return null}(t)||s.replace(":","")}))},b.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},b.diff=function(n,d,m){var f,g=this,w=x.p(d),b=S(n),p=(b.utcOffset()-this.utcOffset())*e,v=this-b,y=function(){return x.m(g,b)};switch(w){case l:f=y()/12;break;case h:f=y();break;case u:f=y()/3;break;case c:f=(v-p)/6048e5;break;case a:f=(v-p)/864e5;break;case o:f=v/i;break;case r:f=v/e;break;case s:f=v/t;break;default:f=v}return m?f:x.a(f)},b.daysInMonth=function(){return this.endOf(h).$D},b.$locale=function(){return y[this.$L]},b.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),n=j(t,e,!0);return n&&(i.$L=n),i},b.clone=function(){return x.w(this.$d,this)},b.toDate=function(){return new Date(this.valueOf())},b.toJSON=function(){return this.isValid()?this.toISOString():null},b.toISOString=function(){return this.$d.toISOString()},b.toString=function(){return this.$d.toUTCString()},w}(),$=k.prototype;return S.prototype=$,[["$ms",n],["$s",s],["$m",r],["$H",o],["$W",a],["$M",h],["$y",l],["$D",d]].forEach((function(t){$[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),S.extend=function(t,e){return t.$i||(t(e,k,S),t.$i=!0),S},S.locale=j,S.isDayjs=M,S.unix=function(t){return S(1e3*t)},S.en=y[v],S.Ls=y,S.p={},S}))},"76f0":function(t,e,i){"use strict";var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"timeLineContainer",staticClass:"timeLineContainer",style:{backgroundColor:t.backgroundColor},on:{touchstart:t.onTouchstart,touchmove:t.onTouchmove,mousedown:t.onMousedown,mouseout:t.onMouseout,mousemove:t.onMousemove,mouseleave:t.onMouseleave}},[i("canvas",{ref:"canvas",staticClass:"canvas",on:{mousewheel:function(e){return e.stopPropagation(),e.preventDefault(),t.onMouseweel(e)}}}),t.showWindowList&&t.windowList&&t.windowList.length>1?i("div",{ref:"windowList",staticClass:"windowList",on:{scroll:t.onWindowListScroll}},t._l(t.windowListInner,(function(e,n){return i("WindowListItem",{key:n,ref:"WindowListItem",refInFor:!0,attrs:{index:n,data:e,"total-m-s":t.totalMS,"start-timestamp":t.startTimestamp,width:t.width,active:e.active},on:{click_window_timeSegments:t.triggerClickWindowTimeSegments,click:function(e){return t.toggleActive(n)}}})})),1):t._e()])},s=[],r=i("2909"),o=i("5530"),a=(i("99af"),i("d81d"),i("a9e3"),i("d3b7"),i("0643"),i("4e3e"),i("a573"),i("159b"),i("5a0c")),c=i.n(a),h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"windowListItem",staticClass:"windowListItem",class:{active:t.active},on:{click:t.onClick}},[i("span",{staticClass:"order"},[t._v(t._s(t.index+1))]),i("canvas",{ref:"canvas",staticClass:"windowListItemCanvas"})])},u=[],l={name:"WindowListItem",props:{index:{type:Number},data:{type:Object,default:function(){return{}}},totalMS:{type:Number},startTimestamp:{type:Number},width:{type:Number},active:{type:Boolean,default:!1}},data:function(){return{height:0,ctx:null}},mounted:function(){this.init(),this.drawTimeSegments()},methods:{init:function(){var t=this.$refs.windowListItem.getBoundingClientRect(),e=t.height;this.height=e-1,this.$refs.canvas.width=this.width,this.$refs.canvas.height=this.height,this.ctx=this.$refs.canvas.getContext("2d")},drawTimeSegments:function(t,e){var i=this;if(this.data.timeSegments&&!(this.data.timeSegments.length<=0)){var n=this.width/this.totalMS;this.data.timeSegments.forEach((function(s){if(s.beginTime<=i.startTimestamp+i.totalMS&&s.endTime>=i.startTimestamp){i.ctx.beginPath();var r,o=(s.beginTime-i.startTimestamp)*n;o<0?(o=0,r=(s.endTime-i.startTimestamp)*n):r=(s.endTime-s.beginTime)*n;var a=void 0===s.startRatio?.6:s.startRatio,c=void 0===s.endRatio?.9:s.endRatio;e?i.ctx.rect(o,i.height*a,r,i.height*(c-a)):(i.ctx.fillStyle=s.color,i.ctx.fillRect(o,i.height*a,r,i.height*(c-a))),t&&t(s)}}))}},clearCanvas:function(){this.ctx.clearRect(0,0,this.width,this.height)},draw:function(){var t=this;this.$nextTick((function(){t.clearCanvas(),t.drawTimeSegments()}))},onClick:function(t){this.$emit("click",t);var e=this.$refs.windowListItem.getBoundingClientRect(),i=e.left,n=e.top,s=t.clientX-i,r=t.clientY-n,o=this.getClickTimeSegments(s,r);o.length>0&&this.$emit("click_window_timeSegments",o,this.index,this.data)},getClickTimeSegments:function(t,e){var i=this;if(!this.data.timeSegments||this.data.timeSegments.length<=0)return[];var n=[];return this.drawTimeSegments((function(s){i.ctx.isPointInPath(t,e)&&n.push(s)}),!0),n},getRect:function(){return this.$refs.windowListItem?this.$refs.windowListItem.getBoundingClientRect():null}}},d=l,m=(i("480a"),i("2877")),f=Object(m["a"])(d,h,u,!1,null,"753df3d2",null),g=f.exports,w=36e5,b=[.5,1,2,6,12,24,72,360,720,8760,87600],p=[1/60,1/60,2/60,1/6,.25,.5,1,4,4,720,7200],v=[.05,1/30,.05,1/3,.5,2,4,4,4,720,7200],y=[function(){return!0},function(t){return t.getMinutes()%5===0},function(t){return t.getMinutes()%10===0},function(t){return 0===t.getMinutes()||30===t.getMinutes()},function(t){return 0===t.getMinutes()},function(t){return t.getHours()%2===0&&0===t.getMinutes()},function(t){return t.getHours()%3===0&&0===t.getMinutes()},function(t){return t.getHours()%12===0&&0===t.getMinutes()},function(t){return!1},function(t){return!0},function(t){return!0}],T=[function(){return!0},function(t){return t.getMinutes()%5===0},function(t){return t.getMinutes()%10===0},function(t){return 0===t.getMinutes()||30===t.getMinutes()},function(t){return t.getHours()%2===0&&0===t.getMinutes()},function(t){return t.getHours()%4===0&&0===t.getMinutes()},function(t){return t.getHours()%3===0&&0===t.getMinutes()},function(t){return t.getHours()%12===0&&0===t.getMinutes()},function(t){return!1},function(t){return!0},function(t){return!0}],M={name:"TimeLine",components:{WindowListItem:g},props:{initTime:{type:[Number,String],default:""},timeRange:{type:Object,default:function(){return{}}},initZoomIndex:{type:Number,default:5},showCenterLine:{type:Boolean,default:!0},centerLineStyle:{type:Object,default:function(){return{width:2,color:"#fff"}}},textColor:{type:String,default:"rgba(151,158,167,1)"},hoverTextColor:{type:String,default:"rgb(194, 202, 215)"},lineColor:{type:String,default:"rgba(151,158,167,1)"},lineHeightRatio:{type:Object,default:function(){return{date:.3,time:.2,none:.1,hover:.3}}},showHoverTime:{type:Boolean,default:!0},hoverTimeFormat:{type:Function},timeSegments:{type:Array,default:function(){return[]}},backgroundColor:{type:String,default:"#262626"},enableZoom:{type:Boolean,default:!0},enableDrag:{type:Boolean,default:!0},windowList:{type:Array,default:function(){return[]}},baseTimeLineHeight:{type:Number,default:50},initSelectWindowTimeLineIndex:{type:Number,default:-1},isMobile:{type:Boolean,default:!1},maxClickDistance:{type:Number,default:3},roundWidthTimeSegments:{type:Boolean,default:!0},customShowTime:{type:Function},showDateAtZero:{type:Boolean,default:!0},extendZOOM:{type:Array,default:function(){return[]}},formatTime:{type:Function}},data:function(){return{width:0,height:0,ctx:null,currentZoomIndex:0,currentTime:0,startTimestamp:0,mousedown:!1,mousedownX:0,mousedownY:0,mousedownCacheStartTimestamp:0,showWindowList:!1,windowListInner:[],mousemoveX:-1,watchTimeList:[]}},computed:{totalMS:function(){return b[this.currentZoomIndex]*w},timeRangeTimestamp:function(){var t={};return this.timeRange.start&&(t.start="number"===typeof this.timeRange.start?this.timeRange.start:new Date(this.timeRange.start).getTime()),this.timeRange.end&&(t.end="number"===typeof this.timeRange.end?this.timeRange.end:new Date(this.timeRange.end).getTime()),t},ACT_ZOOM_HOUR_GRID:function(){return this.isMobile?v:p},ACT_ZOOM_DATE_SHOW_RULE:function(){return this.isMobile?T:y},yearMonthMode:function(){return 9===this.currentZoomIndex},yearMode:function(){return 10===this.currentZoomIndex}},watch:{timeSegments:{deep:!0,handler:"reRender"}},created:function(){this.extendZOOM.forEach((function(t){b.push(t.zoom),p.push(t.zoomHourGrid),v.push(t.mobileZoomHourGrid)}))},mounted:function(){this.setInitData(),this.init(),this.draw(),this.onMouseup=this.onMouseup.bind(this),this.onResize=this.onResize.bind(this),this.onTouchend=this.onTouchend.bind(this),this.isMobile?window.addEventListener("touchend",this.onTouchend):window.addEventListener("mouseup",this.onMouseup),window.addEventListener("resize",this.onResize)},beforeDestroy:function(){this.isMobile?window.removeEventListener("touchend",this.onTouchend):window.removeEventListener("mouseup",this.onMouseup),window.removeEventListener("resize",this.onResize)},methods:{setInitData:function(){var t=this;this.windowListInner=this.windowList.map((function(e,i){return Object(o["a"])(Object(o["a"])({},e),{},{active:t.initSelectWindowTimeLineIndex===i})})),this.currentZoomIndex=this.initZoomIndex>=0&&this.initZoomIndex<b.length?this.initZoomIndex:5,this.startTimestamp=(this.initTime?"number"===typeof this.initTime?this.initTime:new Date(this.initTime).getTime():new Date(c()().format("YYYY-MM-DD 00:00:00")).getTime())-this.totalMS/2,this.fixStartTimestamp()},fixStartTimestamp:function(){var t=this.totalMS/2,e=this.startTimestamp+t;this.timeRangeTimestamp.start&&e<this.timeRangeTimestamp.start&&(this.startTimestamp=this.timeRangeTimestamp.start-t),this.timeRangeTimestamp.end&&e>this.timeRangeTimestamp.end&&(this.startTimestamp=this.timeRangeTimestamp.end-t)},init:function(){var t=this.$refs.timeLineContainer.getBoundingClientRect(),e=t.width,i=t.height;this.width=e,this.height=this.windowList.length>1?this.baseTimeLineHeight:i,this.$refs.canvas.width=this.width,this.$refs.canvas.height=this.height,this.ctx=this.$refs.canvas.getContext("2d"),this.showWindowList=!0},draw:function(){this.drawTimeSegments(),this.addGraduations(),this.drawMiddleLine(),this.currentTime=this.startTimestamp+this.totalMS/2,this.$emit("timeChange",this.currentTime);try{this.$refs.WindowListItem.forEach((function(t){t.draw()}))}catch(t){}this.updateWatchTime()},updateWatchTime:function(){var t=this;this.watchTimeList.forEach((function(e){if(e.time<t.startTimestamp||e.time>t.startTimestamp+t.totalMS)e.callback(-1,-1);else{var i=(e.time-t.startTimestamp)*(t.width/t.totalMS),n=0,s=t.$refs.canvas.getBoundingClientRect(),r=s.left,o=s.top;if(-1!==e.windowTimeLineIndex&&t.windowList.length>1&&e.windowTimeLineIndex>=0&&e.windowTimeLineIndex<t.windowList.length){var a=t.$refs.WindowListItem[e.windowTimeLineIndex].getRect();n=a?a.top:o}else n=o;e.callback(i+r,n)}}))},drawMiddleLine:function(){if(this.showCenterLine){this.ctx.beginPath();var t=this.centerLineStyle,e=t.width,i=t.color,n=this.width/2;this.drawLine(n,0,n,this.height,e,i)}},addGraduations:function(){this.ctx.beginPath();for(var t=b[this.currentZoomIndex]/this.ACT_ZOOM_HOUR_GRID[this.currentZoomIndex],e=this.ACT_ZOOM_HOUR_GRID[this.currentZoomIndex]*w,i=this.width/t,n=e-this.startTimestamp%e,s=n/e*i,r=0;r<t;r++){var o=this.startTimestamp+n+r*e,a=0;this.yearMode?a=o-new Date("".concat(c()(o).format("YYYY"),"-01-01 00:00:00")).getTime():this.yearMonthMode&&(a=o-new Date("".concat(c()(o).format("YYYY"),"-").concat(c()(o).format("MM"),"-01 00:00:00")).getTime());var h=s+r*i-a/e*i,u=o-a,l=0,d=new Date(u);this.showDateAtZero&&0===d.getHours()&&0===d.getMinutes()?(l=this.height*(void 0===this.lineHeightRatio.date?.3:this.lineHeightRatio.date),this.ctx.fillStyle=this.textColor,this.ctx.fillText(this.graduationTitle(u),h-13,l+15)):this.checkShowTime(d)?(l=this.height*(void 0===this.lineHeightRatio.time?.2:this.lineHeightRatio.time),this.ctx.fillStyle=this.textColor,this.ctx.fillText(this.graduationTitle(u),h-13,l+15)):l=this.height*(void 0===this.lineHeightRatio.none?.1:this.lineHeightRatio.none),this.drawLine(h,0,h,l,1,this.lineColor)}},checkShowTime:function(t){if(this.customShowTime){var e=this.customShowTime(t,this.currentZoomIndex);if(!0===e)return!0;if(!1===e)return!1}return this.ACT_ZOOM_DATE_SHOW_RULE[this.currentZoomIndex](t)},drawTimeSegments:function(t,e){var i=this,n=this.width/this.totalMS;this.timeSegments.forEach((function(s){if(s.beginTime<=i.startTimestamp+i.totalMS){var r=s.endTime>=i.startTimestamp;i.ctx.beginPath();var o,a=(s.beginTime-i.startTimestamp)*n;a<0?(a=0,o=r?(s.endTime-i.startTimestamp)*n:1):o=r?(s.endTime-s.beginTime)*n:1;var c=void 0===s.startRatio?.6:s.startRatio,h=void 0===s.endRatio?.9:s.endRatio;i.roundWidthTimeSegments&&(a=Math.round(a),o=Math.round(o)),o=Math.max(1,o),e?i.ctx.rect(a,i.height*c,o,i.height*(h-c)):(i.ctx.fillStyle=s.color,i.ctx.fillRect(a,i.height*c,o,i.height*(h-c))),t&&t(s)}}))},onTouchstart:function(t){this.isMobile&&(t=t.touches[0],this.onPointerdown(t))},onMousedown:function(t){this.isMobile||this.onPointerdown(t)},onPointerdown:function(t){var e=this.getClientOffset(t);this.mousedownX=e[0],this.mousedownY=e[1],this.mousedown=!0,this.mousedownCacheStartTimestamp=this.startTimestamp,this.$emit("mousedown",t)},onTouchend:function(t){this.isMobile&&(t=t.touches[0],this.onPointerup(t))},onMouseup:function(t){this.isMobile||this.onPointerup(t)},onPointerup:function(t){var e=this,i=this.getClientOffset(t),n=function(){e.mousedown=!1,e.mousedownX=0,e.mousedownY=0,e.mousedownCacheStartTimestamp=0};if(Math.abs(i[0]-this.mousedownX)<=this.maxClickDistance&&Math.abs(i[1]-this.mousedownY)<=this.maxClickDistance)return n(),void this.onClick.apply(this,Object(r["a"])(i));this.mousedown&&this.enableDrag?(n(),this.$emit("dragTimeChange",this.currentTime)):n(),this.$emit("mouseup",t)},onTouchmove:function(t){this.isMobile&&(t=t.touches[0],this.onPointermove(t))},onMousemove:function(t){this.isMobile||this.onPointermove(t)},onPointermove:function(t){var e=this.getClientOffset(t)[0];this.mousemoveX=e,this.mousedown&&this.enableDrag?this.drag(e):this.showHoverTime&&this.hoverShow(e)},onMouseleave:function(){this.mousemoveX=-1},drag:function(t){if(this.enableDrag){var e=this.width/this.totalMS,i=t-this.mousedownX,n=this.totalMS/2,s=this.mousedownCacheStartTimestamp-Math.round(i/e),r=s+n;this.timeRangeTimestamp.start&&r<this.timeRangeTimestamp.start&&(s=this.timeRangeTimestamp.start-n),this.timeRangeTimestamp.end&&r>this.timeRangeTimestamp.end&&(s=this.timeRangeTimestamp.end-n),this.startTimestamp=s,this.clearCanvas(this.width,this.height),this.draw()}},hoverShow:function(t,e){var i=this.width/this.totalMS,n=this.startTimestamp+t/i;e||(this.clearCanvas(this.width,this.height),this.draw());var s=this.height*(void 0===this.lineHeightRatio.hover?.3:this.lineHeightRatio.hover);this.drawLine(t,0,t,s,1,this.lineColor),this.ctx.fillStyle=this.hoverTextColor;var r=this.hoverTimeFormat?this.hoverTimeFormat(n):c()(n).format("YYYY-MM-DD HH:mm:ss"),o=this.ctx.measureText(r).width;this.ctx.fillText(r,t-o/2,s+20)},onMouseout:function(){this.clearCanvas(this.width,this.height),this.draw()},onMouseweel:function(t){if(this.enableZoom){var e=window.event||t,i=Math.max(-1,Math.min(1,e.wheelDelta||-e.detail));i<0?this.currentZoomIndex+1>=b.length-1?this.currentZoomIndex=b.length-1:this.currentZoomIndex++:i>0&&(this.currentZoomIndex-1<=0?this.currentZoomIndex=0:this.currentZoomIndex--),this.clearCanvas(this.width,this.height),this.startTimestamp=this.currentTime-this.totalMS/2,this.draw()}},onClick:function(t,e){var i=this.width/this.totalMS,n=this.startTimestamp+t/i,s=c()(n).format("YYYY-MM-DD HH:mm:ss"),r=this.getClickTimeSegments(t,e);r&&r.length>0?this.$emit("click_timeSegments",r,n,s,t):this.onCanvasClick(n,s,t)},getClickTimeSegments:function(t,e){var i=this,n=[];return this.drawTimeSegments((function(s){i.ctx.isPointInPath(t,e)&&n.push(s)}),!0),n},getClientOffset:function(t){if(!this.$refs.timeLineContainer||!t)return[0,0];var e=this.$refs.timeLineContainer.getBoundingClientRect(),i=e.left,n=e.top;return[t.clientX-i,t.clientY-n]},clearCanvas:function(t,e){this.ctx.clearRect(0,0,t,e)},graduationTitle:function(t){var e=c()(t),i="";return this.formatTime&&(i=this.formatTime(e)),i||(this.yearMode?e.format("YYYY"):this.yearMonthMode?e.format("YYYY-MM"):0===e.hour()&&0===e.minute()&&0===e.millisecond()?e.format("MM-DD"):e.format("HH:mm"))},drawLine:function(t,e,i,n){var s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"#fff";this.ctx.beginPath(),this.ctx.strokeStyle=r,this.ctx.lineWidth=s,this.ctx.moveTo(t,e),this.ctx.lineTo(i,n),this.ctx.stroke()},reRender:function(){var t=this;this.$nextTick((function(){t.clearCanvas(t.width,t.height),t.reset(),t.setInitData(),t.init(),t.draw()}))},reset:function(){this.width=0,this.height=0,this.ctx=null,this.currentZoomIndex=0,this.currentTime=0,this.startTimestamp=0,this.mousedown=!1,this.mousedownX=0,this.mousedownCacheStartTimestamp=0},setTime:function(t){if(!this.mousedown){var e="number"===typeof t?t:new Date(t).getTime();this.startTimestamp=e-this.totalMS/2,this.fixStartTimestamp(),this.clearCanvas(this.width,this.height),this.draw(),-1===this.mousemoveX||this.isMobile||this.hoverShow(this.mousemoveX,!0)}},triggerClickWindowTimeSegments:function(t,e,i){this.$emit("click_window_timeSegments",t,e,i)},setZoom:function(t){this.currentZoomIndex=t>=0&&t<b.length?t:5,this.clearCanvas(this.width,this.height),this.startTimestamp=this.currentTime-this.totalMS/2,this.draw()},toggleActive:function(t){this.windowListInner.forEach((function(t){t.active=!1})),this.windowListInner[t].active=!0,this.$emit("change_window_time_line",t,this.windowListInner[t])},watchTime:function(t,e,i){t&&e&&this.watchTimeList.push({time:"number"===typeof t?t:new Date(t).getTime(),callback:e,windowTimeLineIndex:"number"===typeof i?i-1:-1})},onWindowListScroll:function(){this.updateWatchTime()},onResize:function(){this.init(),this.draw();try{this.$refs.WindowListItem.forEach((function(t){t.init()}))}catch(t){}},onCanvasClick:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.$emit.apply(this,["click_timeline"].concat(e))}}},j=M,S=(i("f5c9"),Object(m["a"])(j,n,s,!1,null,null,null));e["a"]=S.exports},"93bf":function(t,e,i){
/*!
* screenfull
* v5.1.0 - 2020-12-24
* (c) Sindre Sorhus; MIT License
*/
(function(){"use strict";var e="undefined"!==typeof window&&"undefined"!==typeof window.document?window.document:{},i=t.exports,n=function(){for(var t,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,s=i.length,r={};n<s;n++)if(t=i[n],t&&t[1]in e){for(n=0;n<t.length;n++)r[i[0][n]]=t[n];return r}return!1}(),s={change:n.fullscreenchange,error:n.fullscreenerror},r={request:function(t,i){return new Promise(function(s,r){var o=function(){this.off("change",o),s()}.bind(this);this.on("change",o),t=t||e.documentElement;var a=t[n.requestFullscreen](i);a instanceof Promise&&a.then(o).catch(r)}.bind(this))},exit:function(){return new Promise(function(t,i){if(this.isFullscreen){var s=function(){this.off("change",s),t()}.bind(this);this.on("change",s);var r=e[n.exitFullscreen]();r instanceof Promise&&r.then(s).catch(i)}else t()}.bind(this))},toggle:function(t,e){return this.isFullscreen?this.exit():this.request(t,e)},onchange:function(t){this.on("change",t)},onerror:function(t){this.on("error",t)},on:function(t,i){var n=s[t];n&&e.addEventListener(n,i,!1)},off:function(t,i){var n=s[t];n&&e.removeEventListener(n,i,!1)},raw:n};n?(Object.defineProperties(r,{isFullscreen:{get:function(){return Boolean(e[n.fullscreenElement])}},element:{enumerable:!0,get:function(){return e[n.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(e[n.fullscreenEnabled])}}}),i?t.exports=r:window.screenfull=r):i?t.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}})()},cc81:function(t,e,i){},f5c9:function(t,e,i){"use strict";i("fd81")},fd81:function(t,e,i){}}]);