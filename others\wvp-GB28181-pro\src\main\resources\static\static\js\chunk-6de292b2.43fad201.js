(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6de292b2"],{4678:function(t,e,i){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3b","./en-ie.js":"e1d3b","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function s(t){var e=r(t);return i(e)}function r(t){if(!i.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}s.keys=function(){return Object.keys(n)},s.resolve=r,t.exports=s,s.id="4678"},"480a":function(t,e,i){"use strict";i("cc81")},"5a0c":function(t,e,i){!function(e,i){t.exports=i()}(0,(function(){"use strict";var t=1e3,e=6e4,i=36e5,n="millisecond",s="second",r="minute",o="hour",a="day",h="week",u="month",c="quarter",d="year",m="date",f="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,w={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],i=t%100;return"["+t+(e[(i-20)%10]||e[i]||e[0])+"]"}},b=function(t,e,i){var n=String(t);return!n||n.length>=e?t:""+Array(e+1-n.length).join(i)+t},v={s:b,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),n=Math.floor(i/60),s=i%60;return(e<=0?"+":"-")+b(n,2,"0")+":"+b(s,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var n=12*(i.year()-e.year())+(i.month()-e.month()),s=e.clone().add(n,u),r=i-s<0,o=e.clone().add(n+(r?-1:1),u);return+(-(n+(i-s)/(r?s-o:o-s))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:u,y:d,w:h,d:a,D:m,h:o,m:r,s:s,ms:n,Q:c}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},p="en",T={};T[p]=w;var j="$isDayjsObject",M=function(t){return t instanceof $||!(!t||!t[j])},y=function t(e,i,n){var s;if(!e)return p;if("string"==typeof e){var r=e.toLowerCase();T[r]&&(s=r),i&&(T[r]=i,s=r);var o=e.split("-");if(!s&&o.length>1)return t(o[0])}else{var a=e.name;T[a]=e,s=a}return!n&&s&&(p=s),s||!n&&p},S=function(t,e){if(M(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new $(i)},x=v;x.l=y,x.i=M,x.w=function(t,e){return S(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var $=function(){function w(t){this.$L=y(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[j]=!0}var b=w.prototype;return b.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(x.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var n=e.match(l);if(n){var s=n[2]-1||0,r=(n[7]||"0").substring(0,3);return i?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,r)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,r)}}return new Date(e)}(t),this.init()},b.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},b.$utils=function(){return x},b.isValid=function(){return!(this.$d.toString()===f)},b.isSame=function(t,e){var i=S(t);return this.startOf(e)<=i&&i<=this.endOf(e)},b.isAfter=function(t,e){return S(t)<this.startOf(e)},b.isBefore=function(t,e){return this.endOf(e)<S(t)},b.$g=function(t,e,i){return x.u(t)?this[e]:this.set(i,t)},b.unix=function(){return Math.floor(this.valueOf()/1e3)},b.valueOf=function(){return this.$d.getTime()},b.startOf=function(t,e){var i=this,n=!!x.u(e)||e,c=x.p(t),f=function(t,e){var s=x.w(i.$u?Date.UTC(i.$y,e,t):new Date(i.$y,e,t),i);return n?s:s.endOf(a)},l=function(t,e){return x.w(i.toDate()[t].apply(i.toDate("s"),(n?[0,0,0,0]:[23,59,59,999]).slice(e)),i)},g=this.$W,w=this.$M,b=this.$D,v="set"+(this.$u?"UTC":"");switch(c){case d:return n?f(1,0):f(31,11);case u:return n?f(1,w):f(0,w+1);case h:var p=this.$locale().weekStart||0,T=(g<p?g+7:g)-p;return f(n?b-T:b+(6-T),w);case a:case m:return l(v+"Hours",0);case o:return l(v+"Minutes",1);case r:return l(v+"Seconds",2);case s:return l(v+"Milliseconds",3);default:return this.clone()}},b.endOf=function(t){return this.startOf(t,!1)},b.$set=function(t,e){var i,h=x.p(t),c="set"+(this.$u?"UTC":""),f=(i={},i[a]=c+"Date",i[m]=c+"Date",i[u]=c+"Month",i[d]=c+"FullYear",i[o]=c+"Hours",i[r]=c+"Minutes",i[s]=c+"Seconds",i[n]=c+"Milliseconds",i)[h],l=h===a?this.$D+(e-this.$W):e;if(h===u||h===d){var g=this.clone().set(m,1);g.$d[f](l),g.init(),this.$d=g.set(m,Math.min(this.$D,g.daysInMonth())).$d}else f&&this.$d[f](l);return this.init(),this},b.set=function(t,e){return this.clone().$set(t,e)},b.get=function(t){return this[x.p(t)]()},b.add=function(n,c){var m,f=this;n=Number(n);var l=x.p(c),g=function(t){var e=S(f);return x.w(e.date(e.date()+Math.round(t*n)),f)};if(l===u)return this.set(u,this.$M+n);if(l===d)return this.set(d,this.$y+n);if(l===a)return g(1);if(l===h)return g(7);var w=(m={},m[r]=e,m[o]=i,m[s]=t,m)[l]||1,b=this.$d.getTime()+n*w;return x.w(b,this)},b.subtract=function(t,e){return this.add(-1*t,e)},b.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||f;var n=t||"YYYY-MM-DDTHH:mm:ssZ",s=x.z(this),r=this.$H,o=this.$m,a=this.$M,h=i.weekdays,u=i.months,c=i.meridiem,d=function(t,i,s,r){return t&&(t[i]||t(e,n))||s[i].slice(0,r)},m=function(t){return x.s(r%12||12,t,"0")},l=c||function(t,e,i){var n=t<12?"AM":"PM";return i?n.toLowerCase():n};return n.replace(g,(function(t,n){return n||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return x.s(e.$y,4,"0");case"M":return a+1;case"MM":return x.s(a+1,2,"0");case"MMM":return d(i.monthsShort,a,u,3);case"MMMM":return d(u,a);case"D":return e.$D;case"DD":return x.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return d(i.weekdaysMin,e.$W,h,2);case"ddd":return d(i.weekdaysShort,e.$W,h,3);case"dddd":return h[e.$W];case"H":return String(r);case"HH":return x.s(r,2,"0");case"h":return m(1);case"hh":return m(2);case"a":return l(r,o,!0);case"A":return l(r,o,!1);case"m":return String(o);case"mm":return x.s(o,2,"0");case"s":return String(e.$s);case"ss":return x.s(e.$s,2,"0");case"SSS":return x.s(e.$ms,3,"0");case"Z":return s}return null}(t)||s.replace(":","")}))},b.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},b.diff=function(n,m,f){var l,g=this,w=x.p(m),b=S(n),v=(b.utcOffset()-this.utcOffset())*e,p=this-b,T=function(){return x.m(g,b)};switch(w){case d:l=T()/12;break;case u:l=T();break;case c:l=T()/3;break;case h:l=(p-v)/6048e5;break;case a:l=(p-v)/864e5;break;case o:l=p/i;break;case r:l=p/e;break;case s:l=p/t;break;default:l=p}return f?l:x.a(l)},b.daysInMonth=function(){return this.endOf(u).$D},b.$locale=function(){return T[this.$L]},b.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),n=y(t,e,!0);return n&&(i.$L=n),i},b.clone=function(){return x.w(this.$d,this)},b.toDate=function(){return new Date(this.valueOf())},b.toJSON=function(){return this.isValid()?this.toISOString():null},b.toISOString=function(){return this.$d.toISOString()},b.toString=function(){return this.$d.toUTCString()},w}(),k=$.prototype;return S.prototype=k,[["$ms",n],["$s",s],["$m",r],["$H",o],["$W",a],["$M",u],["$y",d],["$D",m]].forEach((function(t){k[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),S.extend=function(t,e){return t.$i||(t(e,$,S),t.$i=!0),S},S.locale=y,S.isDayjs=M,S.unix=function(t){return S(1e3*t)},S.en=T[p],S.Ls=T,S.p={},S}))},"76f0":function(t,e,i){"use strict";var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"timeLineContainer",staticClass:"timeLineContainer",style:{backgroundColor:t.backgroundColor},on:{touchstart:t.onTouchstart,touchmove:t.onTouchmove,mousedown:t.onMousedown,mouseout:t.onMouseout,mousemove:t.onMousemove,mouseleave:t.onMouseleave}},[i("canvas",{ref:"canvas",staticClass:"canvas",on:{mousewheel:function(e){return e.stopPropagation(),e.preventDefault(),t.onMouseweel(e)}}}),t.showWindowList&&t.windowList&&t.windowList.length>1?i("div",{ref:"windowList",staticClass:"windowList",on:{scroll:t.onWindowListScroll}},t._l(t.windowListInner,(function(e,n){return i("WindowListItem",{key:n,ref:"WindowListItem",refInFor:!0,attrs:{index:n,data:e,"total-m-s":t.totalMS,"start-timestamp":t.startTimestamp,width:t.width,active:e.active},on:{click_window_timeSegments:t.triggerClickWindowTimeSegments,click:function(e){return t.toggleActive(n)}}})})),1):t._e()])},s=[],r=i("2909"),o=i("5530"),a=(i("99af"),i("d81d"),i("a9e3"),i("d3b7"),i("0643"),i("4e3e"),i("a573"),i("159b"),i("5a0c")),h=i.n(a),u=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"windowListItem",staticClass:"windowListItem",class:{active:t.active},on:{click:t.onClick}},[i("span",{staticClass:"order"},[t._v(t._s(t.index+1))]),i("canvas",{ref:"canvas",staticClass:"windowListItemCanvas"})])},c=[],d={name:"WindowListItem",props:{index:{type:Number},data:{type:Object,default:function(){return{}}},totalMS:{type:Number},startTimestamp:{type:Number},width:{type:Number},active:{type:Boolean,default:!1}},data:function(){return{height:0,ctx:null}},mounted:function(){this.init(),this.drawTimeSegments()},methods:{init:function(){var t=this.$refs.windowListItem.getBoundingClientRect(),e=t.height;this.height=e-1,this.$refs.canvas.width=this.width,this.$refs.canvas.height=this.height,this.ctx=this.$refs.canvas.getContext("2d")},drawTimeSegments:function(t,e){var i=this;if(this.data.timeSegments&&!(this.data.timeSegments.length<=0)){var n=this.width/this.totalMS;this.data.timeSegments.forEach((function(s){if(s.beginTime<=i.startTimestamp+i.totalMS&&s.endTime>=i.startTimestamp){i.ctx.beginPath();var r,o=(s.beginTime-i.startTimestamp)*n;o<0?(o=0,r=(s.endTime-i.startTimestamp)*n):r=(s.endTime-s.beginTime)*n;var a=void 0===s.startRatio?.6:s.startRatio,h=void 0===s.endRatio?.9:s.endRatio;e?i.ctx.rect(o,i.height*a,r,i.height*(h-a)):(i.ctx.fillStyle=s.color,i.ctx.fillRect(o,i.height*a,r,i.height*(h-a))),t&&t(s)}}))}},clearCanvas:function(){this.ctx.clearRect(0,0,this.width,this.height)},draw:function(){var t=this;this.$nextTick((function(){t.clearCanvas(),t.drawTimeSegments()}))},onClick:function(t){this.$emit("click",t);var e=this.$refs.windowListItem.getBoundingClientRect(),i=e.left,n=e.top,s=t.clientX-i,r=t.clientY-n,o=this.getClickTimeSegments(s,r);o.length>0&&this.$emit("click_window_timeSegments",o,this.index,this.data)},getClickTimeSegments:function(t,e){var i=this;if(!this.data.timeSegments||this.data.timeSegments.length<=0)return[];var n=[];return this.drawTimeSegments((function(s){i.ctx.isPointInPath(t,e)&&n.push(s)}),!0),n},getRect:function(){return this.$refs.windowListItem?this.$refs.windowListItem.getBoundingClientRect():null}}},m=d,f=(i("480a"),i("2877")),l=Object(f["a"])(m,u,c,!1,null,"753df3d2",null),g=l.exports,w=36e5,b=[.5,1,2,6,12,24,72,360,720,8760,87600],v=[1/60,1/60,2/60,1/6,.25,.5,1,4,4,720,7200],p=[.05,1/30,.05,1/3,.5,2,4,4,4,720,7200],T=[function(){return!0},function(t){return t.getMinutes()%5===0},function(t){return t.getMinutes()%10===0},function(t){return 0===t.getMinutes()||30===t.getMinutes()},function(t){return 0===t.getMinutes()},function(t){return t.getHours()%2===0&&0===t.getMinutes()},function(t){return t.getHours()%3===0&&0===t.getMinutes()},function(t){return t.getHours()%12===0&&0===t.getMinutes()},function(t){return!1},function(t){return!0},function(t){return!0}],j=[function(){return!0},function(t){return t.getMinutes()%5===0},function(t){return t.getMinutes()%10===0},function(t){return 0===t.getMinutes()||30===t.getMinutes()},function(t){return t.getHours()%2===0&&0===t.getMinutes()},function(t){return t.getHours()%4===0&&0===t.getMinutes()},function(t){return t.getHours()%3===0&&0===t.getMinutes()},function(t){return t.getHours()%12===0&&0===t.getMinutes()},function(t){return!1},function(t){return!0},function(t){return!0}],M={name:"TimeLine",components:{WindowListItem:g},props:{initTime:{type:[Number,String],default:""},timeRange:{type:Object,default:function(){return{}}},initZoomIndex:{type:Number,default:5},showCenterLine:{type:Boolean,default:!0},centerLineStyle:{type:Object,default:function(){return{width:2,color:"#fff"}}},textColor:{type:String,default:"rgba(151,158,167,1)"},hoverTextColor:{type:String,default:"rgb(194, 202, 215)"},lineColor:{type:String,default:"rgba(151,158,167,1)"},lineHeightRatio:{type:Object,default:function(){return{date:.3,time:.2,none:.1,hover:.3}}},showHoverTime:{type:Boolean,default:!0},hoverTimeFormat:{type:Function},timeSegments:{type:Array,default:function(){return[]}},backgroundColor:{type:String,default:"#262626"},enableZoom:{type:Boolean,default:!0},enableDrag:{type:Boolean,default:!0},windowList:{type:Array,default:function(){return[]}},baseTimeLineHeight:{type:Number,default:50},initSelectWindowTimeLineIndex:{type:Number,default:-1},isMobile:{type:Boolean,default:!1},maxClickDistance:{type:Number,default:3},roundWidthTimeSegments:{type:Boolean,default:!0},customShowTime:{type:Function},showDateAtZero:{type:Boolean,default:!0},extendZOOM:{type:Array,default:function(){return[]}},formatTime:{type:Function}},data:function(){return{width:0,height:0,ctx:null,currentZoomIndex:0,currentTime:0,startTimestamp:0,mousedown:!1,mousedownX:0,mousedownY:0,mousedownCacheStartTimestamp:0,showWindowList:!1,windowListInner:[],mousemoveX:-1,watchTimeList:[]}},computed:{totalMS:function(){return b[this.currentZoomIndex]*w},timeRangeTimestamp:function(){var t={};return this.timeRange.start&&(t.start="number"===typeof this.timeRange.start?this.timeRange.start:new Date(this.timeRange.start).getTime()),this.timeRange.end&&(t.end="number"===typeof this.timeRange.end?this.timeRange.end:new Date(this.timeRange.end).getTime()),t},ACT_ZOOM_HOUR_GRID:function(){return this.isMobile?p:v},ACT_ZOOM_DATE_SHOW_RULE:function(){return this.isMobile?j:T},yearMonthMode:function(){return 9===this.currentZoomIndex},yearMode:function(){return 10===this.currentZoomIndex}},watch:{timeSegments:{deep:!0,handler:"reRender"}},created:function(){this.extendZOOM.forEach((function(t){b.push(t.zoom),v.push(t.zoomHourGrid),p.push(t.mobileZoomHourGrid)}))},mounted:function(){this.setInitData(),this.init(),this.draw(),this.onMouseup=this.onMouseup.bind(this),this.onResize=this.onResize.bind(this),this.onTouchend=this.onTouchend.bind(this),this.isMobile?window.addEventListener("touchend",this.onTouchend):window.addEventListener("mouseup",this.onMouseup),window.addEventListener("resize",this.onResize)},beforeDestroy:function(){this.isMobile?window.removeEventListener("touchend",this.onTouchend):window.removeEventListener("mouseup",this.onMouseup),window.removeEventListener("resize",this.onResize)},methods:{setInitData:function(){var t=this;this.windowListInner=this.windowList.map((function(e,i){return Object(o["a"])(Object(o["a"])({},e),{},{active:t.initSelectWindowTimeLineIndex===i})})),this.currentZoomIndex=this.initZoomIndex>=0&&this.initZoomIndex<b.length?this.initZoomIndex:5,this.startTimestamp=(this.initTime?"number"===typeof this.initTime?this.initTime:new Date(this.initTime).getTime():new Date(h()().format("YYYY-MM-DD 00:00:00")).getTime())-this.totalMS/2,this.fixStartTimestamp()},fixStartTimestamp:function(){var t=this.totalMS/2,e=this.startTimestamp+t;this.timeRangeTimestamp.start&&e<this.timeRangeTimestamp.start&&(this.startTimestamp=this.timeRangeTimestamp.start-t),this.timeRangeTimestamp.end&&e>this.timeRangeTimestamp.end&&(this.startTimestamp=this.timeRangeTimestamp.end-t)},init:function(){var t=this.$refs.timeLineContainer.getBoundingClientRect(),e=t.width,i=t.height;this.width=e,this.height=this.windowList.length>1?this.baseTimeLineHeight:i,this.$refs.canvas.width=this.width,this.$refs.canvas.height=this.height,this.ctx=this.$refs.canvas.getContext("2d"),this.showWindowList=!0},draw:function(){this.drawTimeSegments(),this.addGraduations(),this.drawMiddleLine(),this.currentTime=this.startTimestamp+this.totalMS/2,this.$emit("timeChange",this.currentTime);try{this.$refs.WindowListItem.forEach((function(t){t.draw()}))}catch(t){}this.updateWatchTime()},updateWatchTime:function(){var t=this;this.watchTimeList.forEach((function(e){if(e.time<t.startTimestamp||e.time>t.startTimestamp+t.totalMS)e.callback(-1,-1);else{var i=(e.time-t.startTimestamp)*(t.width/t.totalMS),n=0,s=t.$refs.canvas.getBoundingClientRect(),r=s.left,o=s.top;if(-1!==e.windowTimeLineIndex&&t.windowList.length>1&&e.windowTimeLineIndex>=0&&e.windowTimeLineIndex<t.windowList.length){var a=t.$refs.WindowListItem[e.windowTimeLineIndex].getRect();n=a?a.top:o}else n=o;e.callback(i+r,n)}}))},drawMiddleLine:function(){if(this.showCenterLine){this.ctx.beginPath();var t=this.centerLineStyle,e=t.width,i=t.color,n=this.width/2;this.drawLine(n,0,n,this.height,e,i)}},addGraduations:function(){this.ctx.beginPath();for(var t=b[this.currentZoomIndex]/this.ACT_ZOOM_HOUR_GRID[this.currentZoomIndex],e=this.ACT_ZOOM_HOUR_GRID[this.currentZoomIndex]*w,i=this.width/t,n=e-this.startTimestamp%e,s=n/e*i,r=0;r<t;r++){var o=this.startTimestamp+n+r*e,a=0;this.yearMode?a=o-new Date("".concat(h()(o).format("YYYY"),"-01-01 00:00:00")).getTime():this.yearMonthMode&&(a=o-new Date("".concat(h()(o).format("YYYY"),"-").concat(h()(o).format("MM"),"-01 00:00:00")).getTime());var u=s+r*i-a/e*i,c=o-a,d=0,m=new Date(c);this.showDateAtZero&&0===m.getHours()&&0===m.getMinutes()?(d=this.height*(void 0===this.lineHeightRatio.date?.3:this.lineHeightRatio.date),this.ctx.fillStyle=this.textColor,this.ctx.fillText(this.graduationTitle(c),u-13,d+15)):this.checkShowTime(m)?(d=this.height*(void 0===this.lineHeightRatio.time?.2:this.lineHeightRatio.time),this.ctx.fillStyle=this.textColor,this.ctx.fillText(this.graduationTitle(c),u-13,d+15)):d=this.height*(void 0===this.lineHeightRatio.none?.1:this.lineHeightRatio.none),this.drawLine(u,0,u,d,1,this.lineColor)}},checkShowTime:function(t){if(this.customShowTime){var e=this.customShowTime(t,this.currentZoomIndex);if(!0===e)return!0;if(!1===e)return!1}return this.ACT_ZOOM_DATE_SHOW_RULE[this.currentZoomIndex](t)},drawTimeSegments:function(t,e){var i=this,n=this.width/this.totalMS;this.timeSegments.forEach((function(s){if(s.beginTime<=i.startTimestamp+i.totalMS){var r=s.endTime>=i.startTimestamp;i.ctx.beginPath();var o,a=(s.beginTime-i.startTimestamp)*n;a<0?(a=0,o=r?(s.endTime-i.startTimestamp)*n:1):o=r?(s.endTime-s.beginTime)*n:1;var h=void 0===s.startRatio?.6:s.startRatio,u=void 0===s.endRatio?.9:s.endRatio;i.roundWidthTimeSegments&&(a=Math.round(a),o=Math.round(o)),o=Math.max(1,o),e?i.ctx.rect(a,i.height*h,o,i.height*(u-h)):(i.ctx.fillStyle=s.color,i.ctx.fillRect(a,i.height*h,o,i.height*(u-h))),t&&t(s)}}))},onTouchstart:function(t){this.isMobile&&(t=t.touches[0],this.onPointerdown(t))},onMousedown:function(t){this.isMobile||this.onPointerdown(t)},onPointerdown:function(t){var e=this.getClientOffset(t);this.mousedownX=e[0],this.mousedownY=e[1],this.mousedown=!0,this.mousedownCacheStartTimestamp=this.startTimestamp,this.$emit("mousedown",t)},onTouchend:function(t){this.isMobile&&(t=t.touches[0],this.onPointerup(t))},onMouseup:function(t){this.isMobile||this.onPointerup(t)},onPointerup:function(t){var e=this,i=this.getClientOffset(t),n=function(){e.mousedown=!1,e.mousedownX=0,e.mousedownY=0,e.mousedownCacheStartTimestamp=0};if(Math.abs(i[0]-this.mousedownX)<=this.maxClickDistance&&Math.abs(i[1]-this.mousedownY)<=this.maxClickDistance)return n(),void this.onClick.apply(this,Object(r["a"])(i));this.mousedown&&this.enableDrag?(n(),this.$emit("dragTimeChange",this.currentTime)):n(),this.$emit("mouseup",t)},onTouchmove:function(t){this.isMobile&&(t=t.touches[0],this.onPointermove(t))},onMousemove:function(t){this.isMobile||this.onPointermove(t)},onPointermove:function(t){var e=this.getClientOffset(t)[0];this.mousemoveX=e,this.mousedown&&this.enableDrag?this.drag(e):this.showHoverTime&&this.hoverShow(e)},onMouseleave:function(){this.mousemoveX=-1},drag:function(t){if(this.enableDrag){var e=this.width/this.totalMS,i=t-this.mousedownX,n=this.totalMS/2,s=this.mousedownCacheStartTimestamp-Math.round(i/e),r=s+n;this.timeRangeTimestamp.start&&r<this.timeRangeTimestamp.start&&(s=this.timeRangeTimestamp.start-n),this.timeRangeTimestamp.end&&r>this.timeRangeTimestamp.end&&(s=this.timeRangeTimestamp.end-n),this.startTimestamp=s,this.clearCanvas(this.width,this.height),this.draw()}},hoverShow:function(t,e){var i=this.width/this.totalMS,n=this.startTimestamp+t/i;e||(this.clearCanvas(this.width,this.height),this.draw());var s=this.height*(void 0===this.lineHeightRatio.hover?.3:this.lineHeightRatio.hover);this.drawLine(t,0,t,s,1,this.lineColor),this.ctx.fillStyle=this.hoverTextColor;var r=this.hoverTimeFormat?this.hoverTimeFormat(n):h()(n).format("YYYY-MM-DD HH:mm:ss"),o=this.ctx.measureText(r).width;this.ctx.fillText(r,t-o/2,s+20)},onMouseout:function(){this.clearCanvas(this.width,this.height),this.draw()},onMouseweel:function(t){if(this.enableZoom){var e=window.event||t,i=Math.max(-1,Math.min(1,e.wheelDelta||-e.detail));i<0?this.currentZoomIndex+1>=b.length-1?this.currentZoomIndex=b.length-1:this.currentZoomIndex++:i>0&&(this.currentZoomIndex-1<=0?this.currentZoomIndex=0:this.currentZoomIndex--),this.clearCanvas(this.width,this.height),this.startTimestamp=this.currentTime-this.totalMS/2,this.draw()}},onClick:function(t,e){var i=this.width/this.totalMS,n=this.startTimestamp+t/i,s=h()(n).format("YYYY-MM-DD HH:mm:ss"),r=this.getClickTimeSegments(t,e);r&&r.length>0?this.$emit("click_timeSegments",r,n,s,t):this.onCanvasClick(n,s,t)},getClickTimeSegments:function(t,e){var i=this,n=[];return this.drawTimeSegments((function(s){i.ctx.isPointInPath(t,e)&&n.push(s)}),!0),n},getClientOffset:function(t){if(!this.$refs.timeLineContainer||!t)return[0,0];var e=this.$refs.timeLineContainer.getBoundingClientRect(),i=e.left,n=e.top;return[t.clientX-i,t.clientY-n]},clearCanvas:function(t,e){this.ctx.clearRect(0,0,t,e)},graduationTitle:function(t){var e=h()(t),i="";return this.formatTime&&(i=this.formatTime(e)),i||(this.yearMode?e.format("YYYY"):this.yearMonthMode?e.format("YYYY-MM"):0===e.hour()&&0===e.minute()&&0===e.millisecond()?e.format("MM-DD"):e.format("HH:mm"))},drawLine:function(t,e,i,n){var s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"#fff";this.ctx.beginPath(),this.ctx.strokeStyle=r,this.ctx.lineWidth=s,this.ctx.moveTo(t,e),this.ctx.lineTo(i,n),this.ctx.stroke()},reRender:function(){var t=this;this.$nextTick((function(){t.clearCanvas(t.width,t.height),t.reset(),t.setInitData(),t.init(),t.draw()}))},reset:function(){this.width=0,this.height=0,this.ctx=null,this.currentZoomIndex=0,this.currentTime=0,this.startTimestamp=0,this.mousedown=!1,this.mousedownX=0,this.mousedownCacheStartTimestamp=0},setTime:function(t){if(!this.mousedown){var e="number"===typeof t?t:new Date(t).getTime();this.startTimestamp=e-this.totalMS/2,this.fixStartTimestamp(),this.clearCanvas(this.width,this.height),this.draw(),-1===this.mousemoveX||this.isMobile||this.hoverShow(this.mousemoveX,!0)}},triggerClickWindowTimeSegments:function(t,e,i){this.$emit("click_window_timeSegments",t,e,i)},setZoom:function(t){this.currentZoomIndex=t>=0&&t<b.length?t:5,this.clearCanvas(this.width,this.height),this.startTimestamp=this.currentTime-this.totalMS/2,this.draw()},toggleActive:function(t){this.windowListInner.forEach((function(t){t.active=!1})),this.windowListInner[t].active=!0,this.$emit("change_window_time_line",t,this.windowListInner[t])},watchTime:function(t,e,i){t&&e&&this.watchTimeList.push({time:"number"===typeof t?t:new Date(t).getTime(),callback:e,windowTimeLineIndex:"number"===typeof i?i-1:-1})},onWindowListScroll:function(){this.updateWatchTime()},onResize:function(){this.init(),this.draw();try{this.$refs.WindowListItem.forEach((function(t){t.init()}))}catch(t){}},onCanvasClick:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.$emit.apply(this,["click_timeline"].concat(e))}}},y=M,S=(i("f5c9"),Object(f["a"])(y,n,s,!1,null,null,null));e["a"]=S.exports},"93bf":function(t,e,i){
/*!
* screenfull
* v5.1.0 - 2020-12-24
* (c) Sindre Sorhus; MIT License
*/
(function(){"use strict";var e="undefined"!==typeof window&&"undefined"!==typeof window.document?window.document:{},i=t.exports,n=function(){for(var t,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,s=i.length,r={};n<s;n++)if(t=i[n],t&&t[1]in e){for(n=0;n<t.length;n++)r[i[0][n]]=t[n];return r}return!1}(),s={change:n.fullscreenchange,error:n.fullscreenerror},r={request:function(t,i){return new Promise(function(s,r){var o=function(){this.off("change",o),s()}.bind(this);this.on("change",o),t=t||e.documentElement;var a=t[n.requestFullscreen](i);a instanceof Promise&&a.then(o).catch(r)}.bind(this))},exit:function(){return new Promise(function(t,i){if(this.isFullscreen){var s=function(){this.off("change",s),t()}.bind(this);this.on("change",s);var r=e[n.exitFullscreen]();r instanceof Promise&&r.then(s).catch(i)}else t()}.bind(this))},toggle:function(t,e){return this.isFullscreen?this.exit():this.request(t,e)},onchange:function(t){this.on("change",t)},onerror:function(t){this.on("error",t)},on:function(t,i){var n=s[t];n&&e.addEventListener(n,i,!1)},off:function(t,i){var n=s[t];n&&e.removeEventListener(n,i,!1)},raw:n};n?(Object.defineProperties(r,{isFullscreen:{get:function(){return Boolean(e[n.fullscreenElement])}},element:{enumerable:!0,get:function(){return e[n.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(e[n.fullscreenEnabled])}}}),i?t.exports=r:window.screenfull=r):i?t.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}})()},cc81:function(t,e,i){},f5c9:function(t,e,i){"use strict";i("fd81")},fd81:function(t,e,i){}}]);