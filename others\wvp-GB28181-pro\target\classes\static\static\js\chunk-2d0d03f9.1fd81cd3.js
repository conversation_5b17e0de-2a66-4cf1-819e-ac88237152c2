(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d03f9"],{"66be":function(t,n,i){"use strict";i.r(n);var e=function(){var t=this,n=t.$createElement,i=t._self._c||n;return i("div",{staticClass:"app-container",staticStyle:{margin:"100px 200px"},attrs:{id:"operationsForSystemInfo"}},t._l(t.systemInfoList,(function(n,e){return i("el-descriptions",{key:e,staticStyle:{"margin-bottom":"30px"},attrs:{title:e,column:2,loading:t.loading}},t._l(n,(function(n,e){return i("el-descriptions-item",{key:e},[i("template",{slot:"label"},[i("span",[t._v(t._s(e))])]),n.startsWith("http")?i("a",{attrs:{target:"_blank",href:n}},[t._v(t._s(n))]):i("span",[t._v(t._s(n))])],2)})),1)})),1)},s=[],a=(i("d3b7"),{name:"OperationsSystemInfo",data:function(){return{loading:!1,winHeight:window.innerHeight-220,systemInfoList:{}}},created:function(){this.initData()},methods:{initData:function(){var t=this;this.loading=!0,this.$store.dispatch("server/info").then((function(n){t.systemInfoList=n})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}}}),o=a,r=i("2877"),l=Object(r["a"])(o,e,s,!1,null,null,null);n["default"]=l.exports}}]);