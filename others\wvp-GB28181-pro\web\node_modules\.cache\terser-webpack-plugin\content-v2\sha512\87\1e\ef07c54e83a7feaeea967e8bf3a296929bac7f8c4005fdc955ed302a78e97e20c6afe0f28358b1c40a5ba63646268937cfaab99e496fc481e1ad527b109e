{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-fbccdea4\",\"chunk-b3c5ace6\",\"chunk-3b4a2238\"],{\"0328\":function(e,t,i){\"use strict\";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.isLoging,expression:\"isLoging\"}],attrs:{id:\"devicePlayer\"}},[e.showVideoDialog?i(\"el-dialog\",{directives:[{name:\"el-drag-dialog\",rawName:\"v-el-drag-dialog\"}],attrs:{title:\"视频播放\",top:\"0\",\"close-on-click-modal\":!1,visible:e.showVideoDialog},on:{\"update:visible\":function(t){e.showVideoDialog=t},close:function(t){return e.close()}}},[i(\"div\",{staticStyle:{width:\"100%\",height:\"100%\"}},[Object.keys(this.player).length>1?i(\"el-tabs\",{attrs:{type:\"card\",stretch:!0},on:{\"tab-click\":e.changePlayer},model:{value:e.activePlayer,callback:function(t){e.activePlayer=t},expression:\"activePlayer\"}},[i(\"el-tab-pane\",{attrs:{label:\"Jessibuca\",name:\"jessibuca\"}},[\"jessibuca\"===e.activePlayer?i(\"jessibucaPlayer\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),i(\"el-tab-pane\",{attrs:{label:\"WebRTC\",name:\"webRTC\"}},[\"webRTC\"===e.activePlayer?i(\"rtc-player\",{ref:\"webRTC\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),i(\"el-tab-pane\",{attrs:{label:\"h265web\",name:\"h265web\"}},[\"h265web\"===e.activePlayer?i(\"h265web\",{ref:\"h265web\",attrs:{\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\",\"show-button\":!0}}):e._e()],1)],1):e._e(),1==Object.keys(this.player).length&&this.player.jessibuca?i(\"jessibucaPlayer\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.webRTC?i(\"rtc-player\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.h265web?i(\"h265web\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),i(\"div\",{staticStyle:{\"text-align\":\"right\",\"margin-top\":\"1rem\"},attrs:{id:\"shared\"}},[i(\"el-tabs\",{on:{\"tab-click\":e.tabHandleClick},model:{value:e.tabActiveName,callback:function(t){e.tabActiveName=t},expression:\"tabActiveName\"}},[i(\"el-tab-pane\",{attrs:{label:\"实时视频\",name:\"media\"}},[i(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[i(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"播放地址：\")]),i(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedUrl,callback:function(t){e.$set(e.getPlayerShared,\"sharedUrl\",t)},expression:\"getPlayerShared.sharedUrl\"}},[i(\"template\",{slot:\"append\"},[i(\"i\",{staticClass:\"cpoy-btn el-icon-document-copy\",staticStyle:{cursor:\"pointer\"},attrs:{title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedUrl)}}})])],2)],1),i(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[i(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"iframe：\")]),i(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedIframe,callback:function(t){e.$set(e.getPlayerShared,\"sharedIframe\",t)},expression:\"getPlayerShared.sharedIframe\"}},[i(\"template\",{slot:\"append\"},[i(\"i\",{staticClass:\"cpoy-btn el-icon-document-copy\",staticStyle:{cursor:\"pointer\"},attrs:{title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}}})])],2)],1),i(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[i(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"资源地址：\")]),i(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedRtmp,callback:function(t){e.$set(e.getPlayerShared,\"sharedRtmp\",t)},expression:\"getPlayerShared.sharedRtmp\"}},[i(\"el-button\",{staticStyle:{cursor:\"pointer\"},attrs:{slot:\"append\",icon:\"el-icon-document-copy\",title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}},slot:\"append\"}),e.streamInfo?i(\"el-dropdown\",{attrs:{slot:\"prepend\",trigger:\"click\"},on:{command:e.copyUrl},slot:\"prepend\"},[i(\"el-button\",[e._v(\" 更多地址\"),i(\"i\",{staticClass:\"el-icon-arrow-down el-icon--right\"})]),i(\"el-dropdown-menu\",[e.streamInfo.flv?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.flv}},[i(\"el-tag\",[e._v(\"FLV:\")]),i(\"span\",[e._v(e._s(e.streamInfo.flv))])],1):e._e(),e.streamInfo.https_flv?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_flv}},[i(\"el-tag\",[e._v(\"FLV(https):\")]),i(\"span\",[e._v(e._s(e.streamInfo.https_flv))])],1):e._e(),e.streamInfo.ws_flv?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_flv}},[i(\"el-tag\",[e._v(\"FLV(ws):\")]),i(\"span\",[e._v(e._s(e.streamInfo.ws_flv))])],1):e._e(),e.streamInfo.wss_flv?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_flv}},[i(\"el-tag\",[e._v(\"FLV(wss):\")]),i(\"span\",[e._v(e._s(e.streamInfo.wss_flv))])],1):e._e(),e.streamInfo.fmp4?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.fmp4}},[i(\"el-tag\",[e._v(\"FMP4:\")]),i(\"span\",[e._v(e._s(e.streamInfo.fmp4))])],1):e._e(),e.streamInfo.https_fmp4?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_fmp4}},[i(\"el-tag\",[e._v(\"FMP4(https):\")]),i(\"span\",[e._v(e._s(e.streamInfo.https_fmp4))])],1):e._e(),e.streamInfo.ws_fmp4?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_fmp4}},[i(\"el-tag\",[e._v(\"FMP4(ws):\")]),i(\"span\",[e._v(e._s(e.streamInfo.ws_fmp4))])],1):e._e(),e.streamInfo.wss_fmp4?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_fmp4}},[i(\"el-tag\",[e._v(\"FMP4(wss):\")]),i(\"span\",[e._v(e._s(e.streamInfo.wss_fmp4))])],1):e._e(),e.streamInfo.hls?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.hls}},[i(\"el-tag\",[e._v(\"HLS:\")]),i(\"span\",[e._v(e._s(e.streamInfo.hls))])],1):e._e(),e.streamInfo.https_hls?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_hls}},[i(\"el-tag\",[e._v(\"HLS(https):\")]),i(\"span\",[e._v(e._s(e.streamInfo.https_hls))])],1):e._e(),e.streamInfo.ws_hls?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_hls}},[i(\"el-tag\",[e._v(\"HLS(ws):\")]),i(\"span\",[e._v(e._s(e.streamInfo.ws_hls))])],1):e._e(),e.streamInfo.wss_hls?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_hls}},[i(\"el-tag\",[e._v(\"HLS(wss):\")]),i(\"span\",[e._v(e._s(e.streamInfo.wss_hls))])],1):e._e(),e.streamInfo.ts?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ts}},[i(\"el-tag\",[e._v(\"TS:\")]),i(\"span\",[e._v(e._s(e.streamInfo.ts))])],1):e._e(),e.streamInfo.https_ts?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_ts}},[i(\"el-tag\",[e._v(\"TS(https):\")]),i(\"span\",[e._v(e._s(e.streamInfo.https_ts))])],1):e._e(),e.streamInfo.ws_ts?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_ts}},[i(\"el-tag\",[e._v(\"TS(ws):\")]),i(\"span\",[e._v(e._s(e.streamInfo.ws_ts))])],1):e._e(),e.streamInfo.wss_ts?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_ts}},[i(\"el-tag\",[e._v(\"TS(wss):\")]),i(\"span\",[e._v(e._s(e.streamInfo.wss_ts))])],1):e._e(),e.streamInfo.rtc?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtc}},[i(\"el-tag\",[e._v(\"RTC:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtc))])],1):e._e(),e.streamInfo.rtcs?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtcs}},[i(\"el-tag\",[e._v(\"RTCS:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtcs))])],1):e._e(),e.streamInfo.rtmp?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtmp}},[i(\"el-tag\",[e._v(\"RTMP:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtmp))])],1):e._e(),e.streamInfo.rtmps?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtmps}},[i(\"el-tag\",[e._v(\"RTMPS:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtmps))])],1):e._e(),e.streamInfo.rtsp?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtsp}},[i(\"el-tag\",[e._v(\"RTSP:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtsp))])],1):e._e(),e.streamInfo.rtsps?i(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtsps}},[i(\"el-tag\",[e._v(\"RTSPS:\")]),i(\"span\",[e._v(e._s(e.streamInfo.rtsps))])],1):e._e()],1)],1):e._e()],1)],1)]),e.showPtz?i(\"el-tab-pane\",{attrs:{label:\"云台控制\",name:\"control\"}},[i(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"240px auto\",height:\"180px\",overflow:\"auto\"}},[i(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"6.25rem auto\"}},[i(\"div\",{staticClass:\"control-wrapper\"},[i(\"div\",{staticClass:\"control-btn control-top\",on:{mousedown:function(t){return e.ptzCamera(\"up\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-caret-top\"}),i(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),i(\"div\",{staticClass:\"control-btn control-left\",on:{mousedown:function(t){return e.ptzCamera(\"left\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-caret-left\"}),i(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),i(\"div\",{staticClass:\"control-btn control-bottom\",on:{mousedown:function(t){return e.ptzCamera(\"down\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-caret-bottom\"}),i(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),i(\"div\",{staticClass:\"control-btn control-right\",on:{mousedown:function(t){return e.ptzCamera(\"right\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-caret-right\"}),i(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),i(\"div\",{staticClass:\"control-round\"},[i(\"div\",{staticClass:\"control-round-inner\"},[i(\"i\",{staticClass:\"fa fa-pause-circle\"})])]),i(\"div\",{staticClass:\"contro-speed\",staticStyle:{position:\"absolute\",left:\"4px\",top:\"7rem\",width:\"6.25rem\"}},[i(\"el-slider\",{attrs:{max:100},model:{value:e.controSpeed,callback:function(t){e.controSpeed=t},expression:\"controSpeed\"}})],1)]),i(\"div\",[i(\"div\",{staticClass:\"ptz-btn-box\"},[i(\"div\",{attrs:{title:\"变倍+\"},on:{mousedown:function(t){return e.ptzCamera(\"zoomin\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-zoom-in control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),i(\"div\",{attrs:{title:\"变倍-\"},on:{mousedown:function(t){return e.ptzCamera(\"zoomout\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[i(\"i\",{staticClass:\"el-icon-zoom-out control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])]),i(\"div\",{staticClass:\"ptz-btn-box\"},[i(\"div\",{attrs:{title:\"聚焦+\"},on:{mousedown:function(t){return e.focusCamera(\"near\")},mouseup:function(t){return e.focusCamera(\"stop\")}}},[i(\"i\",{staticClass:\"iconfont icon-bianjiao-fangda control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),i(\"div\",{attrs:{title:\"聚焦-\"},on:{mousedown:function(t){return e.focusCamera(\"far\")},mouseup:function(t){return e.focusCamera(\"stop\")}}},[i(\"i\",{staticClass:\"iconfont icon-bianjiao-suoxiao control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])]),i(\"div\",{staticClass:\"ptz-btn-box\"},[i(\"div\",{attrs:{title:\"光圈+\"},on:{mousedown:function(t){return e.irisCamera(\"in\")},mouseup:function(t){return e.irisCamera(\"stop\")}}},[i(\"i\",{staticClass:\"iconfont icon-guangquan control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),i(\"div\",{attrs:{title:\"光圈-\"},on:{mousedown:function(t){return e.irisCamera(\"out\")},mouseup:function(t){return e.irisCamera(\"stop\")}}},[i(\"i\",{staticClass:\"iconfont icon-guangquan- control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])])])]),\"control\"===e.tabActiveName?i(\"div\",{staticStyle:{\"text-align\":\"left\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{size:\"mini\",placeholder:\"请选择云台功能\"},model:{value:e.ptzMethod,callback:function(t){e.ptzMethod=t},expression:\"ptzMethod\"}},[i(\"el-option\",{attrs:{label:\"预置点\",value:\"preset\"}}),i(\"el-option\",{attrs:{label:\"巡航组\",value:\"cruise\"}}),i(\"el-option\",{attrs:{label:\"自动扫描\",value:\"scan\"}}),i(\"el-option\",{attrs:{label:\"雨刷\",value:\"wiper\"}}),i(\"el-option\",{attrs:{label:\"辅助开关\",value:\"switch\"}})],1),\"preset\"===e.ptzMethod?i(\"ptzPreset\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"cruise\"===e.ptzMethod?i(\"ptzCruising\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"scan\"===e.ptzMethod?i(\"ptzScan\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"wiper\"===e.ptzMethod?i(\"ptzWiper\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"switch\"===e.ptzMethod?i(\"ptzSwitch\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e()],1):e._e()])]):e._e(),i(\"el-tab-pane\",{attrs:{label:\"编码信息\",name:\"codec\"}},[i(\"mediaInfo\",{ref:\"mediaInfo\",attrs:{app:e.app,stream:e.streamId,\"media-server-id\":e.mediaServerId}})],1),e.showBroadcast?i(\"el-tab-pane\",{attrs:{label:\"语音对讲\",name:\"broadcast\"}},[i(\"div\",{staticStyle:{padding:\"0 10px\"}},[i(\"el-radio-group\",{attrs:{disabled:-1!==e.broadcastStatus},model:{value:e.broadcastMode,callback:function(t){e.broadcastMode=t},expression:\"broadcastMode\"}},[i(\"el-radio\",{attrs:{label:!0}},[e._v(\"喊话(Broadcast)\")]),i(\"el-radio\",{attrs:{label:!1}},[e._v(\"对讲(Talk)\")])],1)],1),i(\"div\",{staticClass:\"trank\",staticStyle:{\"text-align\":\"center\"}},[i(\"el-button\",{staticStyle:{\"font-size\":\"32px\",padding:\"24px\",\"margin-top\":\"24px\"},attrs:{type:e.getBroadcastStatus(),disabled:-2===e.broadcastStatus,circle:\"\",icon:\"el-icon-microphone\"},on:{click:function(t){return e.broadcastStatusClick()}}}),i(\"p\",[-2===e.broadcastStatus?i(\"span\",[e._v(\"正在释放资源\")]):e._e(),-1===e.broadcastStatus?i(\"span\",[e._v(\"点击开始对讲\")]):e._e(),0===e.broadcastStatus?i(\"span\",[e._v(\"等待接通中...\")]):e._e(),1===e.broadcastStatus?i(\"span\",[e._v(\"请说话\")]):e._e()])],1)]):e._e()],1)],1)]):e._e()],1)},o=[],n=(i(\"caad\"),i(\"b0c0\"),i(\"e9c4\"),i(\"b64b\"),i(\"2532\"),i(\"a888\")),a=i(\"1c46\"),l=i.n(a),r=i(\"bbf2\"),c=i(\"2655\"),d=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{staticStyle:{width:\"100%\"},attrs:{id:\"ptzPreset\"}},[e._l(e.presetList,(function(t){return i(\"el-tag\",{key:t.presetId,staticStyle:{\"margin-right\":\"1rem\",cursor:\"pointer\",\"margin-bottom\":\"0.6rem\"},attrs:{closable:\"\",size:\"mini\"},on:{close:function(i){return e.delPreset(t)},click:function(i){return e.gotoPreset(t)}}},[e._v(\" \"+e._s(t.presetName?t.presetName:t.presetId)+\" \")])})),e.inputVisible?i(\"el-input\",{ref:\"saveTagInput\",staticStyle:{width:\"300px\",\"vertical-align\":\"bottom\"},attrs:{min:\"1\",max:\"255\",placeholder:\"预置位编号\",\"addon-before\":\"预置位编号\",\"addon-after\":\"(1-255)\",size:\"small\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[i(\"el-button\",{on:{click:function(t){return e.addPreset()}}},[e._v(\"保存\")]),i(\"el-button\",{on:{click:function(t){return e.cancel()}}},[e._v(\"取消\")])]},proxy:!0}],null,!1,3889715118),model:{value:e.ptzPresetId,callback:function(t){e.ptzPresetId=t},expression:\"ptzPresetId\"}}):i(\"el-button\",{attrs:{size:\"small\"},on:{click:e.showInput}},[e._v(\"+ 添加\")])],2)},u=[],h=(i(\"d3b7\"),{name:\"PtzPreset\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{presetList:[],inputVisible:!1,ptzPresetId:\"\"}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch(\"frontEnd/queryPreset\",[this.deviceId,this.channelDeviceId]).then((function(t){e.presetList=t,e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))})).catch((function(e){console.log(e)}))},showInput:function(){var e=this;this.inputVisible=!0,this.$nextTick((function(t){e.$refs.saveTagInput.$refs.input.focus()}))},addPreset:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/addPreset\",[this.deviceId,this.channelDeviceId,this.ptzPresetId]).then((function(t){setTimeout((function(){e.inputVisible=!1,e.ptzPresetId=\"\",e.getPresetList()}),1e3)})).catch((function(i){t.close(),e.inputVisible=!1,e.ptzPresetId=\"\",e.$message({showClose:!0,message:i,type:\"error\"})})).finally((function(){t.close()}))},cancel:function(){this.inputVisible=!1,this.ptzPresetId=\"\"},gotoPreset:function(e){var t=this;console.log(e),this.$store.dispatch(\"frontEnd/callPreset\",[this.deviceId,this.channelDeviceId,e.presetId]).then((function(e){t.$message({showClose:!0,message:\"调用成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})}))},delPreset:function(e){var t=this;this.$confirm(\"确定删除此预置位\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){var i=t.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});t.$store.dispatch(\"frontEnd/deletePreset\",[t.deviceId,t.channelDeviceId,e.presetId]).then((function(e){setTimeout((function(){t.getPresetList()}),1e3)})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){i.close()}))})).catch((function(){}))}}}),p=h,m=i(\"2877\"),f=Object(m[\"a\"])(p,d,u,!1,null,null,null),b=f.exports,g=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"ptzCruising\"}},[i(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"80px auto\",\"line-height\":\"28px\"}},[i(\"span\",[e._v(\"巡航组号: \")]),i(\"el-input\",{attrs:{min:\"1\",max:\"255\",placeholder:\"巡航组号\",\"addon-before\":\"巡航组号\",\"addon-after\":\"(1-255)\",size:\"mini\"},model:{value:e.cruiseId,callback:function(t){e.cruiseId=t},expression:\"cruiseId\"}})],1),i(\"p\",e._l(e.presetList,(function(t,s){return i(\"el-tag\",{key:t.presetId,staticStyle:{\"margin-right\":\"1rem\",cursor:\"pointer\"},attrs:{closable:\"\"},on:{close:function(i){return e.delPreset(t,s)}}},[e._v(\" \"+e._s(t.presetName?t.presetName:t.presetId)+\" \")])})),1),e.selectPresetVisible?i(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[i(\"el-form-item\",[i(\"el-select\",{attrs:{placeholder:\"请选择预置点\"},model:{value:e.selectPreset,callback:function(t){e.selectPreset=t},expression:\"selectPreset\"}},e._l(e.allPresetList,(function(e){return i(\"el-option\",{key:e.presetId,attrs:{label:e.presetName,value:e}})})),1)],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.addCruisePoint}},[e._v(\"保存\")]),i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.cancelAddCruisePoint}},[e._v(\"取消\")])],1)],1):i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.selectPresetVisible=!0}}},[e._v(\"添加巡航点\")]),e.setSpeedVisible?i(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[i(\"el-form-item\",[e.setSpeedVisible?i(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航速度\",\"addon-before\":\"巡航速度\",\"addon-after\":\"(1-4095)\",size:\"mini\"},model:{value:e.cruiseSpeed,callback:function(t){e.cruiseSpeed=t},expression:\"cruiseSpeed\"}}):e._e()],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setCruiseSpeed}},[e._v(\"保存\")]),i(\"el-button\",{on:{click:e.cancelSetCruiseSpeed}},[e._v(\"取消\")])],1)],1):i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v(\"设置巡航速度\")]),e.setTimeVisible?i(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[i(\"el-form-item\",[i(\"el-input\",{staticStyle:{width:\"100%\"},attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航停留时间(秒)\",\"addon-before\":\"巡航停留时间(秒)\",\"addon-after\":\"(1-4095)\"},model:{value:e.cruiseTime,callback:function(t){e.cruiseTime=t},expression:\"cruiseTime\"}})],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setCruiseTime}},[e._v(\"保存\")]),i(\"el-button\",{on:{click:e.cancelSetCruiseTime}},[e._v(\"取消\")])],1)],1):i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setTimeVisible=!0}}},[e._v(\"设置巡航时间\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.startCruise}},[e._v(\"开始巡航\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.stopCruise}},[e._v(\"停止巡航\")]),i(\"el-button\",{attrs:{size:\"mini\",type:\"danger\"},on:{click:e.deleteCruise}},[e._v(\"删除巡航\")])],1)},v=[],y=(i(\"a434\"),{name:\"PtzCruising\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{cruiseId:1,presetList:[],allPresetList:[],selectPreset:\"\",inputVisible:!1,selectPresetVisible:!1,setSpeedVisible:!1,setTimeVisible:!1,cruiseSpeed:\"\",cruiseTime:\"\"}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch(\"frontEnd/queryPreset\",[this.deviceId,this.channelDeviceId]).then((function(t){e.allPresetList=t}))},addCruisePoint:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/addPointForCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.selectPreset.presetId]).then((function(t){e.presetList.push(e.selectPreset)})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.selectPreset=\"\",e.selectPresetVisible=!1,t.close()}))},cancelAddCruisePoint:function(){this.selectPreset=\"\",this.selectPresetVisible=!1},delPreset:function(e,t){var i=this,s=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/deletePointForCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId,e.presetId]).then((function(e){i.presetList.splice(t,1)})).catch((function(e){i.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){s.close()}))},deleteCruise:function(e,t){var i=this;this.$confirm(\"确定删除此巡航组\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){var e=i.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});i.$store.dispatch(\"frontEnd/deletePointForCruise\",[i.deviceId,i.channelDeviceId,i.cruiseId,0]).then((function(e){i.presetList=[]})).catch((function(e){i.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){e.close()}))}))},setCruiseSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setCruiseSpeed\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseSpeed]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.cruiseSpeed=\"\",e.setSpeedVisible=!1,t.close()}))},cancelSetCruiseSpeed:function(){this.cruiseSpeed=\"\",this.setSpeedVisible=!1},setCruiseTime:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setCruiseTime\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseTime]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))},cancelSetCruiseTime:function(){this.setTimeVisible=!1,this.cruiseTime=\"\"},startCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/startCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))},stopCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/stopCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))}}}),_=y,w=(i(\"e8b4\"),Object(m[\"a\"])(_,g,v,!1,null,null,null)),I=w.exports,S=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"ptzScan\"}},[i(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"80px auto\",\"line-height\":\"28px\"}},[i(\"span\",[e._v(\"扫描组号: \")]),i(\"el-input\",{attrs:{min:\"1\",max:\"255\",placeholder:\"扫描组号\",\"addon-before\":\"扫描组号\",\"addon-after\":\"(1-255)\",size:\"mini\"},model:{value:e.scanId,callback:function(t){e.scanId=t},expression:\"scanId\"}})],1),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.setScanLeft}},[e._v(\"设置左边界\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.setScanRight}},[e._v(\"设置右边界\")]),e.setSpeedVisible?i(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[i(\"el-form-item\",[e.setSpeedVisible?i(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航速度\",\"addon-before\":\"巡航速度\",\"addon-after\":\"(1-4095)\",size:\"mini\"},model:{value:e.speed,callback:function(t){e.speed=t},expression:\"speed\"}}):e._e()],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setSpeed}},[e._v(\"保存\")]),i(\"el-button\",{on:{click:e.cancelSetSpeed}},[e._v(\"取消\")])],1)],1):i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v(\"设置扫描速度\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.startScan}},[e._v(\"开始自动扫描\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.stopScan}},[e._v(\"停止自动扫描\")])],1)},C=[],k={name:\"PtzScan\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{scanId:1,setSpeedVisible:!1,speed:\"\"}},created:function(){},methods:{setSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setSpeedForScan\",[this.deviceId,this.channelDeviceId,this.scanId,this.speed]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},cancelSetSpeed:function(){this.speed=\"\",this.setSpeedVisible=!1},setScanLeft:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setLeftForScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},setScanRight:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setRightForScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},startScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/startScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){t.close()}))},stopScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/stopScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){t.close()}))}}},x=k,$=(i(\"fdc8\"),Object(m[\"a\"])(x,S,C,!1,null,null,null)),P=$.exports,T=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"ptzWiper\"}},[i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"on\")}}},[e._v(\"开启\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"off\")}}},[e._v(\"关闭\")])],1)},D=[],E={name:\"PtzWiper\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{}},created:function(){},methods:{open:function(e){var t=this,i=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/wiper\",[this.deviceId,this.channelDeviceId,e]).then((function(e){t.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){i.close()}))}}},z=E,R=(i(\"a135\"),Object(m[\"a\"])(z,T,D,!1,null,null,null)),M=R.exports,U=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"ptzScan\"}},[i(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[i(\"el-form-item\",[i(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"开关编号\",\"addon-before\":\"开关编号\",\"addon-after\":\"(2-255)\",size:\"mini\"},model:{value:e.switchId,callback:function(t){e.switchId=t},expression:\"switchId\"}})],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"on\")}}},[e._v(\"开启\")]),i(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"off\")}}},[e._v(\"关闭\")])],1)],1)],1)},L=[],N={name:\"PtzScan\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{switchId:1}},created:function(){},methods:{open:function(e){var t=this,i=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/auxiliary\",[this.deviceId,this.channelDeviceId,e,this.switchId]).then((function(e){t.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){i.close()}))}}},B=N,F=(i(\"34c9\"),Object(m[\"a\"])(B,U,L,!1,null,null,null)),O=F.exports,A=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"mediaInfo\"}},[i(\"el-button\",{staticStyle:{position:\"absolute\",right:\"1rem\"},attrs:{icon:\"el-icon-refresh-right\",circle:\"\",size:\"mini\"},on:{click:e.getMediaInfo}}),i(\"el-descriptions\",{attrs:{size:\"mini\",column:3,title:\"概况\"}},[i(\"el-descriptions-item\",{attrs:{label:\"观看人数\"}},[e._v(e._s(e.info.readerCount))]),i(\"el-descriptions-item\",{attrs:{label:\"网络\"}},[e._v(e._s(e.formatByteSpeed()))]),i(\"el-descriptions-item\",{attrs:{label:\"持续时间\"}},[e._v(e._s(e.info.aliveSecond)+\"秒\")])],1),i(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"1fr 1fr\"}},[e.info.videoCodec?i(\"el-descriptions\",{attrs:{size:\"mini\",column:2,title:\"视频信息\"}},[i(\"el-descriptions-item\",{attrs:{label:\"编码\"}},[e._v(e._s(e.info.videoCodec))]),i(\"el-descriptions-item\",{attrs:{label:\"分辨率\"}},[e._v(e._s(e.info.width)+\"x\"+e._s(e.info.height)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"FPS\"}},[e._v(e._s(e.info.fps))]),i(\"el-descriptions-item\",{attrs:{label:\"丢包率\"}},[e._v(e._s(e.info.loss))])],1):e._e(),e.info.audioCodec?i(\"el-descriptions\",{attrs:{size:\"mini\",column:2,title:\"音频信息\"}},[i(\"el-descriptions-item\",{attrs:{label:\"编码\"}},[e._v(\" \"+e._s(e.info.audioCodec)+\" \")]),i(\"el-descriptions-item\",{attrs:{label:\"采样率\"}},[e._v(e._s(e.info.audioSampleRate))])],1):e._e()],1)],1)},V=[],j=(i(\"99af\"),i(\"b680\"),{name:\"MediaInfo\",components:{},props:[\"app\",\"stream\",\"mediaServerId\"],data:function(){return{info:{},task:null}},created:function(){this.getMediaInfo()},methods:{getMediaInfo:function(){var e=this;this.$store.dispatch(\"server/getMediaInfo\",{app:this.app,stream:this.stream,mediaServerId:this.mediaServerId}).then((function(t){e.info=t}))},startTask:function(){this.task=setInterval(this.getMediaInfo,1e3)},stopTask:function(){this.task&&(window.clearInterval(this.task),this.task=null)},formatByteSpeed:function(){var e=this.info.bytesSpeed,t=1024;return e<t?e+\" B/S\":e<Math.pow(t,2)?(e/t).toFixed(2)+\" KB/S\":e<Math.pow(t,3)?(e/Math.pow(t,2)).toFixed(2)+\" MB/S\":e<Math.pow(t,4)?(e/Math.pow(t,3)).toFixed(2)+\" G/S\":(e/Math.pow(t,4)).toFixed(2)+\" T/S\"},formatAliveSecond:function(){var e=this.info.aliveSecond,t=parseInt(e.value/3600),i=parseInt(e.value/60%60),s=Math.ceil(e.value%60),o=t<10?\"0\"+t:t,n=s>59?59:s;return\"\".concat(o>0?\"\".concat(o,\"小时\"):\"\").concat(i<10?\"0\"+i:i,\"分\").concat(n<10?\"0\"+n:n,\"秒\")}}}),W=j,H=(i(\"5869\"),Object(m[\"a\"])(W,A,V,!1,null,null,null)),G=H.exports,J=i(\"4f91\"),Z={name:\"DevicePlayer\",directives:{elDragDialog:n[\"a\"]},components:{H265web:J[\"a\"],PtzPreset:b,PtzCruising:I,ptzScan:P,ptzWiper:M,ptzSwitch:O,mediaInfo:G,jessibucaPlayer:c[\"default\"],rtcPlayer:r[\"default\"]},props:{},data:function(){return{video:\"http://lndxyj.iqilu.com/public/upload/2019/10/14/8c001ea0c09cdc59a57829dabc8010fa.mp4\",videoUrl:\"\",activePlayer:\"jessibuca\",player:{jessibuca:[\"ws_flv\",\"wss_flv\"],webRTC:[\"rtc\",\"rtcs\"],h265web:[\"ws_flv\",\"wss_flv\"]},showVideoDialog:!1,streamId:\"\",ptzMethod:\"preset\",ptzPresetId:\"\",app:\"\",mediaServerId:\"\",deviceId:\"\",channelId:\"\",tabActiveName:\"media\",hasAudio:!1,loadingRecords:!1,recordsLoading:!1,isLoging:!1,controSpeed:30,timeVal:0,timeMin:0,timeMax:1440,presetPos:1,cruisingSpeed:100,cruisingTime:5,cruisingGroup:0,scanSpeed:100,scanGroup:0,tracks:[],showPtz:!0,showBroadcast:!0,showRrecord:!0,sliderTime:0,seekTime:0,recordStartTime:0,showTimeText:\"00:00:00\",streamInfo:null,broadcastMode:!0,broadcastRtc:null,broadcastStatus:-1}},computed:{getPlayerShared:function(){return{sharedUrl:window.location.origin+\"/#/play/wasm/\"+encodeURIComponent(this.videoUrl),sharedIframe:'<iframe src=\"'+window.location.origin+\"/#/play/wasm/\"+encodeURIComponent(this.videoUrl)+'\"></iframe>',sharedRtmp:this.videoUrl}}},created:function(){console.log(\"created\"),console.log(this.player),this.broadcastStatus=-1,1===Object.keys(this.player).length&&(this.activePlayer=Object.keys(this.player)[0])},methods:{tabHandleClick:function(e,t){console.log(e),this.tracks=[],\"codec\"===e.name?this.$refs.mediaInfo.startTask():this.$refs.mediaInfo.stopTask()},changePlayer:function(e){console.log(this.player[e.name][0]),this.activePlayer=e.name,this.videoUrl=this.getUrlByStreamInfo(),console.log(this.videoUrl)},openDialog:function(e,t,i,s){if(!this.showVideoDialog)switch(this.tabActiveName=e,this.channelId=i,this.deviceId=t,this.streamId=\"\",this.mediaServerId=\"\",this.app=\"\",this.videoUrl=\"\",this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),e){case\"media\":this.play(s.streamInfo,s.hasAudio);break;case\"streamPlay\":this.tabActiveName=\"media\",this.showRrecord=!1,this.showPtz=!1,this.showBroadcast=!1,this.play(s.streamInfo,s.hasAudio);break;case\"control\":break}},play:function(e,t){this.streamInfo=e,this.hasAudio=t,this.isLoging=!1,this.videoUrl=this.getUrlByStreamInfo(),this.streamId=e.stream,this.app=e.app,this.mediaServerId=e.mediaServerId,this.playFromStreamInfo(!1,e)},getUrlByStreamInfo:function(){console.log(this.streamInfo);var e=this.streamInfo;return this.streamInfo.transcodeStream&&(e=this.streamInfo.transcodeStream),\"https:\"===location.protocol?this.videoUrl=e[this.player[this.activePlayer][1]]:this.videoUrl=e[this.player[this.activePlayer][0]],this.videoUrl},playFromStreamInfo:function(e,t){var i=this;this.showVideoDialog=!0,this.hasaudio=e&&this.hasaudio,this.$refs[this.activePlayer]?this.$refs[this.activePlayer].play(this.getUrlByStreamInfo(t)):this.$nextTick((function(){i.$refs[i.activePlayer].play(i.getUrlByStreamInfo(t))}))},close:function(){console.log(\"关闭视频\"),this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),this.videoUrl=\"\",this.showVideoDialog=!1,this.stopBroadcast()},ptzCamera:function(e){console.log(\"云台控制：\"+e),this.$store.dispatch(\"frontEnd/ptz\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100),parseInt(255*this.controSpeed/100),parseInt(16*this.controSpeed/100)])},irisCamera:function(e){this.$store.dispatch(\"frontEnd/iris\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},focusCamera:function(e){this.$store.dispatch(\"frontEnd/focus\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},videoError:function(e){console.log(\"播放器错误：\"+JSON.stringify(e))},copyUrl:function(e){var t=this;console.log(e),this.$copyText(e).then((function(e){t.$message.success({showClose:!0,message:\"成功拷贝到粘贴板\"})}),(function(e){}))},getBroadcastStatus:function(){return-2==this.broadcastStatus||-1==this.broadcastStatus?\"primary\":0==this.broadcastStatus?\"warning\":1===this.broadcastStatus?\"danger\":void 0},broadcastStatusClick:function(){var e=this;-1===this.broadcastStatus?(this.broadcastStatus=0,this.$store.dispatch(\"play/broadcastStart\",[this.deviceId,this.channelId,this.broadcastMode]).then((function(t){var i=t.streamInfo;document.location.protocol.includes(\"https\")?e.startBroadcast(i.rtcs):e.startBroadcast(i.rtc)}))):1===this.broadcastStatus&&(this.broadcastStatus=-1,this.broadcastRtc.close())},startBroadcast:function(e){var t=this;this.$store.dispatch(\"user/getUserInfo\").then((function(i){if(null!=i){var s=i.pushKey;e+=\"&sign=\"+l.a.createHash(\"md5\").update(s,\"utf8\").digest(\"hex\"),console.log(\"开始语音喊话： \"+e),t.broadcastRtc=new ZLMRTCClient.Endpoint({debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!1,recvOnly:!1}),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT,(function(e){console.error(\"不支持webrtc\",e),t.$message({showClose:!0,message:\"不支持webrtc, 无法进行语音喊话\",type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error(\"ICE 协商出错\"),t.$message({showClose:!0,message:\"ICE 协商出错\",type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(e){console.error(\"offer anwser 交换失败\",e),t.$message({showClose:!0,message:\"offer anwser 交换失败\"+e,type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,(function(e){console.log(\"状态改变\",e),\"connecting\"===e?t.broadcastStatus=0:\"connected\"===e?t.broadcastStatus=1:\"disconnected\"===e&&(t.broadcastStatus=-1)})),t.broadcastRtc.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED,(function(e){console.log(\"捕获流失败\",e),t.$message({showClose:!0,message:\"捕获流失败\"+e,type:\"error\"}),t.broadcastStatus=-1}))}else t.broadcastStatus=-1})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"}),t.broadcastStatus=-1}))},stopBroadcast:function(){this.broadcastRtc.close(),this.broadcastStatus=-1,this.$store.dispatch(\"play/broadcastStop\",[this.deviceId,this.channelId])}}},Y=Z,q=(i(\"c5f0\"),Object(m[\"a\"])(Y,s,o,!1,null,null,null));t[\"a\"]=q.exports},\"0868\":function(e,t,i){},1148:function(e,t,i){\"use strict\";var s=i(\"a691\"),o=i(\"1d80\");e.exports=\"\".repeat||function(e){var t=String(o(this)),i=\"\",n=s(e);if(n<0||n==1/0)throw RangeError(\"Wrong number of repetitions\");for(;n>0;(n>>>=1)&&(t+=t))1&n&&(i+=t);return i}},\"257e\":function(e,t,i){\"use strict\";i(\"d017\")},2655:function(e,t,i){\"use strict\";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{ref:\"container\",staticStyle:{width:\"100%\",height:\"100%\",\"background-color\":\"#000000\",margin:\"0 auto\",position:\"relative\"},on:{dblclick:e.fullscreenSwich}},[i(\"div\",{staticStyle:{width:\"100%\",\"padding-top\":\"56.25%\",position:\"relative\"}}),i(\"div\",{staticClass:\"buttons-box\",attrs:{id:\"buttonsBox\"}},[i(\"div\",{staticClass:\"buttons-box-left\"},[e.playing?e._e():i(\"i\",{staticClass:\"iconfont icon-play jessibuca-btn\",on:{click:e.playBtnClick}}),e.playing?i(\"i\",{staticClass:\"iconfont icon-pause jessibuca-btn\",on:{click:e.pause}}):e._e(),i(\"i\",{staticClass:\"iconfont icon-stop jessibuca-btn\",on:{click:e.destroy}}),e.isNotMute?i(\"i\",{staticClass:\"iconfont icon-audio-high jessibuca-btn\",on:{click:function(t){return e.mute()}}}):e._e(),e.isNotMute?e._e():i(\"i\",{staticClass:\"iconfont icon-audio-mute jessibuca-btn\",on:{click:function(t){return e.cancelMute()}}})]),i(\"div\",{staticClass:\"buttons-box-right\"},[i(\"span\",{staticClass:\"jessibuca-btn\"},[e._v(e._s(e.kBps)+\" kb/s\")]),i(\"i\",{staticClass:\"iconfont icon-camera1196054easyiconnet jessibuca-btn\",staticStyle:{\"font-size\":\"1rem !important\"},on:{click:e.screenshot}}),i(\"i\",{staticClass:\"iconfont icon-shuaxin11 jessibuca-btn\",on:{click:e.playBtnClick}}),e.fullscreen?e._e():i(\"i\",{staticClass:\"iconfont icon-weibiaoti10 jessibuca-btn\",on:{click:e.fullscreenSwich}}),e.fullscreen?i(\"i\",{staticClass:\"iconfont icon-weibiaoti11 jessibuca-btn\",on:{click:e.fullscreenSwich}}):e._e()])])])},o=[],n=i(\"5530\"),a={},l={name:\"Jessibuca\",props:[\"videoUrl\",\"error\",\"hasAudio\",\"height\"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:\"\",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1}},watch:{videoUrl:{handler:function(e,t){var i=this;this.$nextTick((function(){i.play(e)}))},immediate:!0}},created:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){e.updatePlayerDomSize(),window.onresize=e.updatePlayerDomSize,\"undefined\"===typeof e.videoUrl&&(e.videoUrl=t),e.btnDom=document.getElementById(\"buttonsBox\")}))},mounted:function(){this.updatePlayerDomSize()},destroyed:function(){a[this._uid]&&a[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.performance=\"\"},methods:{updatePlayerDomSize:function(){var e=this,t=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(t){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(t.parentNode));var i=t.parentNode.clientWidth,s=t.parentNode.clientHeight,o=i,n=9/16*o;s>0&&i>s/9*16&&(n=s,o=s/9*16);var l=Math.min(document.body.clientHeight,document.documentElement.clientHeight);n>l&&(n=l,o=16/9*n),this.playerWidth=o,this.playerHeight=n,this.playing&&a[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(){var e={container:this.$refs.container,autoWasm:!0,background:\"\",controlAutoHide:!1,debug:!1,decoder:\"static/js/jessibuca/decoder.js\",forceNoOffscreen:!1,hasAudio:\"undefined\"===typeof this.hasAudio||this.hasAudio,heartTimeout:5,heartTimeoutReplay:!0,heartTimeoutReplayTimes:3,hiddenAutoPause:!1,hotKey:!0,isFlv:!1,isFullResize:!1,isNotMute:this.isNotMute,isResize:!0,keepScreenOn:!0,loadingText:\"请稍等, 视频加载中......\",loadingTimeout:10,loadingTimeoutReplay:!0,loadingTimeoutReplayTimes:3,openWebglAlignment:!1,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},recordType:\"mp4\",rotate:0,showBandwidth:!1,supportDblclickFullscreen:!1,timeout:10,useMSE:!0,useWCS:!1,useWebFullScreen:!0,videoBuffer:.1,wasmDecodeErrorReplay:!0,wcsUseVideoRender:!0};console.log(\"Jessibuca -> options: \",e),a[this._uid]=new window.Jessibuca(Object(n[\"a\"])({},e));var t=a[this._uid],i=this;t.on(\"pause\",(function(){i.playing=!1})),t.on(\"play\",(function(){i.playing=!0})),t.on(\"fullscreen\",(function(e){i.fullscreen=e})),t.on(\"mute\",(function(e){i.isNotMute=!e})),t.on(\"performance\",(function(e){var t=\"卡顿\";2===e?t=\"非常流畅\":1===e&&(t=\"流畅\"),i.performance=t})),t.on(\"kBps\",(function(e){i.kBps=Math.round(e)})),t.on(\"videoInfo\",(function(e){console.log(\"Jessibuca -> videoInfo: \",e)})),t.on(\"audioInfo\",(function(e){console.log(\"Jessibuca -> audioInfo: \",e)})),t.on(\"error\",(function(e){console.log(\"Jessibuca -> error: \",e)})),t.on(\"timeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"loadingTimeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"delayTimeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"playToRenderTimes\",(function(e){console.log(\"Jessibuca -> playToRenderTimes: \",e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(\"Jessibuca -> url: \",e),a[this._uid]&&this.destroy(),this.create(),a[this._uid].on(\"play\",(function(){t.playing=!0,t.loaded=!0,t.quieting=jessibuca.quieting})),a[this._uid].hasLoaded()?a[this._uid].play(e):a[this._uid].on(\"load\",(function(){a[t._uid].play(e)}))},pause:function(){a[this._uid]&&a[this._uid].pause(),this.playing=!1,this.err=\"\",this.performance=\"\"},screenshot:function(){a[this._uid]&&a[this._uid].screenshot()},mute:function(){a[this._uid]&&a[this._uid].mute()},cancelMute:function(){a[this._uid]&&a[this._uid].cancelMute()},destroy:function(){a[this._uid]&&a[this._uid].destroy(),null==document.getElementById(\"buttonsBox\")&&this.$refs.container.appendChild(this.btnDom),a[this._uid]=null,this.playing=!1,this.err=\"\",this.performance=\"\"},fullscreenSwich:function(){var e=this.isFullscreen();a[this._uid].setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}}},r=l,c=(i(\"989a\"),i(\"2877\")),d=Object(c[\"a\"])(r,s,o,!1,null,null,null);t[\"default\"]=d.exports},\"28b5\":function(e,t,i){},\"2b7e\":function(e,t,i){},\"34c9\":function(e,t,i){\"use strict\";i(\"2b7e\")},\"35d9\":function(e,t,i){},\"3b05\":function(e,t,i){\"use strict\";i(\"35d9\")},\"408a\":function(e,t,i){var s=i(\"c6b6\");e.exports=function(e){if(\"number\"!=typeof e&&\"Number\"!=s(e))throw TypeError(\"Incorrect invocation\");return+e}},\"4f91\":function(e,t,i){\"use strict\";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{ref:\"container\",staticStyle:{\"background-color\":\"#000000\"},attrs:{id:\"h265Player\"},on:{dblclick:e.fullscreenSwich}},[i(\"div\",{ref:\"playerBox\",staticStyle:{width:\"100%\",height:\"100%\",margin:\"0 auto\"},attrs:{id:\"glplayer\"}}),e.playerLoading?i(\"div\",{staticClass:\"player-loading\"},[i(\"i\",{staticClass:\"el-icon-loading\"}),i(\"span\",[e._v(\"视频加载中\")])]):e._e(),e.showButton?i(\"div\",{staticClass:\"buttons-box\",attrs:{id:\"buttonsBox\"}},[i(\"div\",{staticClass:\"buttons-box-left\"},[e.playing?e._e():i(\"i\",{staticClass:\"iconfont icon-play h265web-btn\",on:{click:e.unPause}}),e.playing?i(\"i\",{staticClass:\"iconfont icon-pause h265web-btn\",on:{click:e.pause}}):e._e(),i(\"i\",{staticClass:\"iconfont icon-stop h265web-btn\",on:{click:e.destroy}}),e.isNotMute?i(\"i\",{staticClass:\"iconfont icon-audio-high h265web-btn\",on:{click:function(t){return e.mute()}}}):e._e(),e.isNotMute?e._e():i(\"i\",{staticClass:\"iconfont icon-audio-mute h265web-btn\",on:{click:function(t){return e.cancelMute()}}})]),i(\"div\",{staticClass:\"buttons-box-right\"},[i(\"i\",{staticClass:\"iconfont icon-camera1196054easyiconnet h265web-btn\",staticStyle:{\"font-size\":\"1rem !important\"},on:{click:e.screenshot}}),i(\"i\",{staticClass:\"iconfont icon-shuaxin11 h265web-btn\",on:{click:e.playBtnClick}}),e.fullscreen?e._e():i(\"i\",{staticClass:\"iconfont icon-weibiaoti10 h265web-btn\",on:{click:e.fullscreenSwich}}),e.fullscreen?i(\"i\",{staticClass:\"iconfont icon-weibiaoti11 h265web-btn\",on:{click:e.fullscreenSwich}}):e._e()])]):e._e()])},o=[],n=(i(\"b0c0\"),i(\"ac1f\"),i(\"5319\"),{}),a=\"base64:********************************************************************************************************************************************************************************************************************************************************************************\",l={name:\"H265web\",props:[\"videoUrl\",\"error\",\"hasAudio\",\"height\",\"showButton\"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1,playerWidth:0,playerHeight:0,inited:!1,playerLoading:!1,mediaInfo:null}},watch:{videoUrl:function(e,t){this.play(e)},playing:function(e,t){this.$emit(\"playStatusChange\",e)},immediate:!0},mounted:function(){var e=this,t=decodeURIComponent(this.$route.params.url);window.onresize=function(){e.updatePlayerDomSize()},this.btnDom=document.getElementById(\"buttonsBox\"),this.ensureGlobalInterceptorActive(),console.log(\"初始化时的地址为: \"+t),t&&this.play(this.videoUrl)},destroyed:function(){n[this._uid]&&n[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.playerLoading=!1},methods:{updatePlayerDomSize:function(){var e=this,t=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(t){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(t.parentNode));var i=t.parentNode.clientWidth,s=t.parentNode.clientHeight,o=i,a=9/16*o;s>0&&i>s/9*16&&(a=s,o=s/9*16);var l=Math.min(document.body.clientHeight,document.documentElement.clientHeight);a>l&&(a=l,o=16/9*a),this.$refs.playerBox.style.width=o+\"px\",this.$refs.playerBox.style.height=a+\"px\",this.playerWidth=o,this.playerHeight=a,this.playing&&n[this._uid].resize(this.playerWidth,this.playerHeight)},resize:function(e,t){this.playerWidth=e,this.playerHeight=t,this.$refs.playerBox.style.width=e+\"px\",this.$refs.playerBox.style.height=t+\"px\",this.playing&&n[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(e){var t=this;if(console.log(\"开始创建播放器实例，UID:\",this._uid,\"URL:\",e),n[this._uid]){console.warn(\"创建前发现残留的播放器实例，强制清理，UID:\",this._uid);try{n[this._uid].release&&n[this._uid].release()}catch(o){console.warn(\"清理残留播放器时出现错误:\",o)}n[this._uid]=null,this.clearPlayerDOM()}this.playerLoading=!0;var i={};console.log(\"正在创建新的h265web播放器实例，UID:\",this._uid),n[this._uid]=new window.new265webjs(e,Object.assign({player:\"glplayer\",width:this.playerWidth,height:this.playerHeight,token:a,extInfo:{coreProbePart:.4,probeSize:8192,ignoreAudio:null==this.hasAudio||this.hasAudio?0:1},debug:!1,debugLevel:0,logLevel:0,disableStats:!0,disableAnalytics:!0,noStats:!0,noLog:!0,silent:!0},i)),console.log(\"h265web播放器实例创建成功，UID:\",this._uid);var s=n[this._uid];s.onOpenFullScreen=function(){t.fullscreen=!0},s.onCloseFullScreen=function(){t.fullscreen=!1},s.onReadyShowDone=function(){console.log(\"播放器准备完成，开始自动播放，UID:\",t._uid);try{var e=s.play();console.log(\"自动播放结果:\",e,\"UID:\",t._uid),setTimeout((function(){t.playing=s.isPlaying(),console.log(\"播放器初始化完成，playing状态:\",t.playing,\"UID:\",t._uid),t.playerLoading=!1}),200)}catch(o){console.warn(\"播放器自动播放失败:\",o),t.playing=!1,t.playerLoading=!1}},s.onLoadFinish=function(){try{t.loaded=!0,s.mediaInfo&&\"function\"===typeof s.mediaInfo?t.mediaInfo=s.mediaInfo():console.warn(\"播放器不支持mediaInfo方法\")}catch(o){console.warn(\"获取媒体信息时出现错误:\",o),t.loaded=!0}},s.onPlayTime=function(e){try{null===e||void 0===e||isNaN(e)?console.warn(\"播放器返回无效的时间值:\",e):t.$emit(\"playTimeChange\",e)}catch(o){console.warn(\"播放器时间回调出现错误:\",o)}},s.onSeekFinish=function(){try{console.log(\"播放器seek完成\"),t.$emit(\"seekFinish\")}catch(o){console.warn(\"播放器seek完成回调出现错误:\",o)}},s.do()},screenshot:function(){if(n[this._uid]){var e=document.createElement(\"canvas\");console.log(this.mediaInfo),e.width=this.mediaInfo.meta.size.width,e.height=this.mediaInfo.meta.size.height,n[this._uid].snapshot(e);var t=document.createElement(\"a\");t.download=\"screenshot.png\",t.href=e.toDataURL(\"image/png\").replace(\"image/png\",\"image/octet-stream\"),t.click()}},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;if(console.log(\"开始播放视频，URL:\",e,\"UID:\",this._uid),n[this._uid])return console.log(\"检测到已存在的播放器实例，先销毁，UID:\",this._uid),this.destroy(),void setTimeout((function(){console.log(\"销毁完成，重新创建播放器，UID:\",t._uid),t.play(e)}),300);if(e){if(0===this.playerWidth||0===this.playerHeight)return console.log(\"播放器尺寸未初始化，等待DOM更新\"),this.updatePlayerDomSize(),void setTimeout((function(){t.play(e)}),300);console.log(\"创建新的播放器实例，UID:\",this._uid),this.create(e)}else console.warn(\"播放URL为空，无法播放\")},unPause:function(){var e=this;try{if(n[this._uid]&&n[this._uid].play){console.log(\"开始调用播放器play方法，UID:\",this._uid);var t=n[this._uid].play();t&&\"function\"===typeof t.catch?t.then((function(){console.log(\"播放器play Promise resolved，UID:\",e._uid),setTimeout((function(){e.playing=n[e._uid].isPlaying(),console.log(\"播放器状态更新，playing:\",e.playing,\"UID:\",e._uid)}),100)})).catch((function(t){\"AbortError\"!==t.name?(console.warn(\"恢复播放时出现错误:\",t),e.playing=!1):console.log(\"播放被中断（AbortError），这是正常的\")})):setTimeout((function(){e.playing=n[e._uid].isPlaying(),console.log(\"播放器状态更新（非Promise），playing:\",e.playing,\"UID:\",e._uid)}),100),console.log(\"播放器play方法调用完成，UID:\",this._uid)}else console.warn(\"播放器实例不存在或不支持play方法，UID:\",this._uid)}catch(i){console.warn(\"恢复播放时出现错误:\",i),this.playing=!1}this.err=\"\"},pause:function(){try{if(n[this._uid]&&n[this._uid].pause){var e=n[this._uid].pause();e&&\"function\"===typeof e.catch&&e.catch((function(e){\"AbortError\"!==e.name&&console.warn(\"暂停播放时出现错误:\",e)})),this.playing=n[this._uid].isPlaying()}}catch(t){console.warn(\"暂停播放时出现错误:\",t),this.playing=!1}this.err=\"\"},forcePlay:function(){var e=this;console.log(\"强制播放方法被调用，UID:\",this._uid);try{if(n[this._uid]){var t=n[this._uid].isPlaying();if(console.log(\"当前播放状态:\",t,\"UID:\",this._uid),t)console.log(\"播放器已在播放状态，无需强制播放，UID:\",this._uid),this.playing=!0;else if(console.log(\"播放器未播放，尝试启动播放，UID:\",this._uid),n[this._uid].play){var i=n[this._uid].play();console.log(\"调用play()方法结果:\",i,\"UID:\",this._uid),setTimeout((function(){var t=n[e._uid].isPlaying();e.playing=t,console.log(\"强制播放后状态:\",t,\"UID:\",e._uid),t||(console.warn(\"强制播放失败，播放器仍未播放，UID:\",e._uid),setTimeout((function(){n[e._uid]&&n[e._uid].play&&(console.log(\"再次尝试播放，UID:\",e._uid),n[e._uid].play(),setTimeout((function(){e.playing=n[e._uid].isPlaying(),console.log(\"二次尝试后状态:\",e.playing,\"UID:\",e._uid)}),200))}),300))}),200)}}else console.warn(\"播放器实例不存在，无法强制播放，UID:\",this._uid)}catch(s){console.warn(\"强制播放时出现错误:\",s),this.playing=!1}},mute:function(){n[this._uid]&&(n[this._uid].setVoice(0),this.isNotMute=!1)},cancelMute:function(){n[this._uid]&&(n[this._uid].setVoice(1),this.isNotMute=!0)},destroy:function(){if(console.log(\"开始销毁h265web播放器，UID:\",this._uid),this.playing=!1,this.loaded=!1,this.playerLoading=!1,this.err=\"\",n[this._uid])try{console.log(\"正在销毁播放器实例，UID:\",this._uid),n[this._uid].pause&&n[this._uid].pause();try{n[this._uid].release&&(n[this._uid].release(),console.log(\"播放器资源已释放，UID:\",this._uid))}catch(e){console.warn(\"释放播放器资源时出现错误:\",e)}n[this._uid]=null,console.log(\"播放器引用已清空，UID:\",this._uid),this.clearPlayerDOM()}catch(e){console.warn(\"销毁播放器时出现错误:\",e),n[this._uid]=null,this.clearPlayerDOM()}else console.log(\"播放器实例不存在，直接清理DOM，UID:\",this._uid),this.clearPlayerDOM();console.log(\"h265web播放器销毁完成，UID:\",this._uid)},clearPlayerDOM:function(){try{if(this.$refs.playerBox){console.log(\"清理播放器DOM容器，UID:\",this._uid);while(this.$refs.playerBox.firstChild)this.$refs.playerBox.removeChild(this.$refs.playerBox.firstChild);this.$refs.playerBox.style.cssText=\"\",this.$refs.playerBox.innerHTML=\"\",console.log(\"播放器DOM容器已清理，UID:\",this._uid)}}catch(e){console.warn(\"清理DOM容器时出现错误:\",e)}},fullscreenSwich:function(){var e=this.isFullscreen();e?n[this._uid].closeFullScreen():n[this._uid].fullScreen(),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1},setPlaybackRate:function(e){try{n[this._uid]&&n[this._uid].setPlaybackRate&&n[this._uid].setPlaybackRate(e)}catch(t){console.warn(\"设置播放倍速时出现错误:\",t)}},seek:function(e){var t=this;try{if(console.log(\"h265web播放器seek方法被调用，目标时间:\",e,\"秒\"),console.log(\"播放器状态:\",{playerExists:!!n[this._uid],seekMethodExists:!(!n[this._uid]||!n[this._uid].seek),playerUid:this._uid,loaded:this.loaded,playing:this.playing}),n[this._uid]&&n[this._uid].seek){console.log(\"执行播放器seek操作到:\",e,\"秒\"),e<0&&(console.warn(\"seek时间小于0，调整为0\"),e=0);var i=this.playing;return n[this._uid].seek(e),console.log(\"播放器seek操作已执行，之前播放状态:\",i),setTimeout((function(){try{t.$emit(\"seekFinish\"),console.log(\"h265web播放器seek操作完成\"),console.log(\"h265web播放器seek完成，播放状态由父组件控制\")}catch(e){console.warn(\"触发seek完成事件时出现错误:\",e)}}),200),!0}return console.warn(\"播放器未准备好或不支持seek操作\",{playerExists:!!n[this._uid],seekMethodExists:!(!n[this._uid]||!n[this._uid].seek)}),!1}catch(s){return console.error(\"播放器seek时出现错误:\",s),!1}},getCurrentTime:function(){try{if(n[this._uid]){if(n[this._uid].getCurrentTime)return n[this._uid].getCurrentTime();if(n[this._uid].getTime)return n[this._uid].getTime();if(void 0!==n[this._uid].currentTime)return n[this._uid].currentTime}return null}catch(e){return console.warn(\"获取播放器当前时间时出现错误:\",e),null}},getPlayerStatus:function(){try{return n[this._uid]?{playing:this.playing,loaded:this.loaded,playerExists:!0,hasSeekMethod:!!n[this._uid].seek,hasTimeMethod:!(!n[this._uid].getCurrentTime&&!n[this._uid].getTime)}:{playing:!1,loaded:!1,playerExists:!1,hasSeekMethod:!1,hasTimeMethod:!1}}catch(e){return console.warn(\"获取播放器状态时出现错误:\",e),null}},ensureGlobalInterceptorActive:function(){try{if(window.h265webInterceptor){var e=window.h265webInterceptor.status();e.active||window.h265webInterceptor.start()}}catch(t){}}}},r=l,c=(i(\"3b05\"),i(\"2877\")),d=Object(c[\"a\"])(r,s,o,!1,null,null,null);t[\"a\"]=d.exports},5869:function(e,t,i){\"use strict\";i(\"e0ad\")},\"6ebe\":function(e,t,i){\"use strict\";i(\"28b5\")},7295:function(e,t,i){},7317:function(e,t,i){\"use strict\";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{id:\"CommonChannelEdit\"}},[i(\"el-form\",{ref:\"passwordForm\",staticClass:\"channel-form\",attrs:{\"status-icon\":\"\",\"label-width\":\"160px\"}},[i(\"div\",{staticClass:\"form-box\"},[i(\"el-form-item\",{attrs:{label:\"名称\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入通道名称\"},model:{value:e.form.gbName,callback:function(t){e.$set(e.form,\"gbName\",t)},expression:\"form.gbName\"}})],1),i(\"el-form-item\",{attrs:{label:\"编码\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入通道编码\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[i(\"el-button\",{on:{click:function(t){return e.buildDeviceIdCode(e.form.gbDeviceId)}}},[e._v(\"生成\")])]},proxy:!0}]),model:{value:e.form.gbDeviceId,callback:function(t){e.$set(e.form,\"gbDeviceId\",t)},expression:\"form.gbDeviceId\"}})],1),i(\"el-form-item\",{attrs:{label:\"设备厂商\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入设备厂商\"},model:{value:e.form.gbManufacturer,callback:function(t){e.$set(e.form,\"gbManufacturer\",t)},expression:\"form.gbManufacturer\"}})],1),i(\"el-form-item\",{attrs:{label:\"设备型号\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入设备型号\"},model:{value:e.form.gbModel,callback:function(t){e.$set(e.form,\"gbModel\",t)},expression:\"form.gbModel\"}})],1),i(\"el-form-item\",{attrs:{label:\"行政区域\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入行政区域\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[i(\"el-button\",{on:{click:function(t){return e.chooseCivilCode()}}},[e._v(\"选择\")])]},proxy:!0}]),model:{value:e.form.gbCivilCode,callback:function(t){e.$set(e.form,\"gbCivilCode\",t)},expression:\"form.gbCivilCode\"}})],1),i(\"el-form-item\",{attrs:{label:\"安装地址\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入安装地址\"},model:{value:e.form.gbAddress,callback:function(t){e.$set(e.form,\"gbAddress\",t)},expression:\"form.gbAddress\"}})],1),i(\"el-form-item\",{attrs:{label:\"子设备\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择是否有子设备\"},model:{value:e.form.gbParental,callback:function(t){e.$set(e.form,\"gbParental\",t)},expression:\"form.gbParental\"}},[i(\"el-option\",{attrs:{label:\"有\",value:1}}),i(\"el-option\",{attrs:{label:\"无\",value:0}})],1)],1),i(\"el-form-item\",{attrs:{label:\"父节点编码\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入父节点编码或选择所属虚拟组织\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[i(\"el-button\",{on:{click:function(t){return e.chooseGroup()}}},[e._v(\"选择\")])]},proxy:!0}]),model:{value:e.form.gbParentId,callback:function(t){e.$set(e.form,\"gbParentId\",t)},expression:\"form.gbParentId\"}})],1),i(\"el-form-item\",{attrs:{label:\"设备状态\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择设备状态\"},model:{value:e.form.gbStatus,callback:function(t){e.$set(e.form,\"gbStatus\",t)},expression:\"form.gbStatus\"}},[i(\"el-option\",{attrs:{label:\"在线\",value:\"ON\"}}),i(\"el-option\",{attrs:{label:\"离线\",value:\"OFF\"}})],1)],1),i(\"el-form-item\",{attrs:{label:\"经度\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入经度\"},model:{value:e.form.gbLongitude,callback:function(t){e.$set(e.form,\"gbLongitude\",t)},expression:\"form.gbLongitude\"}})],1),i(\"el-form-item\",{attrs:{label:\"纬度\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入纬度\"},model:{value:e.form.gbLatitude,callback:function(t){e.$set(e.form,\"gbLatitude\",t)},expression:\"form.gbLatitude\"}})],1),i(\"el-form-item\",{attrs:{label:\"云台类型\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择云台类型\"},model:{value:e.form.gbPtzType,callback:function(t){e.$set(e.form,\"gbPtzType\",t)},expression:\"form.gbPtzType\"}},[i(\"el-option\",{attrs:{label:\"球机\",value:1}}),i(\"el-option\",{attrs:{label:\"半球\",value:2}}),i(\"el-option\",{attrs:{label:\"固定枪机\",value:3}}),i(\"el-option\",{attrs:{label:\"遥控枪机\",value:4}}),i(\"el-option\",{attrs:{label:\"遥控半球\",value:5}}),i(\"el-option\",{attrs:{label:\"多目设备的全景/拼接通道\",value:6}}),i(\"el-option\",{attrs:{label:\"多目设备的分割通道\",value:7}})],1)],1)],1),i(\"div\",[i(\"el-form-item\",{attrs:{label:\"警区\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入警区\"},model:{value:e.form.gbBlock,callback:function(t){e.$set(e.form,\"gbBlock\",t)},expression:\"form.gbBlock\"}})],1),i(\"el-form-item\",{attrs:{label:\"设备归属\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入设备归属\"},model:{value:e.form.gbOwner,callback:function(t){e.$set(e.form,\"gbOwner\",t)},expression:\"form.gbOwner\"}})],1),i(\"el-form-item\",{attrs:{label:\"信令安全模式\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择信令安全模式\"},model:{value:e.form.gbSafetyWay,callback:function(t){e.$set(e.form,\"gbSafetyWay\",t)},expression:\"form.gbSafetyWay\"}},[i(\"el-option\",{attrs:{label:\"不采用\",value:0}}),i(\"el-option\",{attrs:{label:\"S/MIME签名\",value:2}}),i(\"el-option\",{attrs:{label:\"S/MIME加密签名同时采用\",value:3}}),i(\"el-option\",{attrs:{label:\"数字摘要\",value:4}})],1)],1),i(\"el-form-item\",{attrs:{label:\"注册方式\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择注册方式\"},model:{value:e.form.gbRegisterWay,callback:function(t){e.$set(e.form,\"gbRegisterWay\",t)},expression:\"form.gbRegisterWay\"}},[i(\"el-option\",{attrs:{label:\"IETFRFC3261标准\",value:1}}),i(\"el-option\",{attrs:{label:\"基于口令的双向认证\",value:2}}),i(\"el-option\",{attrs:{label:\"基于数字证书的双向认证注册\",value:3}})],1)],1),i(\"el-form-item\",{attrs:{label:\"证书序列号\"}},[i(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入证书序列号\"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,\"gbCertNum\",t)},expression:\"form.gbCertNum\"}})],1),i(\"el-form-item\",{attrs:{label:\"证书有效标识\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择证书有效标识\"},model:{value:e.form.gbCertifiable,callback:function(t){e.$set(e.form,\"gbCertifiable\",t)},expression:\"form.gbCertifiable\"}},[i(\"el-option\",{attrs:{label:\"有效\",value:1}}),i(\"el-option\",{attrs:{label:\"无效\",value:0}})],1)],1),i(\"el-form-item\",{attrs:{label:\"无效原因码\"}},[i(\"el-input\",{attrs:{type:\"errCode\",placeholder:\"请输入无效原因码\"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,\"gbCertNum\",t)},expression:\"form.gbCertNum\"}})],1),i(\"el-form-item\",{attrs:{label:\"证书终止有效期\"}},[i(\"el-date-picker\",{staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择日期时间\"},model:{value:e.form.gbEndTime,callback:function(t){e.$set(e.form,\"gbEndTime\",t)},expression:\"form.gbEndTime\"}})],1),i(\"el-form-item\",{attrs:{label:\"保密属性\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择保密属性\"},model:{value:e.form.gbSecrecy,callback:function(t){e.$set(e.form,\"gbSecrecy\",t)},expression:\"form.gbSecrecy\"}},[i(\"el-option\",{attrs:{label:\"不涉密\",value:0}}),i(\"el-option\",{attrs:{label:\"涉密\",value:1}})],1)],1),i(\"el-form-item\",{attrs:{label:\"IP地址\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入IP地址\"},model:{value:e.form.gbIpAddress,callback:function(t){e.$set(e.form,\"gbIpAddress\",t)},expression:\"form.gbIpAddress\"}})],1),i(\"el-form-item\",{attrs:{label:\"端口\"}},[i(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入端口\"},model:{value:e.form.gbPort,callback:function(t){e.$set(e.form,\"gbPort\",t)},expression:\"form.gbPort\"}})],1),i(\"el-form-item\",{attrs:{label:\"设备口令\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入设备口令\"},model:{value:e.form.gbPassword,callback:function(t){e.$set(e.form,\"gbPassword\",t)},expression:\"form.gbPassword\"}})],1)],1),i(\"div\",[i(\"el-form-item\",{attrs:{label:\"业务分组编号\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入业务分组编号\"},model:{value:e.form.gbBusinessGroupId,callback:function(t){e.$set(e.form,\"gbBusinessGroupId\",t)},expression:\"form.gbBusinessGroupId\"}})],1),i(\"el-form-item\",{attrs:{label:\"位置类型\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbPositionType,callback:function(t){e.$set(e.form,\"gbPositionType\",t)},expression:\"form.gbPositionType\"}},[i(\"el-option\",{attrs:{label:\"省际检查站\",value:1}}),i(\"el-option\",{attrs:{label:\"党政机关\",value:2}}),i(\"el-option\",{attrs:{label:\"车站码头\",value:3}}),i(\"el-option\",{attrs:{label:\"中心广场\",value:4}}),i(\"el-option\",{attrs:{label:\"体育场馆\",value:5}}),i(\"el-option\",{attrs:{label:\"商业中心\",value:6}}),i(\"el-option\",{attrs:{label:\"宗教场所\",value:7}}),i(\"el-option\",{attrs:{label:\"校园周边\",value:8}}),i(\"el-option\",{attrs:{label:\"治安复杂区域\",value:9}}),i(\"el-option\",{attrs:{label:\"交通干线\",value:10}})],1)],1),i(\"el-form-item\",{attrs:{label:\"室外/室内\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbRoomType,callback:function(t){e.$set(e.form,\"gbRoomType\",t)},expression:\"form.gbRoomType\"}},[i(\"el-option\",{attrs:{label:\"室外\",value:1}}),i(\"el-option\",{attrs:{label:\"室内\",value:2}})],1)],1),i(\"el-form-item\",{attrs:{label:\"用途\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbUseType,callback:function(t){e.$set(e.form,\"gbUseType\",t)},expression:\"form.gbUseType\"}},[i(\"el-option\",{attrs:{label:\"治安\",value:1}}),i(\"el-option\",{attrs:{label:\"交通\",value:2}}),i(\"el-option\",{attrs:{label:\"重点\",value:3}})],1)],1),i(\"el-form-item\",{attrs:{label:\"补光\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbSupplyLightType,callback:function(t){e.$set(e.form,\"gbSupplyLightType\",t)},expression:\"form.gbSupplyLightType\"}},[i(\"el-option\",{attrs:{label:\"无补光\",value:1}}),i(\"el-option\",{attrs:{label:\"红外补光\",value:2}}),i(\"el-option\",{attrs:{label:\"白光补光\",value:3}}),i(\"el-option\",{attrs:{label:\"激光补光\",value:4}}),i(\"el-option\",{attrs:{label:\"其他\",value:9}})],1)],1),i(\"el-form-item\",{attrs:{label:\"监视方位\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbDirectionType,callback:function(t){e.$set(e.form,\"gbDirectionType\",t)},expression:\"form.gbDirectionType\"}},[i(\"el-option\",{attrs:{label:\"东(西向东)\",value:1}}),i(\"el-option\",{attrs:{label:\"西(东向西)\",value:2}}),i(\"el-option\",{attrs:{label:\"南(北向南)\",value:3}}),i(\"el-option\",{attrs:{label:\"北(南向北)\",value:4}}),i(\"el-option\",{attrs:{label:\"东南(西北到东南)\",value:5}}),i(\"el-option\",{attrs:{label:\"东北(西南到东北)\",value:6}}),i(\"el-option\",{attrs:{label:\"西南(东北到西南)\",value:7}}),i(\"el-option\",{attrs:{label:\"西北(东南到西北)\",value:8}})],1)],1),i(\"el-form-item\",{attrs:{label:\"分辨率\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入分辨率\"},model:{value:e.form.gbResolution,callback:function(t){e.$set(e.form,\"gbResolution\",t)},expression:\"form.gbResolution\"}})],1),i(\"el-form-item\",{attrs:{label:\"下载倍速\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{multiple:\"\",placeholder:\"请选择位置类型\"},model:{value:e.form.gbDownloadSpeedArray,callback:function(t){e.$set(e.form,\"gbDownloadSpeedArray\",t)},expression:\"form.gbDownloadSpeedArray\"}},[i(\"el-option\",{attrs:{label:\"1倍速\",value:\"1\"}}),i(\"el-option\",{attrs:{label:\"2倍速\",value:\"2\"}}),i(\"el-option\",{attrs:{label:\"4倍速\",value:\"4\"}}),i(\"el-option\",{attrs:{label:\"8倍速\",value:\"8\"}}),i(\"el-option\",{attrs:{label:\"16倍速\",value:\"16\"}})],1)],1),i(\"el-form-item\",{attrs:{label:\"空域编码能力\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择空域编码能力\"},model:{value:e.form.gbSvcSpaceSupportMod,callback:function(t){e.$set(e.form,\"gbSvcSpaceSupportMod\",t)},expression:\"form.gbSvcSpaceSupportMod\"}},[i(\"el-option\",{attrs:{label:\"1级增强\",value:\"1\"}}),i(\"el-option\",{attrs:{label:\"2级增强\",value:\"2\"}}),i(\"el-option\",{attrs:{label:\"3级增强\",value:\"3\"}})],1)],1),i(\"el-form-item\",{attrs:{label:\"时域编码能力\"}},[i(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择空域编码能力\"},model:{value:e.form.gbSvcTimeSupportMode,callback:function(t){e.$set(e.form,\"gbSvcTimeSupportMode\",t)},expression:\"form.gbSvcTimeSupportMode\"}},[i(\"el-option\",{attrs:{label:\"1级增强\",value:\"1\"}}),i(\"el-option\",{attrs:{label:\"2级增强\",value:\"2\"}}),i(\"el-option\",{attrs:{label:\"3级增强\",value:\"3\"}})],1)],1),i(\"div\",{staticStyle:{float:\"right\"}},[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.onSubmit}},[e._v(\"保存\")]),e.cancel?i(\"el-button\",{on:{click:e.cancelSubmit}},[e._v(\"取消\")]):e._e(),1===e.form.dataType?i(\"el-button\",{on:{click:e.reset}},[e._v(\"重置\")]):e._e()],1)],1)]),i(\"channelCode\",{ref:\"channelCode\"}),i(\"chooseCivilCode\",{ref:\"chooseCivilCode\"}),i(\"chooseGroup\",{ref:\"chooseGroup\"})],1)},o=[],n=(i(\"a15b\"),i(\"d3b7\"),i(\"165c\")),a=i(\"363b\"),l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"chooseGroup\"}},[i(\"el-dialog\",{directives:[{name:\"el-drag-dialog\",rawName:\"v-el-drag-dialog\"}],attrs:{title:\"选择虚拟组织\",width:\"30%\",top:\"5rem\",\"append-to-body\":!0,\"close-on-click-modal\":!1,visible:e.showDialog,\"destroy-on-close\":!0},on:{\"update:visible\":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i(\"GroupTree\",{ref:\"regionTree\",attrs:{\"show-header\":!0,edit:!0,\"enable-add-channel\":!1,\"click-event\":e.treeNodeClickEvent,\"on-channel-change\":e.onChannelChange,\"tree-height\":\"45vh\"}}),i(\"el-form\",[i(\"el-form-item\",[i(\"div\",{staticStyle:{\"text-align\":\"right\"}},[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.onSubmit}},[e._v(\"保存\")]),i(\"el-button\",{on:{click:e.close}},[e._v(\"取消\")])],1)])],1)],1)],1)},r=[],c=i(\"a888\"),d=i(\"c2c8\"),u={name:\"ChooseCivilCode\",directives:{elDragDialog:c[\"a\"]},components:{GroupTree:d[\"a\"]},props:{},data:function(){return{showDialog:!1,endCallback:!1,groupDeviceId:\"\",businessGroup:\"\"}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.endCallback=e},onSubmit:function(){this.endCallback&&this.endCallback(this.groupDeviceId,this.businessGroup),this.close()},close:function(){this.showDialog=!1},treeNodeClickEvent:function(e){\"\"!==e.deviceId&&e.deviceId!==e.businessGroup&&(this.groupDeviceId=e.deviceId,this.businessGroup=e.businessGroup)},onChannelChange:function(e){}}},h=u,p=i(\"2877\"),m=Object(p[\"a\"])(h,l,r,!1,null,null,null),f=m.exports,b={name:\"CommonChannelEdit\",components:{ChooseCivilCode:a[\"a\"],ChooseGroup:f,channelCode:n[\"a\"]},props:[\"id\",\"dataForm\",\"saveSuccess\",\"cancel\"],data:function(){return{loading:!1,form:{}}},created:function(){this.id?this.getCommonChannel():(this.dataForm.gbDeviceId||(this.dataForm.gbDeviceId=\"\"),console.log(this.dataForm),this.form=this.dataForm)},methods:{onSubmit:function(){var e=this;this.loading=!0,this.form.gbDownloadSpeedArray&&(this.form.gbDownloadSpeed=this.form.gbDownloadSpeedArray.join(\"/\")),this.form.gbId?this.$store.dispatch(\"commonChanel/update\",this.form).then((function(t){e.$message.success({showClose:!0,message:\"保存成功\"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]})):this.$store.dispatch(\"commonChanel/add\",this.form).then((function(t){e.$message.success({showClose:!0,message:\"保存成功\"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]}))},reset:function(){var e=this;this.$confirm(\"确定重置为默认内容?\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){e.loading=!0,e.$axios({method:\"post\",url:\"/api/common/channel/reset\",params:{id:e.form.gbId}}).then((function(t){0===t.data.code&&(e.$message.success({showClose:!0,message:\"重置成功 已保存\"}),e.getCommonChannel())})).catch((function(e){console.error(e)})).finally((function(){return[e.loading=!1]}))})).catch((function(){}))},getCommonChannel:function(){var e=this;this.loading=!0,this.$store.dispatch(\"commonChanel/queryOne\",this.id).then((function(t){t.gbDownloadSpeed&&(t.gbDownloadSpeedArray=t.gbDownloadSpeed.split(\"/\")),e.form=t})).finally((function(){e.loading=!1}))},buildDeviceIdCode:function(e){var t=this;this.$refs.channelCode.openDialog((function(e){console.log(t.form),console.log(\"code===> \"+e),t.form.gbDeviceId=e,console.log(\"code22===> \"+e)}),e)},chooseCivilCode:function(){var e=this;this.$refs.chooseCivilCode.openDialog((function(t){e.form.gbCivilCode=t}))},chooseGroup:function(){var e=this;this.$refs.chooseGroup.openDialog((function(t,i){e.form.gbBusinessGroupId=i,e.form.gbParentId=t}))},cancelSubmit:function(){this.cancel&&this.cancel()}}},g=b,v=(i(\"257e\"),Object(p[\"a\"])(g,s,o,!1,null,null,null));t[\"a\"]=v.exports},\"953f\":function(e,t,i){},\"989a\":function(e,t,i){\"use strict\";i(\"953f\")},a135:function(e,t,i){\"use strict\";i(\"b21a\")},b21a:function(e,t,i){},b680:function(e,t,i){\"use strict\";var s=i(\"23e7\"),o=i(\"a691\"),n=i(\"408a\"),a=i(\"1148\"),l=i(\"d039\"),r=1..toFixed,c=Math.floor,d=function(e,t,i){return 0===t?i:t%2===1?d(e,t-1,i*e):d(e*e,t/2,i)},u=function(e){var t=0,i=e;while(i>=4096)t+=12,i/=4096;while(i>=2)t+=1,i/=2;return t},h=r&&(\"0.000\"!==8e-5.toFixed(3)||\"1\"!==.9.toFixed(0)||\"1.25\"!==1.255.toFixed(2)||\"1000000000000000128\"!==(0xde0b6b3a7640080).toFixed(0))||!l((function(){r.call({})}));s({target:\"Number\",proto:!0,forced:h},{toFixed:function(e){var t,i,s,l,r=n(this),h=o(e),p=[0,0,0,0,0,0],m=\"\",f=\"0\",b=function(e,t){var i=-1,s=t;while(++i<6)s+=e*p[i],p[i]=s%1e7,s=c(s/1e7)},g=function(e){var t=6,i=0;while(--t>=0)i+=p[t],p[t]=c(i/e),i=i%e*1e7},v=function(){var e=6,t=\"\";while(--e>=0)if(\"\"!==t||0===e||0!==p[e]){var i=String(p[e]);t=\"\"===t?i:t+a.call(\"0\",7-i.length)+i}return t};if(h<0||h>20)throw RangeError(\"Incorrect fraction digits\");if(r!=r)return\"NaN\";if(r<=-1e21||r>=1e21)return String(r);if(r<0&&(m=\"-\",r=-r),r>1e-21)if(t=u(r*d(2,69,1))-69,i=t<0?r*d(2,-t,1):r/d(2,t,1),i*=4503599627370496,t=52-t,t>0){b(0,i),s=h;while(s>=7)b(1e7,0),s-=7;b(d(10,s,1),0),s=t-1;while(s>=23)g(1<<23),s-=23;g(1<<s),b(1,1),g(2),f=v()}else b(0,i),b(1<<-t,0),f=v()+a.call(\"0\",h);return h>0?(l=f.length,f=m+(l<=h?\"0.\"+a.call(\"0\",h-l)+f:f.slice(0,l-h)+\".\"+f.slice(l-h))):f=m+f,f}})},bbbb:function(e,t,i){},bbf2:function(e,t,i){\"use strict\";i.r(t);var s=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},o=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{attrs:{id:\"rtcPlayer\"}},[i(\"video\",{staticStyle:{\"text-align\":\"left\"},attrs:{id:\"webRtcPlayerBox\",controls:\"\",autoplay:\"\"}},[e._v(\" Your browser is too old which doesn't support HTML5 video. \")])])}],n=null,a={name:\"RtcPlayer\",props:[\"videoUrl\",\"error\",\"hasaudio\"],data:function(){return{timer:null}},watch:{videoUrl:function(e,t){this.pause(),this.play(e)},immediate:!0},mounted:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){\"undefined\"===typeof e.videoUrl&&(e.videoUrl=t),console.log(\"初始化时的地址为: \"+e.videoUrl),e.play(e.videoUrl)}))},destroyed:function(){clearTimeout(this.timer)},methods:{play:function(e){var t=this;n=new ZLMRTCClient.Endpoint({element:document.getElementById(\"webRtcPlayerBox\"),debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!0,recvOnly:!0,usedatachannel:!1}),n.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error(\"ICE 协商出错\"),t.eventcallbacK(\"ICE ERROR\",\"ICE 协商出错\")})),n.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS,(function(e){console.log(\"播放成功\",e.streams),t.eventcallbacK(\"playing\",\"播放成功\")})),n.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(i){console.error(\"offer anwser 交换失败\",i),t.eventcallbacK(\"OFFER ANSWER ERROR \",\"offer anwser 交换失败\"),-400==i.code&&\"流不存在\"==i.msg&&(console.log(\"流不存在\"),t.timer=setTimeout((function(){t.webrtcPlayer.close(),t.play(e)}),100))})),n.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM,(function(e){t.eventcallbacK(\"LOCAL STREAM\",\"获取到了本地流\")}))},pause:function(){null!=n&&(n.close(),n=null)},eventcallbacK:function(e,t){console.log(\"player 事件回调\"),console.log(e),console.log(t)}}},l=a,r=(i(\"6ebe\"),i(\"2877\")),c=Object(r[\"a\"])(l,s,o,!1,null,null,null);t[\"default\"]=c.exports},c5f0:function(e,t,i){\"use strict\";i(\"bbbb\")},d017:function(e,t,i){},e0ad:function(e,t,i){},e8b4:function(e,t,i){\"use strict\";i(\"0868\")},e9c4:function(e,t,i){var s=i(\"23e7\"),o=i(\"d066\"),n=i(\"d039\"),a=o(\"JSON\",\"stringify\"),l=/[\\uD800-\\uDFFF]/g,r=/^[\\uD800-\\uDBFF]$/,c=/^[\\uDC00-\\uDFFF]$/,d=function(e,t,i){var s=i.charAt(t-1),o=i.charAt(t+1);return r.test(e)&&!c.test(o)||c.test(e)&&!r.test(s)?\"\\\\u\"+e.charCodeAt(0).toString(16):e},u=n((function(){return'\"\\\\udf06\\\\ud834\"'!==a(\"\\udf06\\ud834\")||'\"\\\\udead\"'!==a(\"\\udead\")}));a&&s({target:\"JSON\",stat:!0,forced:u},{stringify:function(e,t,i){var s=a.apply(null,arguments);return\"string\"==typeof s?s.replace(l,d):s}})},fdc8:function(e,t,i){\"use strict\";i(\"7295\")}}]);", "extractedComments": []}