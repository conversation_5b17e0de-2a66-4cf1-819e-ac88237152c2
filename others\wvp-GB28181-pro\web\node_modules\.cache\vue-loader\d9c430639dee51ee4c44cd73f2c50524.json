{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750428896989}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}