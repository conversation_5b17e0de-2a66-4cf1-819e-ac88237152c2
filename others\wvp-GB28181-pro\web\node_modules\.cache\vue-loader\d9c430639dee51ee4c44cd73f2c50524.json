{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750430544768}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}