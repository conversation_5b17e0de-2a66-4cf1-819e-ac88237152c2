{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\common\\h265web.vue", "mtime": 1750431331348}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpjb25zdCBoMjY1d2ViUGxheWVyID0ge30NCi8qKg0KICog5LuOZ2l0aHVi5LiK5aSN5Yi255qEDQogKiBAc2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9udW1iZXJ3b2xmL2gyNjV3ZWIuanMvYmxvYi9tYXN0ZXIvZXhhbXBsZV9ub3JtYWwvaW5kZXguanMNCiAqLw0KY29uc3QgdG9rZW4gPSAnYmFzZTY0OlFYVjBhRzl5T21Ob1lXNW5lV0Z1Ykc5dVozeHVkVzFpWlhKM2IyeG1MRWRwZEdoMVlqcG9kSFJ3Y3pvdkwyZHBkR2gxWWk1amIyMHZiblZ0WW1WeWQyOXNaaXhGYldGcGJEcHdiM0p6WTJobFozUXlNMEJtYjNodFlXbHNMbU52YlN4UlVUbzFNekV6TmpVNE56SXNTRzl0WlZCaFoyVTZhSFIwY0RvdkwzaDJhV1JsYnk1MmFXUmxieXhFYVhOamIzSmtPbTUxYldKbGNuZHZiR1lqT0RZNU5DeDNaV05vWVhJNmJuVnRZbVZ5ZDI5c1pqRXhMRUpsYVdwcGJtY3NWMjl5YTBsdU9rSmhhV1IxJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnSDI2NXdlYicsDQogIHByb3BzOiBbJ3ZpZGVvVXJsJywgJ2Vycm9yJywgJ2hhc0F1ZGlvJywgJ2hlaWdodCcsICdzaG93QnV0dG9uJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHBsYXlpbmc6IGZhbHNlLA0KICAgICAgaXNOb3RNdXRlOiBmYWxzZSwNCiAgICAgIHF1aWV0aW5nOiBmYWxzZSwNCiAgICAgIGZ1bGxzY3JlZW46IGZhbHNlLA0KICAgICAgbG9hZGVkOiBmYWxzZSwgLy8gbXV0ZQ0KICAgICAgc3BlZWQ6IDAsDQogICAgICBrQnBzOiAwLA0KICAgICAgYnRuRG9tOiBudWxsLA0KICAgICAgdmlkZW9JbmZvOiBudWxsLA0KICAgICAgdm9sdW1lOiAxLA0KICAgICAgcm90YXRlOiAwLA0KICAgICAgdm9kOiB0cnVlLCAvLyDngrnmkq0NCiAgICAgIGZvcmNlTm9PZmZzY3JlZW46IGZhbHNlLA0KICAgICAgcGxheWVyV2lkdGg6IDAsDQogICAgICBwbGF5ZXJIZWlnaHQ6IDAsDQogICAgICBpbml0ZWQ6IGZhbHNlLA0KICAgICAgcGxheWVyTG9hZGluZzogZmFsc2UsDQogICAgICBtZWRpYUluZm86IG51bGwsDQogICAgICBsYXN0UGxheVRpbWVVcGRhdGU6IDAgLy8g6K6w5b2V5pyA5ZCO5LiA5qyh5pKt5pS+5pe26Ze05pu05paw55qE5pe26Ze05oizDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIHZpZGVvVXJsKG5ld0RhdGEsIG9sZERhdGEpIHsNCiAgICAgIHRoaXMucGxheShuZXdEYXRhKQ0KICAgIH0sDQogICAgcGxheWluZyhuZXdEYXRhLCBvbGREYXRhKSB7DQogICAgICB0aGlzLiRlbWl0KCdwbGF5U3RhdHVzQ2hhbmdlJywgbmV3RGF0YSkNCiAgICB9LA0KICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIGNvbnN0IHBhcmFtVXJsID0gZGVjb2RlVVJJQ29tcG9uZW50KHRoaXMuJHJvdXRlLnBhcmFtcy51cmwpDQogICAgd2luZG93Lm9ucmVzaXplID0gKCkgPT4gew0KICAgICAgdGhpcy51cGRhdGVQbGF5ZXJEb21TaXplKCkNCiAgICB9DQogICAgdGhpcy5idG5Eb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnYnV0dG9uc0JveCcpDQoNCiAgICAvLyDlhajlsYDmi6bmiKrlmajlt7LlnKhtYWluLmpz5Lit5ZCv5Yqo77yM6L+Z6YeM5Y+q6ZyA6KaB56Gu5L+d5oum5oiq5Zmo5q2j5bi45bel5L2cDQogICAgdGhpcy5lbnN1cmVHbG9iYWxJbnRlcmNlcHRvckFjdGl2ZSgpDQoNCiAgICBjb25zb2xlLmxvZygn5Yid5aeL5YyW5pe255qE5Zyw5Z2A5Li6OiAnICsgcGFyYW1VcmwpDQogICAgaWYgKHBhcmFtVXJsKSB7DQogICAgICB0aGlzLnBsYXkodGhpcy52aWRlb1VybCkNCiAgICB9DQogIH0sDQogIGRlc3Ryb3llZCgpIHsNCiAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdKSB7DQogICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uZGVzdHJveSgpDQogICAgfQ0KICAgIHRoaXMucGxheWluZyA9IGZhbHNlDQogICAgdGhpcy5sb2FkZWQgPSBmYWxzZQ0KICAgIHRoaXMucGxheWVyTG9hZGluZyA9IGZhbHNlDQoNCiAgICAvLyDlhajlsYDmi6bmiKrlmajkvJrmjIHnu63lt6XkvZzvvIzkuI3pnIDopoHlnKjnu4Tku7bplIDmr4Hml7bmgaLlpI0NCiAgICAvLyBjb25zb2xlLmxvZygnW2gyNjV3ZWJdIOe7hOS7tumUgOavge+8jOWFqOWxgOaLpuaIquWZqOe7p+e7reW3peS9nCcpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICB1cGRhdGVQbGF5ZXJEb21TaXplKCkgew0KICAgICAgY29uc3QgZG9tID0gdGhpcy4kcmVmcy5jb250YWluZXINCiAgICAgIGlmICghdGhpcy5wYXJlbnROb2RlUmVzaXplT2JzZXJ2ZXIpIHsNCiAgICAgICAgdGhpcy5wYXJlbnROb2RlUmVzaXplT2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoZW50cmllcyA9PiB7DQogICAgICAgICAgdGhpcy51cGRhdGVQbGF5ZXJEb21TaXplKCkNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5wYXJlbnROb2RlUmVzaXplT2JzZXJ2ZXIub2JzZXJ2ZShkb20ucGFyZW50Tm9kZSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IGJveFdpZHRoID0gZG9tLnBhcmVudE5vZGUuY2xpZW50V2lkdGgNCiAgICAgIGNvbnN0IGJveEhlaWdodCA9IGRvbS5wYXJlbnROb2RlLmNsaWVudEhlaWdodA0KICAgICAgbGV0IHdpZHRoID0gYm94V2lkdGgNCiAgICAgIGxldCBoZWlnaHQgPSAoOSAvIDE2KSAqIHdpZHRoDQogICAgICBpZiAoYm94SGVpZ2h0ID4gMCAmJiBib3hXaWR0aCA+IGJveEhlaWdodCAvIDkgKiAxNikgew0KICAgICAgICBoZWlnaHQgPSBib3hIZWlnaHQNCiAgICAgICAgd2lkdGggPSBib3hIZWlnaHQgLyA5ICogMTYNCiAgICAgIH0NCg0KICAgICAgY29uc3QgY2xpZW50SGVpZ2h0ID0gTWF0aC5taW4oZG9jdW1lbnQuYm9keS5jbGllbnRIZWlnaHQsIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQpDQogICAgICBpZiAoaGVpZ2h0ID4gY2xpZW50SGVpZ2h0KSB7DQogICAgICAgIGhlaWdodCA9IGNsaWVudEhlaWdodA0KICAgICAgICB3aWR0aCA9ICgxNiAvIDkpICogaGVpZ2h0DQogICAgICB9DQoNCiAgICAgIHRoaXMuJHJlZnMucGxheWVyQm94LnN0eWxlLndpZHRoID0gd2lkdGggKyAncHgnDQogICAgICB0aGlzLiRyZWZzLnBsYXllckJveC5zdHlsZS5oZWlnaHQgPSBoZWlnaHQgKyAncHgnDQogICAgICB0aGlzLnBsYXllcldpZHRoID0gd2lkdGgNCiAgICAgIHRoaXMucGxheWVySGVpZ2h0ID0gaGVpZ2h0DQogICAgICBpZiAodGhpcy5wbGF5aW5nKSB7DQogICAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5yZXNpemUodGhpcy5wbGF5ZXJXaWR0aCwgdGhpcy5wbGF5ZXJIZWlnaHQpDQogICAgICB9DQogICAgfSwNCiAgICByZXNpemUod2lkdGgsIGhlaWdodCkgew0KICAgICAgdGhpcy5wbGF5ZXJXaWR0aCA9IHdpZHRoDQogICAgICB0aGlzLnBsYXllckhlaWdodCA9IGhlaWdodA0KICAgICAgdGhpcy4kcmVmcy5wbGF5ZXJCb3guc3R5bGUud2lkdGggPSB3aWR0aCArICdweCcNCiAgICAgIHRoaXMuJHJlZnMucGxheWVyQm94LnN0eWxlLmhlaWdodCA9IGhlaWdodCArICdweCcNCiAgICAgIGlmICh0aGlzLnBsYXlpbmcpIHsNCiAgICAgICAgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnJlc2l6ZSh0aGlzLnBsYXllcldpZHRoLCB0aGlzLnBsYXllckhlaWdodCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGNyZWF0ZSh1cmwpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vliJvlu7rmkq3mlL7lmajlrp7kvovvvIxVSUQ6JywgdGhpcy5fdWlkLCAnVVJMOicsIHVybCkNCg0KICAgICAgLy8g5YaN5qyh56Gu5L+d5rKh5pyJ5q6L55WZ55qE5pKt5pS+5Zmo5a6e5L6LDQogICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5Yib5bu65YmN5Y+R546w5q6L55WZ55qE5pKt5pS+5Zmo5a6e5L6L77yM5by65Yi25riF55CG77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnJlbGVhc2UpIHsNCiAgICAgICAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5yZWxlYXNlKCkNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmuIXnkIbmrovnlZnmkq3mlL7lmajml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgIH0NCiAgICAgICAgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdID0gbnVsbA0KICAgICAgICB0aGlzLmNsZWFyUGxheWVyRE9NKCkNCiAgICAgIH0NCg0KICAgICAgdGhpcy5wbGF5ZXJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3Qgb3B0aW9ucyA9IHt9DQoNCiAgICAgIGNvbnNvbGUubG9nKCfmraPlnKjliJvlu7rmlrDnmoRoMjY1d2Vi5pKt5pS+5Zmo5a6e5L6L77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSA9IG5ldyB3aW5kb3cubmV3MjY1d2VianModXJsLCBPYmplY3QuYXNzaWduKA0KICAgICAgICB7DQogICAgICAgICAgcGxheWVyOiAnZ2xwbGF5ZXInLCAvLyDmkq3mlL7lmajlrrnlmahpZA0KICAgICAgICAgIHdpZHRoOiB0aGlzLnBsYXllcldpZHRoLA0KICAgICAgICAgIGhlaWdodDogdGhpcy5wbGF5ZXJIZWlnaHQsDQogICAgICAgICAgdG9rZW46IHRva2VuLA0KICAgICAgICAgIGV4dEluZm86IHsNCiAgICAgICAgICAgIGNvcmVQcm9iZVBhcnQ6IDAuNCwNCiAgICAgICAgICAgIHByb2JlU2l6ZTogODE5MiwNCiAgICAgICAgICAgIGlnbm9yZUF1ZGlvOiB0aGlzLmhhc0F1ZGlvID09IG51bGwgPyAwIDogKHRoaXMuaGFzQXVkaW8gPyAwIDogMSkNCiAgICAgICAgICB9LA0KICAgICAgICAgIC8vIOWxj+iUvee7n+iuoeWSjOaXpeW/l+ebuOWFs+mFjee9rg0KICAgICAgICAgIGRlYnVnOiBmYWxzZSwgLy8g5YWz6Zet6LCD6K+V5qih5byPDQogICAgICAgICAgZGVidWdMZXZlbDogMCwgLy8g6K6+572u6LCD6K+V57qn5Yir5Li6MA0KICAgICAgICAgIGxvZ0xldmVsOiAwLCAvLyDlhbPpl63ml6Xlv5fovpPlh7oNCiAgICAgICAgICBkaXNhYmxlU3RhdHM6IHRydWUsIC8vIOemgeeUqOe7n+iuoQ0KICAgICAgICAgIGRpc2FibGVBbmFseXRpY3M6IHRydWUsIC8vIOemgeeUqOWIhuaekA0KICAgICAgICAgIG5vU3RhdHM6IHRydWUsIC8vIOS4jeWPkemAgee7n+iuoeaVsOaNrg0KICAgICAgICAgIG5vTG9nOiB0cnVlLCAvLyDkuI3ovpPlh7rml6Xlv5cNCiAgICAgICAgICBzaWxlbnQ6IHRydWUgLy8g6Z2Z6buY5qih5byPDQogICAgICAgIH0sDQogICAgICAgIG9wdGlvbnMNCiAgICAgICkpDQoNCiAgICAgIGNvbnNvbGUubG9nKCdoMjY1d2Vi5pKt5pS+5Zmo5a6e5L6L5Yib5bu65oiQ5Yqf77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgIGNvbnN0IGgyNjV3ZWIgPSBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0NCiAgICAgIGgyNjV3ZWIub25PcGVuRnVsbFNjcmVlbiA9ICgpID0+IHsNCiAgICAgICAgdGhpcy5mdWxsc2NyZWVuID0gdHJ1ZQ0KICAgICAgfQ0KICAgICAgaDI2NXdlYi5vbkNsb3NlRnVsbFNjcmVlbiA9ICgpID0+IHsNCiAgICAgICAgdGhpcy5mdWxsc2NyZWVuID0gZmFsc2UNCiAgICAgIH0NCiAgICAgIGgyNjV3ZWIub25SZWFkeVNob3dEb25lID0gKCkgPT4gew0KICAgICAgICAvLyDlh4blpIflpb3mmL7npLrkuobvvIzlsJ3or5Xoh6rliqjmkq3mlL4NCiAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqOWHhuWkh+WujOaIkO+8jOW8gOWni+iHquWKqOaSreaUvu+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzdWx0ID0gaDI2NXdlYi5wbGF5KCkNCiAgICAgICAgICBjb25zb2xlLmxvZygn6Ieq5Yqo5pKt5pS+57uT5p6cOicsIHJlc3VsdCwgJ1VJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgICAgICAvLyDlu7bov5/mo4Dmn6Xmkq3mlL7nirbmgIHvvIznoa7kv53nirbmgIHmraPnoa4NCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMucGxheWluZyA9IGgyNjV3ZWIuaXNQbGF5aW5nKCkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmkq3mlL7lmajliJ3lp4vljJblrozmiJDvvIxwbGF5aW5n54q25oCBOicsIHRoaXMucGxheWluZywgJ1VJRDonLCB0aGlzLl91aWQpDQogICAgICAgICAgICB0aGlzLnBsYXllckxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIH0sIDIwMCkNCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+aSreaUvuWZqOiHquWKqOaSreaUvuWksei0pTonLCBlcnJvcikNCiAgICAgICAgICB0aGlzLnBsYXlpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMucGxheWVyTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGgyNjV3ZWIub25Mb2FkRmluaXNoID0gKCkgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMubG9hZGVkID0gdHJ1ZQ0KICAgICAgICAgIC8vIOWPr+S7peiOt+WPlm1lZGlhSW5mbw0KICAgICAgICAgIC8vIEBzZWUgaHR0cHM6Ly9naXRodWIuY29tL251bWJlcndvbGYvaDI2NXdlYi5qcy9ibG9iLzhiMjZhMzFmZmE0MTliZDBhMGY5OWZiZDUxMTE1OTBlMTQ0ZTM2YTgvZXhhbXBsZV9ub3JtYWwvaW5kZXguanMjTDI1MkM5LUwyNjNDMTENCiAgICAgICAgICBpZiAoaDI2NXdlYi5tZWRpYUluZm8gJiYgdHlwZW9mIGgyNjV3ZWIubWVkaWFJbmZvID09PSAnZnVuY3Rpb24nKSB7DQogICAgICAgICAgICB0aGlzLm1lZGlhSW5mbyA9IGgyNjV3ZWIubWVkaWFJbmZvKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfmkq3mlL7lmajkuI3mlK/mjIFtZWRpYUluZm/mlrnms5UnKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+iOt+WPluWqkuS9k+S/oeaBr+aXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICB0aGlzLmxvYWRlZCA9IHRydWUgLy8g5LuN54S25qCH6K6w5Li65bey5Yqg6L2977yM6YG/5YWN6Zi75aGeDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGgyNjV3ZWIub25QbGF5VGltZSA9ICh2aWRlb1BUUykgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOajgOafpXZpZGVvUFRT5piv5ZCm5pyJ5pWIDQogICAgICAgICAgaWYgKHZpZGVvUFRTICE9PSBudWxsICYmIHZpZGVvUFRTICE9PSB1bmRlZmluZWQgJiYgIWlzTmFOKHZpZGVvUFRTKSkgew0KICAgICAgICAgICAgLy8g5pS25Yiw5pe26Ze05Zue6LCD77yM6K+05piO5pKt5pS+5Zmo55yf5q2j5Zyo5pKt5pS+DQogICAgICAgICAgICBpZiAoIXRoaXMucGxheWluZykgew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw5pKt5pS+5pe26Ze05Zue6LCD77yM56Gu6K6k5pKt5pS+5Zmo5q2j5Zyo5pKt5pS+77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgICAgICAgdGhpcy5wbGF5aW5nID0gdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5sYXN0UGxheVRpbWVVcGRhdGUgPSBEYXRlLm5vdygpIC8vIOiusOW9leacgOWQjuS4gOasoeaXtumXtOabtOaWsA0KICAgICAgICAgICAgdGhpcy4kZW1pdCgncGxheVRpbWVDaGFuZ2UnLCB2aWRlb1BUUykNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfmkq3mlL7lmajov5Tlm57ml6DmlYjnmoTml7bpl7TlgLw6JywgdmlkZW9QVFMpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5pe26Ze05Zue6LCD5Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBoMjY1d2ViLm9uU2Vla0ZpbmlzaCA9ICgpID0+IHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmoc2Vla+WujOaIkCcpDQogICAgICAgICAgdGhpcy4kZW1pdCgnc2Vla0ZpbmlzaCcpDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmkq3mlL7lmahzZWVr5a6M5oiQ5Zue6LCD5Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBoMjY1d2ViLmRvKCkNCiAgICB9LA0KICAgIHNjcmVlbnNob3Q6IGZ1bmN0aW9uKCkgew0KICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSkgew0KICAgICAgICBjb25zdCBjYW52YXMgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdjYW52YXMnKQ0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLm1lZGlhSW5mbykNCiAgICAgICAgY2FudmFzLndpZHRoID0gdGhpcy5tZWRpYUluZm8ubWV0YS5zaXplLndpZHRoDQogICAgICAgIGNhbnZhcy5oZWlnaHQgPSB0aGlzLm1lZGlhSW5mby5tZXRhLnNpemUuaGVpZ2h0DQogICAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5zbmFwc2hvdChjYW52YXMpIC8vIHNuYXBzaG90IHRvIGNhbnZhcw0KDQogICAgICAgIC8vIOS4i+i9veaIquWbvg0KICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpDQogICAgICAgIGxpbmsuZG93bmxvYWQgPSAnc2NyZWVuc2hvdC5wbmcnDQogICAgICAgIGxpbmsuaHJlZiA9IGNhbnZhcy50b0RhdGFVUkwoJ2ltYWdlL3BuZycpLnJlcGxhY2UoJ2ltYWdlL3BuZycsICdpbWFnZS9vY3RldC1zdHJlYW0nKQ0KICAgICAgICBsaW5rLmNsaWNrKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIHBsYXlCdG5DbGljazogZnVuY3Rpb24oZXZlbnQpIHsNCiAgICAgIHRoaXMucGxheSh0aGlzLnZpZGVvVXJsKQ0KICAgIH0sDQogICAgcGxheTogZnVuY3Rpb24odXJsKSB7DQogICAgICBjb25zb2xlLmxvZygn5byA5aeL5pKt5pS+6KeG6aKR77yMVVJMOicsIHVybCwgJ1VJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgIC8vIOehruS/neWujOWFqOa4heeQhuaXp+eahOaSreaUvuWZqA0KICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5qOA5rWL5Yiw5bey5a2Y5Zyo55qE5pKt5pS+5Zmo5a6e5L6L77yM5YWI6ZSA5q+B77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgdGhpcy5kZXN0cm95KCkNCiAgICAgICAgLy8g57uZ6ZSA5q+B5pON5L2c5pu05aSa5pe26Ze05a6M5oiQ77yM56Gu5L+dRE9N5a6M5YWo5riF55CGDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfplIDmr4HlrozmiJDvvIzph43mlrDliJvlu7rmkq3mlL7lmajvvIxVSUQ6JywgdGhpcy5fdWlkKQ0KICAgICAgICAgIHRoaXMucGxheSh1cmwpDQogICAgICAgIH0sIDMwMCkgLy8g5aKe5Yqg5bu26L+f5pe26Ze0DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBpZiAoIXVybCkgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+aSreaUvlVSTOS4uuepuu+8jOaXoOazleaSreaUvicpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5wbGF5ZXJXaWR0aCA9PT0gMCB8fCB0aGlzLnBsYXllckhlaWdodCA9PT0gMCkgew0KICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo5bC65a+45pyq5Yid5aeL5YyW77yM562J5b6FRE9N5pu05pawJykNCiAgICAgICAgdGhpcy51cGRhdGVQbGF5ZXJEb21TaXplKCkNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5wbGF5KHVybCkNCiAgICAgICAgfSwgMzAwKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2coJ+WIm+W7uuaWsOeahOaSreaUvuWZqOWunuS+i++8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICB0aGlzLmNyZWF0ZSh1cmwpDQogICAgfSwNCiAgICB1blBhdXNlOiBmdW5jdGlvbigpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmIChoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0gJiYgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBsYXkpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5byA5aeL6LCD55So5pKt5pS+5ZmocGxheeaWueazle+8jFVJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgICAgICAvLyDkvb/nlKhQcm9taXNl5aSE55CG5p2l6YG/5YWNQWJvcnRFcnJvcg0KICAgICAgICAgIGNvbnN0IHBsYXlQcm9taXNlID0gaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBsYXkoKQ0KICAgICAgICAgIGlmIChwbGF5UHJvbWlzZSAmJiB0eXBlb2YgcGxheVByb21pc2UuY2F0Y2ggPT09ICdmdW5jdGlvbicpIHsNCiAgICAgICAgICAgIHBsYXlQcm9taXNlLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgICAvLyDmkq3mlL7miJDlip/lkI7mm7TmlrDnirbmgIENCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqHBsYXkgUHJvbWlzZSByZXNvbHZlZO+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMucGxheWluZyA9IGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5pc1BsYXlpbmcoKQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmkq3mlL7lmajnirbmgIHmm7TmlrDvvIxwbGF5aW5nOicsIHRoaXMucGxheWluZywgJ1VJRDonLCB0aGlzLl91aWQpDQogICAgICAgICAgICAgIH0sIDEwMCkgLy8g57uZ5pKt5pS+5Zmo5LiA54K55pe26Ze05pu05paw54q25oCBDQogICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICAgIC8vIOW/veeVpUFib3J0RXJyb3LvvIzov5npgJrluLjmmK/nlLHkuo7lv6vpgJ/nmoRwbGF5L3BhdXNl5pON5L2c5a+86Ie055qEDQogICAgICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+aBouWkjeaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICAgICAgICB0aGlzLnBsYXlpbmcgPSBmYWxzZQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmkq3mlL7ooqvkuK3mlq3vvIhBYm9ydEVycm9y77yJ77yM6L+Z5piv5q2j5bi455qEJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5aaC5p6c5LiN5pivUHJvbWlzZe+8jOeri+WNs+abtOaWsOeKtuaAgQ0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMucGxheWluZyA9IGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5pc1BsYXlpbmcoKQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo54q25oCB5pu05paw77yI6Z2eUHJvbWlzZe+8ie+8jHBsYXlpbmc6JywgdGhpcy5wbGF5aW5nLCAnVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgICAgIH0sIDEwMCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5ZmocGxheeaWueazleiwg+eUqOWujOaIkO+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmkq3mlL7lmajlrp7kvovkuI3lrZjlnKjmiJbkuI3mlK/mjIFwbGF55pa55rOV77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCfmgaLlpI3mkq3mlL7ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgIHRoaXMucGxheWluZyA9IGZhbHNlDQogICAgICB9DQogICAgICB0aGlzLmVyciA9ICcnDQogICAgfSwNCiAgICBwYXVzZTogZnVuY3Rpb24oKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdICYmIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5wYXVzZSkgew0KICAgICAgICAgIC8vIOehruS/neaaguWBnOaTjeS9nOiDveWkn+ato+ehruaJp+ihjA0KICAgICAgICAgIGNvbnN0IHBhdXNlUmVzdWx0ID0gaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBhdXNlKCkNCiAgICAgICAgICAvLyDlpoLmnpxwYXVzZei/lOWbnlByb21pc2XvvIzlpITnkIblj6/og73nmoTplJnor68NCiAgICAgICAgICBpZiAocGF1c2VSZXN1bHQgJiYgdHlwZW9mIHBhdXNlUmVzdWx0LmNhdGNoID09PSAnZnVuY3Rpb24nKSB7DQogICAgICAgICAgICBwYXVzZVJlc3VsdC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+aaguWBnOaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5wbGF5aW5nID0gaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLmlzUGxheWluZygpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5pqC5YGc5pKt5pS+5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnBsYXlpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgICAgdGhpcy5lcnIgPSAnJw0KICAgIH0sDQoNCiAgICAvLyDlvLrliLbmkq3mlL7mlrnms5XvvIznoa7kv53mkq3mlL7lmajnnJ/mraPlvIDlp4vmkq3mlL4NCiAgICBmb3JjZVBsYXk6IGZ1bmN0aW9uKCkgew0KICAgICAgY29uc29sZS5sb2coJ+W8uuWItuaSreaUvuaWueazleiiq+iwg+eUqO+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICB0cnkgew0KICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdKSB7DQogICAgICAgICAgLy8g6K6w5b2V5by65Yi25pKt5pS+5byA5aeL5pe26Ze0DQogICAgICAgICAgY29uc3QgZm9yY2VQbGF5U3RhcnRUaW1lID0gRGF0ZS5ub3coKQ0KDQogICAgICAgICAgLy8g5YWI5qOA5p+l5b2T5YmN54q25oCBDQogICAgICAgICAgY29uc3QgY3VycmVudFBsYXlpbmcgPSBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uaXNQbGF5aW5nKCkNCiAgICAgICAgICBjb25zb2xlLmxvZygn5b2T5YmN5pKt5pS+54q25oCBOicsIGN1cnJlbnRQbGF5aW5nLCAnVUlEOicsIHRoaXMuX3VpZCkNCg0KICAgICAgICAgIC8vIOaXoOiuuuW9k+WJjeeKtuaAgeWmguS9le+8jOmDveWwneivleaSreaUvu+8jOWboOS4umlzUGxheWluZygp5Y+v6IO95LiN5YeG56GuDQogICAgICAgICAgY29uc29sZS5sb2coJ+W8uuWItuWQr+WKqOaSreaUvu+8jFVJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgICAgICAvLyDlsJ3or5XlpJrnp43mkq3mlL7mlrnms5UNCiAgICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBsYXkpIHsNCiAgICAgICAgICAgIGNvbnN0IHBsYXlSZXN1bHQgPSBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0ucGxheSgpDQogICAgICAgICAgICBjb25zb2xlLmxvZygn6LCD55SocGxheSgp5pa55rOV57uT5p6cOicsIHBsYXlSZXN1bHQsICdVSUQ6JywgdGhpcy5fdWlkKQ0KDQogICAgICAgICAgICAvLyDlu7bov5/mo4Dmn6Xmkq3mlL7nirbmgIHlkozml7bpl7Tlm57osIMNCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBuZXdQbGF5aW5nID0gaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLmlzUGxheWluZygpDQogICAgICAgICAgICAgIGNvbnN0IGhhc1JlY2VudFRpbWVVcGRhdGUgPSAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdFBsYXlUaW1lVXBkYXRlKSA8IDIwMDAgLy8gMuenkuWGheacieaXtumXtOabtOaWsA0KDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvLrliLbmkq3mlL7lkI7mo4Dmn6U6Jywgew0KICAgICAgICAgICAgICAgIGlzUGxheWluZzogbmV3UGxheWluZywNCiAgICAgICAgICAgICAgICBoYXNSZWNlbnRUaW1lVXBkYXRlOiBoYXNSZWNlbnRUaW1lVXBkYXRlLA0KICAgICAgICAgICAgICAgIGxhc3RUaW1lVXBkYXRlOiB0aGlzLmxhc3RQbGF5VGltZVVwZGF0ZSwNCiAgICAgICAgICAgICAgICB1aWQ6IHRoaXMuX3VpZA0KICAgICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAgIGlmIChuZXdQbGF5aW5nICYmIGhhc1JlY2VudFRpbWVVcGRhdGUpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5by65Yi25pKt5pS+5oiQ5Yqf77yM5pKt5pS+5Zmo5q2j5Zyo5pKt5pS+77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgICAgICAgICB0aGlzLnBsYXlpbmcgPSB0cnVlDQogICAgICAgICAgICAgIH0gZWxzZSBpZiAobmV3UGxheWluZyAmJiAhaGFzUmVjZW50VGltZVVwZGF0ZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo54q25oCB5Li65pKt5pS+5L2G5peg5pe26Ze05Zue6LCD77yM5Y+v6IO95pyq55yf5q2j5pKt5pS+77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgICAgICAgICB0aGlzLnBsYXlpbmcgPSBmYWxzZQ0KDQogICAgICAgICAgICAgICAgLy8g5YaN5qyh5bCd6K+V5pKt5pS+DQogICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdICYmIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5wbGF5KSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflho3mrKHlsJ3or5Xmkq3mlL7vvIxVSUQ6JywgdGhpcy5fdWlkKQ0KICAgICAgICAgICAgICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0ucGxheSgpDQoNCiAgICAgICAgICAgICAgICAgICAgLy8g5YaN5qyh5qOA5p+lDQogICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbmFsUGxheWluZyA9IGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5pc1BsYXlpbmcoKQ0KICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbmFsSGFzVGltZVVwZGF0ZSA9IChEYXRlLm5vdygpIC0gdGhpcy5sYXN0UGxheVRpbWVVcGRhdGUpIDwgMjAwMA0KDQogICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S6jOasoeWwneivleWQjuajgOafpTonLCB7DQogICAgICAgICAgICAgICAgICAgICAgICBpc1BsYXlpbmc6IGZpbmFsUGxheWluZywNCiAgICAgICAgICAgICAgICAgICAgICAgIGhhc1JlY2VudFRpbWVVcGRhdGU6IGZpbmFsSGFzVGltZVVwZGF0ZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHVpZDogdGhpcy5fdWlkDQogICAgICAgICAgICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMucGxheWluZyA9IGZpbmFsUGxheWluZyAmJiBmaW5hbEhhc1RpbWVVcGRhdGUNCiAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pyA57uI5pKt5pS+54q25oCBOicsIHRoaXMucGxheWluZywgJ1VJRDonLCB0aGlzLl91aWQpDQogICAgICAgICAgICAgICAgICAgIH0sIDUwMCkNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9LCAzMDApDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCflvLrliLbmkq3mlL7lpLHotKXvvIzmkq3mlL7lmajmnKrmkq3mlL7vvIxVSUQ6JywgdGhpcy5fdWlkKQ0KICAgICAgICAgICAgICAgIHRoaXMucGxheWluZyA9IGZhbHNlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sIDUwMCkgLy8g57uZ5pKt5pS+5Zmo5pu05aSa5pe26Ze05ZCv5YqoDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5a6e5L6L5LiN5a2Y5Zyo77yM5peg5rOV5by65Yi25pKt5pS+77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCflvLrliLbmkq3mlL7ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgIHRoaXMucGxheWluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIG11dGU6IGZ1bmN0aW9uKCkgew0KICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSkgew0KICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2V0Vm9pY2UoMC4wKQ0KICAgICAgICB0aGlzLmlzTm90TXV0ZSA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBjYW5jZWxNdXRlOiBmdW5jdGlvbigpIHsNCiAgICAgIGlmIChoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0pIHsNCiAgICAgICAgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnNldFZvaWNlKDEuMCkNCiAgICAgICAgdGhpcy5pc05vdE11dGUgPSB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBkZXN0cm95OiBmdW5jdGlvbigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vplIDmr4FoMjY1d2Vi5pKt5pS+5Zmo77yMVUlEOicsIHRoaXMuX3VpZCkNCg0KICAgICAgLy8g56uL5Y2z6YeN572u54q25oCB77yM6YG/5YWN5YW25LuW5pa55rOV57un57ut6LCD55So5pKt5pS+5ZmoDQogICAgICB0aGlzLnBsYXlpbmcgPSBmYWxzZQ0KICAgICAgdGhpcy5sb2FkZWQgPSBmYWxzZQ0KICAgICAgdGhpcy5wbGF5ZXJMb2FkaW5nID0gZmFsc2UNCiAgICAgIHRoaXMuZXJyID0gJycNCg0KICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmraPlnKjplIDmr4Hmkq3mlL7lmajlrp7kvovvvIxVSUQ6JywgdGhpcy5fdWlkKQ0KDQogICAgICAgICAgLy8g5YWI5pqC5YGc5pKt5pS+77yM6YG/5YWN5LqL5Lu255uR5ZCs5Zmo57un57ut6Kem5Y+RDQogICAgICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5wYXVzZSkgew0KICAgICAgICAgICAgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBhdXNlKCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDnq4vljbPph4rmlL7mkq3mlL7lmajotYTmupDvvIzkuI3kvb/nlKhzZXRUaW1lb3V0DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGlmIChoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0ucmVsZWFzZSkgew0KICAgICAgICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0ucmVsZWFzZSgpDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmkq3mlL7lmajotYTmupDlt7Lph4rmlL7vvIxVSUQ6JywgdGhpcy5fdWlkKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICBjb25zb2xlLndhcm4oJ+mHiuaUvuaSreaUvuWZqOi1hOa6kOaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDnq4vljbPmuIXnqbrmkq3mlL7lmajlvJXnlKgNCiAgICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0gPSBudWxsDQogICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqOW8leeUqOW3sua4heepuu+8jFVJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgICAgICAvLyDmuIXnkIZET03lrrnlmagNCiAgICAgICAgICB0aGlzLmNsZWFyUGxheWVyRE9NKCkNCg0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUud2Fybign6ZSA5q+B5pKt5pS+5Zmo5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSA9IG51bGwNCiAgICAgICAgICB0aGlzLmNsZWFyUGxheWVyRE9NKCkNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqOWunuS+i+S4jeWtmOWcqO+8jOebtOaOpea4heeQhkRPTe+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgICAgIHRoaXMuY2xlYXJQbGF5ZXJET00oKQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygnaDI2NXdlYuaSreaUvuWZqOmUgOavgeWujOaIkO+8jFVJRDonLCB0aGlzLl91aWQpDQogICAgfSwNCg0KICAgIGNsZWFyUGxheWVyRE9NOiBmdW5jdGlvbigpIHsNCiAgICAgIC8vIOa4heeQhkRPTeWuueWZqO+8jOenu+mZpOaJgOacieWtkOWFg+e0oOWSjOS6i+S7tuebkeWQrOWZqA0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMucGxheWVyQm94KSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+a4heeQhuaSreaUvuWZqERPTeWuueWZqO+8jFVJRDonLCB0aGlzLl91aWQpDQoNCiAgICAgICAgICAvLyDnp7vpmaTmiYDmnInlrZDlhYPntKANCiAgICAgICAgICB3aGlsZSAodGhpcy4kcmVmcy5wbGF5ZXJCb3guZmlyc3RDaGlsZCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5wbGF5ZXJCb3gucmVtb3ZlQ2hpbGQodGhpcy4kcmVmcy5wbGF5ZXJCb3guZmlyc3RDaGlsZCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDmuIXnkIblj6/og73nmoTlhoXogZTmoLflvI8NCiAgICAgICAgICB0aGlzLiRyZWZzLnBsYXllckJveC5zdHlsZS5jc3NUZXh0ID0gJycNCg0KICAgICAgICAgIC8vIOenu+mZpOWPr+iDveeahOS6i+S7tuebkeWQrOWZqA0KICAgICAgICAgIHRoaXMuJHJlZnMucGxheWVyQm94LmlubmVySFRNTCA9ICcnDQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5ZmoRE9N5a655Zmo5bey5riF55CG77yMVUlEOicsIHRoaXMuX3VpZCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCfmuIXnkIZET03lrrnlmajml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICBmdWxsc2NyZWVuU3dpY2g6IGZ1bmN0aW9uKCkgew0KICAgICAgY29uc3QgaXNGdWxsID0gdGhpcy5pc0Z1bGxzY3JlZW4oKQ0KICAgICAgaWYgKGlzRnVsbCkgew0KICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uY2xvc2VGdWxsU2NyZWVuKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5mdWxsU2NyZWVuKCkNCiAgICAgIH0NCiAgICAgIHRoaXMuZnVsbHNjcmVlbiA9ICFpc0Z1bGwNCiAgICB9LA0KICAgIGlzRnVsbHNjcmVlbjogZnVuY3Rpb24oKSB7DQogICAgICByZXR1cm4gZG9jdW1lbnQuZnVsbHNjcmVlbkVsZW1lbnQgfHwNCiAgICAgICAgZG9jdW1lbnQubXNGdWxsc2NyZWVuRWxlbWVudCB8fA0KICAgICAgICBkb2N1bWVudC5tb3pGdWxsU2NyZWVuRWxlbWVudCB8fA0KICAgICAgICBkb2N1bWVudC53ZWJraXRGdWxsc2NyZWVuRWxlbWVudCB8fCBmYWxzZQ0KICAgIH0sDQogICAgc2V0UGxheWJhY2tSYXRlOiBmdW5jdGlvbihzcGVlZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSAmJiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2V0UGxheWJhY2tSYXRlKSB7DQogICAgICAgICAgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnNldFBsYXliYWNrUmF0ZShzcGVlZCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCforr7nva7mkq3mlL7lgI3pgJ/ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICBzZWVrOiBmdW5jdGlvbihwdHMpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCdoMjY1d2Vi5pKt5pS+5Zmoc2Vla+aWueazleiiq+iwg+eUqO+8jOebruagh+aXtumXtDonLCBwdHMsICfnp5InKQ0KICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo54q25oCBOicsIHsNCiAgICAgICAgICBwbGF5ZXJFeGlzdHM6ICEhaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLA0KICAgICAgICAgIHNlZWtNZXRob2RFeGlzdHM6ICEhKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSAmJiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2VlayksDQogICAgICAgICAgcGxheWVyVWlkOiB0aGlzLl91aWQsDQogICAgICAgICAgbG9hZGVkOiB0aGlzLmxvYWRlZCwNCiAgICAgICAgICBwbGF5aW5nOiB0aGlzLnBsYXlpbmcNCiAgICAgICAgfSkNCg0KICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdICYmIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5zZWVrKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+aJp+ihjOaSreaUvuWZqHNlZWvmk43kvZzliLA6JywgcHRzLCAn56eSJykNCg0KICAgICAgICAgIC8vIOajgOafpXB0c+WAvOaYr+WQpuWQiOeQhg0KICAgICAgICAgIGlmIChwdHMgPCAwKSB7DQogICAgICAgICAgICBjb25zb2xlLndhcm4oJ3NlZWvml7bpl7TlsI/kuo4w77yM6LCD5pW05Li6MCcpDQogICAgICAgICAgICBwdHMgPSAwDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6K6w5b2V5b2T5YmN5pKt5pS+54q25oCBDQogICAgICAgICAgY29uc3Qgd2FzUGxheWluZ0JlZm9yZVNlZWsgPSB0aGlzLnBsYXlpbmcNCg0KICAgICAgICAgIC8vIOaJp+ihjHNlZWvmk43kvZwNCiAgICAgICAgICBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2VlayhwdHMpDQogICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqHNlZWvmk43kvZzlt7LmiafooYzvvIzkuYvliY3mkq3mlL7nirbmgIE6Jywgd2FzUGxheWluZ0JlZm9yZVNlZWspDQoNCiAgICAgICAgICAvLyDop6blj5FzZWVr5a6M5oiQ5LqL5Lu277yM6K6p54i257uE5Lu255+l6YGTc2Vla+aTjeS9nOW3suWujOaIkA0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgLy8g6YCa55+l54i257uE5Lu2c2Vla+aTjeS9nOWujOaIkA0KICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdzZWVrRmluaXNoJykNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2gyNjV3ZWLmkq3mlL7lmahzZWVr5pON5L2c5a6M5oiQJykNCg0KICAgICAgICAgICAgICAvLyDms6jph4rmjonmkq3mlL7lmajlsYLpnaLnmoToh6rliqjmkq3mlL7vvIznlLHniLbnu4Tku7bnu5/kuIDlpITnkIYNCiAgICAgICAgICAgICAgLy8g6YG/5YWN5LiO54i257uE5Lu255qE6Ieq5Yqo5pKt5pS+6YC76L6R5Yay56qBDQogICAgICAgICAgICAgIC8vIGlmICh3YXNQbGF5aW5nQmVmb3JlU2VlayAmJiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0gJiYgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLnBsYXkpIHsNCiAgICAgICAgICAgICAgLy8gICBjb25zb2xlLmxvZygn5bCd6K+V5oGi5aSN5pKt5pS+54q25oCBJykNCiAgICAgICAgICAgICAgLy8gICB0cnkgew0KICAgICAgICAgICAgICAvLyAgICAgY29uc3QgcGxheVByb21pc2UgPSBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0ucGxheSgpDQogICAgICAgICAgICAgIC8vICAgICBpZiAocGxheVByb21pc2UgJiYgdHlwZW9mIHBsYXlQcm9taXNlLmNhdGNoID09PSAnZnVuY3Rpb24nKSB7DQogICAgICAgICAgICAgIC8vICAgICAgIHBsYXlQcm9taXNlLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgICAgLy8gICAgICAgICBpZiAoZXJyb3IubmFtZSAhPT0gJ0Fib3J0RXJyb3InKSB7DQogICAgICAgICAgICAgIC8vICAgICAgICAgICBjb25zb2xlLndhcm4oJ3NlZWvlkI7mgaLlpI3mkq3mlL7ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgICAgICAgIC8vICAgICAgICAgfQ0KICAgICAgICAgICAgICAvLyAgICAgICB9KQ0KICAgICAgICAgICAgICAvLyAgICAgfQ0KICAgICAgICAgICAgICAvLyAgICAgdGhpcy5wbGF5aW5nID0gaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLmlzUGxheWluZygpDQogICAgICAgICAgICAgIC8vICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgLy8gICAgIGNvbnNvbGUud2Fybignc2Vla+WQjuaBouWkjeaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICAgICAgLy8gICB9DQogICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2gyNjV3ZWLmkq3mlL7lmahzZWVr5a6M5oiQ77yM5pKt5pS+54q25oCB55Sx54i257uE5Lu25o6n5Yi2JykNCiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign6Kem5Y+Rc2Vla+WujOaIkOS6i+S7tuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LCAyMDApIC8vIOWinuWKoOW7tui/n++8jOehruS/nXNlZWvmk43kvZzlrozlhajlrozmiJANCg0KICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmkq3mlL7lmajmnKrlh4blpIflpb3miJbkuI3mlK/mjIFzZWVr5pON5L2cJywgew0KICAgICAgICAgICAgcGxheWVyRXhpc3RzOiAhIWgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSwNCiAgICAgICAgICAgIHNlZWtNZXRob2RFeGlzdHM6ICEhKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSAmJiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2VlaykNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmkq3mlL7lmahzZWVr5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5re75Yqg6I635Y+W5b2T5YmN5pKt5pS+5pe26Ze055qE5pa55rOV77yI5aaC5p6c5pKt5pS+5Zmo5pSv5oyB55qE6K+d77yJDQogICAgZ2V0Q3VycmVudFRpbWU6IGZ1bmN0aW9uKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXSkgew0KICAgICAgICAgIC8vIOWwneivleS4jeWQjOeahOaXtumXtOiOt+WPluaWueazlQ0KICAgICAgICAgIGlmIChoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uZ2V0Q3VycmVudFRpbWUpIHsNCiAgICAgICAgICAgIHJldHVybiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uZ2V0Q3VycmVudFRpbWUoKQ0KICAgICAgICAgIH0gZWxzZSBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLmdldFRpbWUpIHsNCiAgICAgICAgICAgIHJldHVybiBoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uZ2V0VGltZSgpDQogICAgICAgICAgfSBlbHNlIGlmIChoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uY3VycmVudFRpbWUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgcmV0dXJuIGgyNjV3ZWJQbGF5ZXJbdGhpcy5fdWlkXS5jdXJyZW50VGltZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gbnVsbA0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCfojrflj5bmkq3mlL7lmajlvZPliY3ml7bpl7Tml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOiOt+WPluaSreaUvuWZqOeKtuaAgeeahOaWueazlQ0KICAgIGdldFBsYXllclN0YXR1czogZnVuY3Rpb24oKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAoaDI2NXdlYlBsYXllclt0aGlzLl91aWRdKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHBsYXlpbmc6IHRoaXMucGxheWluZywNCiAgICAgICAgICAgIGxvYWRlZDogdGhpcy5sb2FkZWQsDQogICAgICAgICAgICBwbGF5ZXJFeGlzdHM6IHRydWUsDQogICAgICAgICAgICBoYXNTZWVrTWV0aG9kOiAhIShoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uc2VlayksDQogICAgICAgICAgICBoYXNUaW1lTWV0aG9kOiAhIShoMjY1d2ViUGxheWVyW3RoaXMuX3VpZF0uZ2V0Q3VycmVudFRpbWUgfHwgaDI2NXdlYlBsYXllclt0aGlzLl91aWRdLmdldFRpbWUpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgcGxheWluZzogZmFsc2UsDQogICAgICAgICAgbG9hZGVkOiBmYWxzZSwNCiAgICAgICAgICBwbGF5ZXJFeGlzdHM6IGZhbHNlLA0KICAgICAgICAgIGhhc1NlZWtNZXRob2Q6IGZhbHNlLA0KICAgICAgICAgIGhhc1RpbWVNZXRob2Q6IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign6I635Y+W5pKt5pS+5Zmo54q25oCB5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgICByZXR1cm4gbnVsbA0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnoa7kv53lhajlsYDmi6bmiKrlmajmraPluLjlt6XkvZwNCiAgICBlbnN1cmVHbG9iYWxJbnRlcmNlcHRvckFjdGl2ZSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOajgOafpeWFqOWxgOaLpuaIquWZqOaYr+WQpuWtmOWcqOW5tuato+W4uOW3peS9nA0KICAgICAgICBpZiAod2luZG93LmgyNjV3ZWJJbnRlcmNlcHRvcikgew0KICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IHdpbmRvdy5oMjY1d2ViSW50ZXJjZXB0b3Iuc3RhdHVzKCkNCiAgICAgICAgICBpZiAoc3RhdHVzLmFjdGl2ZSkgew0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2coJ1toMjY1d2ViXSDlhajlsYDmi6bmiKrlmajmraPluLjlt6XkvZwnKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyBjb25zb2xlLndhcm4oJ1toMjY1d2ViXSDlhajlsYDmi6bmiKrlmajmnKrmv4DmtLvvvIzlsJ3or5Xph43mlrDlkK/liqgnKQ0KICAgICAgICAgICAgd2luZG93LmgyNjV3ZWJJbnRlcmNlcHRvci5zdGFydCgpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIGNvbnNvbGUud2FybignW2gyNjV3ZWJdIOWFqOWxgOaLpuaIquWZqOS4jeWtmOWcqO+8jOWPr+iDveacquato+ehruWKoOi9vScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIC8vIGNvbnNvbGUuZXJyb3IoJ1toMjY1d2ViXSDmo4Dmn6XlhajlsYDmi6bmiKrlmajnirbmgIHlpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCg0KDQogIH0NCn0NCg=="}, null]}