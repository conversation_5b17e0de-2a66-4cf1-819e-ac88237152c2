(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b3c5ace6"],{2655:function(e,i,t){"use strict";t.r(i);var n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("div",{ref:"container",staticStyle:{width:"100%",height:"100%","background-color":"#000000",margin:"0 auto",position:"relative"},on:{dblclick:e.fullscreenSwich}},[t("div",{staticStyle:{width:"100%","padding-top":"56.25%",position:"relative"}}),t("div",{staticClass:"buttons-box",attrs:{id:"buttonsBox"}},[t("div",{staticClass:"buttons-box-left"},[e.playing?e._e():t("i",{staticClass:"iconfont icon-play jessibuca-btn",on:{click:e.playBtnClick}}),e.playing?t("i",{staticClass:"iconfont icon-pause jessibuca-btn",on:{click:e.pause}}):e._e(),t("i",{staticClass:"iconfont icon-stop jessibuca-btn",on:{click:e.destroy}}),e.isNotMute?t("i",{staticClass:"iconfont icon-audio-high jessibuca-btn",on:{click:function(i){return e.mute()}}}):e._e(),e.isNotMute?e._e():t("i",{staticClass:"iconfont icon-audio-mute jessibuca-btn",on:{click:function(i){return e.cancelMute()}}})]),t("div",{staticClass:"buttons-box-right"},[t("span",{staticClass:"jessibuca-btn"},[e._v(e._s(e.kBps)+" kb/s")]),t("i",{staticClass:"iconfont icon-camera1196054easyiconnet jessibuca-btn",staticStyle:{"font-size":"1rem !important"},on:{click:e.screenshot}}),t("i",{staticClass:"iconfont icon-shuaxin11 jessibuca-btn",on:{click:e.playBtnClick}}),e.fullscreen?e._e():t("i",{staticClass:"iconfont icon-weibiaoti10 jessibuca-btn",on:{click:e.fullscreenSwich}}),e.fullscreen?t("i",{staticClass:"iconfont icon-weibiaoti11 jessibuca-btn",on:{click:e.fullscreenSwich}}):e._e()])])])},o=[],s=t("5530"),c={},u={name:"Jessibuca",props:["videoUrl","error","hasAudio","height"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1}},watch:{videoUrl:{handler:function(e,i){var t=this;this.$nextTick((function(){t.play(e)}))},immediate:!0}},created:function(){var e=this,i=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){e.updatePlayerDomSize(),window.onresize=e.updatePlayerDomSize,"undefined"===typeof e.videoUrl&&(e.videoUrl=i),e.btnDom=document.getElementById("buttonsBox")}))},mounted:function(){this.updatePlayerDomSize()},destroyed:function(){c[this._uid]&&c[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.performance=""},methods:{updatePlayerDomSize:function(){var e=this,i=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(i){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(i.parentNode));var t=i.parentNode.clientWidth,n=i.parentNode.clientHeight,o=t,s=9/16*o;n>0&&t>n/9*16&&(s=n,o=n/9*16);var u=Math.min(document.body.clientHeight,document.documentElement.clientHeight);s>u&&(s=u,o=16/9*s),this.playerWidth=o,this.playerHeight=s,this.playing&&c[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(){var e={container:this.$refs.container,autoWasm:!0,background:"",controlAutoHide:!1,debug:!1,decoder:"static/js/jessibuca/decoder.js",forceNoOffscreen:!1,hasAudio:"undefined"===typeof this.hasAudio||this.hasAudio,heartTimeout:5,heartTimeoutReplay:!0,heartTimeoutReplayTimes:3,hiddenAutoPause:!1,hotKey:!0,isFlv:!1,isFullResize:!1,isNotMute:this.isNotMute,isResize:!0,keepScreenOn:!0,loadingText:"请稍等, 视频加载中......",loadingTimeout:10,loadingTimeoutReplay:!0,loadingTimeoutReplayTimes:3,openWebglAlignment:!1,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},recordType:"mp4",rotate:0,showBandwidth:!1,supportDblclickFullscreen:!1,timeout:10,useMSE:!0,useWCS:!1,useWebFullScreen:!0,videoBuffer:.1,wasmDecodeErrorReplay:!0,wcsUseVideoRender:!0};console.log("Jessibuca -> options: ",e),c[this._uid]=new window.Jessibuca(Object(s["a"])({},e));var i=c[this._uid],t=this;i.on("pause",(function(){t.playing=!1})),i.on("play",(function(){t.playing=!0})),i.on("fullscreen",(function(e){t.fullscreen=e})),i.on("mute",(function(e){t.isNotMute=!e})),i.on("performance",(function(e){var i="卡顿";2===e?i="非常流畅":1===e&&(i="流畅"),t.performance=i})),i.on("kBps",(function(e){t.kBps=Math.round(e)})),i.on("videoInfo",(function(e){console.log("Jessibuca -> videoInfo: ",e)})),i.on("audioInfo",(function(e){console.log("Jessibuca -> audioInfo: ",e)})),i.on("error",(function(e){console.log("Jessibuca -> error: ",e)})),i.on("timeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),i.on("loadingTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),i.on("delayTimeout",(function(e){console.log("Jessibuca -> timeout: ",e)})),i.on("playToRenderTimes",(function(e){console.log("Jessibuca -> playToRenderTimes: ",e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var i=this;console.log("Jessibuca -> url: ",e),c[this._uid]&&this.destroy(),this.create(),c[this._uid].on("play",(function(){i.playing=!0,i.loaded=!0,i.quieting=jessibuca.quieting})),c[this._uid].hasLoaded()?c[this._uid].play(e):c[this._uid].on("load",(function(){c[i._uid].play(e)}))},pause:function(){c[this._uid]&&c[this._uid].pause(),this.playing=!1,this.err="",this.performance=""},screenshot:function(){c[this._uid]&&c[this._uid].screenshot()},mute:function(){c[this._uid]&&c[this._uid].mute()},cancelMute:function(){c[this._uid]&&c[this._uid].cancelMute()},destroy:function(){c[this._uid]&&c[this._uid].destroy(),null==document.getElementById("buttonsBox")&&this.$refs.container.appendChild(this.btnDom),c[this._uid]=null,this.playing=!1,this.err="",this.performance=""},fullscreenSwich:function(){var e=this.isFullscreen();c[this._uid].setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}}},a=u,l=(t("989a"),t("2877")),r=Object(l["a"])(a,n,o,!1,null,null,null);i["default"]=r.exports},"953f":function(e,i,t){},"989a":function(e,i,t){"use strict";t("953f")}}]);