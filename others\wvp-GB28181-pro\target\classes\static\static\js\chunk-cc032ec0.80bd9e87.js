(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cc032ec0"],{3779:function(e,r,t){"use strict";t.r(r);var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container",staticStyle:{height:"calc(100vh - 124px)"},attrs:{id:"mediaServerManger"}},[t("el-form",{staticStyle:{"margin-bottom":"1rem"},attrs:{inline:!0,size:"mini"}},[t("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.add}},[e._v("添加节点")])],1),t("el-row",{attrs:{gutter:12}},e._l(e.mediaServerList,(function(r){return t("el-col",{key:r.id,attrs:{span:e.getNumberByWidth()}},[t("el-card",{staticClass:"server-card",attrs:{shadow:"hover","body-style":{padding:"0px"}}},["zlm"===r.type?t("div",{staticClass:"card-img-zlm"}):e._e(),"abl"===r.type?t("div",{staticClass:"card-img-abl"}):e._e(),t("div",{staticStyle:{padding:"14px","text-align":"left"}},[t("span",{staticStyle:{"font-size":"16px"}},[e._v(e._s(r.id))]),r.defaultServer?e._e():t("el-button",{staticStyle:{padding:"0",float:"right"},attrs:{icon:"el-icon-edit",type:"text"},on:{click:function(t){return e.edit(r)}}},[e._v("编辑")]),r.defaultServer?t("el-button",{staticStyle:{padding:"0",float:"right"},attrs:{icon:"el-icon-edit",type:"text"},on:{click:function(t){return e.edit(r)}}},[e._v("查看")]):e._e(),r.defaultServer?e._e():t("el-button",{staticStyle:{"margin-right":"10px",padding:"0",float:"right"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(t){return e.del(r)}}},[e._v("移除")]),t("div",{staticStyle:{"margin-top":"13px","line-height":"12px"}},[t("span",{staticStyle:{"font-size":"14px",color:"#999","margin-top":"5px"}},[e._v(e._s(r.ip))]),t("span",{staticStyle:{"font-size":"14px",color:"#999","margin-top":"5px",float:"right"}},[e._v(e._s(r.createTime))])])],1),r.status?t("i",{staticClass:"iconfont icon-online server-card-status-online",attrs:{title:"在线"}}):e._e(),r.status?e._e():t("i",{staticClass:"iconfont icon-online server-card-status-offline",attrs:{title:"离线"}}),r.defaultServer?t("i",{staticClass:"server-card-default"},[e._v("默认")]):e._e()])],1)})),1),t("mediaServerEdit",{ref:"mediaServerEdit"})],1)},i=[],o=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"mediaServerEdit"}},[t("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"媒体节点",width:e.dialogWidth,top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(r){e.showDialog=r},close:function(r){return e.close()}}},[t("div",{staticStyle:{"margin-top":"1rem","margin-right":"20px"},attrs:{id:"formStep"}},[1===e.currentStep?t("el-form",{ref:"mediaServerForm",attrs:{rules:e.rules,model:e.mediaServerForm,"label-width":"140px"}},[t("el-form-item",{attrs:{label:"IP",prop:"ip"}},[t("el-input",{attrs:{placeholder:"媒体服务IP",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.ip,callback:function(r){e.$set(e.mediaServerForm,"ip",r)},expression:"mediaServerForm.ip"}})],1),t("el-form-item",{attrs:{label:"HTTP端口",prop:"httpPort"}},[t("el-input",{attrs:{placeholder:"媒体服务HTTP端口",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.httpPort,callback:function(r){e.$set(e.mediaServerForm,"httpPort",r)},expression:"mediaServerForm.httpPort"}})],1),t("el-form-item",{attrs:{label:"SECRET",prop:"secret"}},[t("el-input",{attrs:{placeholder:"媒体服务SECRET",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.secret,callback:function(r){e.$set(e.mediaServerForm,"secret",r)},expression:"mediaServerForm.secret"}})],1),t("el-form-item",{attrs:{label:"类型",prop:"type"}},[t("el-select",{staticStyle:{float:"left",width:"100%"},model:{value:e.mediaServerForm.type,callback:function(r){e.$set(e.mediaServerForm,"type",r)},expression:"mediaServerForm.type"}},[t("el-option",{key:"zlm",attrs:{label:"ZLMediaKit",value:"zlm"}}),t("el-option",{key:"abl",attrs:{label:"ABLMediaServer",value:"abl"}})],1)],1),t("el-form-item",[t("div",{staticStyle:{float:"right"}},[1===e.currentStep&&1===e.serverCheck?t("el-button",{attrs:{type:"primary"},on:{click:e.next}},[e._v("下一步")]):e._e(),t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.checkServer}},[e._v("测试")]),1===e.serverCheck?t("i",{staticClass:"el-icon-success",staticStyle:{color:"#3caf36"}}):e._e(),-1===e.serverCheck?t("i",{staticClass:"el-icon-error",staticStyle:{color:"#c80000"}}):e._e()],1)])],1):e._e(),t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[2===e.currentStep||3===e.currentStep?t("el-form",{ref:"mediaServerForm1",attrs:{rules:e.rules,model:e.mediaServerForm,"label-width":"140px"}},[t("el-form-item",{attrs:{label:"IP",prop:"ip"}},[2===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.ip,callback:function(r){e.$set(e.mediaServerForm,"ip",r)},expression:"mediaServerForm.ip"}}):e._e(),3===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.ip,callback:function(r){e.$set(e.mediaServerForm,"ip",r)},expression:"mediaServerForm.ip"}}):e._e()],1),t("el-form-item",{attrs:{label:"HTTP端口",prop:"httpPort"}},[2===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.httpPort,callback:function(r){e.$set(e.mediaServerForm,"httpPort",r)},expression:"mediaServerForm.httpPort"}}):e._e(),3===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.httpPort,callback:function(r){e.$set(e.mediaServerForm,"httpPort",r)},expression:"mediaServerForm.httpPort"}}):e._e()],1),t("el-form-item",{attrs:{label:"HOOK IP",prop:"ip"}},[t("el-input",{attrs:{placeholder:"媒体服务HOOK_IP",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.hookIp,callback:function(r){e.$set(e.mediaServerForm,"hookIp",r)},expression:"mediaServerForm.hookIp"}})],1),t("el-form-item",{attrs:{label:"SDP IP",prop:"ip"}},[t("el-input",{attrs:{placeholder:"媒体服务SDP_IP",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.sdpIp,callback:function(r){e.$set(e.mediaServerForm,"sdpIp",r)},expression:"mediaServerForm.sdpIp"}})],1),t("el-form-item",{attrs:{label:"流IP",prop:"ip"}},[t("el-input",{attrs:{placeholder:"媒体服务流IP",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.streamIp,callback:function(r){e.$set(e.mediaServerForm,"streamIp",r)},expression:"mediaServerForm.streamIp"}})],1),t("el-form-item",{attrs:{label:"HTTPS PORT",prop:"httpSSlPort"}},[t("el-input",{attrs:{placeholder:"媒体服务HTTPS_PORT",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.httpSSlPort,callback:function(r){e.$set(e.mediaServerForm,"httpSSlPort",r)},expression:"mediaServerForm.httpSSlPort"}})],1),t("el-form-item",{attrs:{label:"RTSP PORT",prop:"rtspPort"}},[t("el-input",{attrs:{placeholder:"媒体服务RTSP_PORT",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.rtspPort,callback:function(r){e.$set(e.mediaServerForm,"rtspPort",r)},expression:"mediaServerForm.rtspPort"}})],1),t("el-form-item",{attrs:{label:"RTSPS PORT",prop:"rtspSSLPort"}},[t("el-input",{attrs:{placeholder:"媒体服务RTSPS_PORT",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.rtspSSLPort,callback:function(r){e.$set(e.mediaServerForm,"rtspSSLPort",r)},expression:"mediaServerForm.rtspSSLPort"}})],1)],1):e._e()],1),t("el-col",{attrs:{span:12}},[2===e.currentStep||3===e.currentStep?t("el-form",{ref:"mediaServerForm2",attrs:{rules:e.rules,model:e.mediaServerForm,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"RTMP PORT",prop:"rtmpPort"}},[t("el-input",{attrs:{placeholder:"媒体服务RTMP_PORT",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.rtmpPort,callback:function(r){e.$set(e.mediaServerForm,"rtmpPort",r)},expression:"mediaServerForm.rtmpPort"}})],1),t("el-form-item",{attrs:{label:"RTMPS PORT",prop:"rtmpSSlPort"}},[t("el-input",{attrs:{placeholder:"媒体服务RTMPS_PORT",clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.rtmpSSlPort,callback:function(r){e.$set(e.mediaServerForm,"rtmpSSlPort",r)},expression:"mediaServerForm.rtmpSSlPort"}})],1),t("el-form-item",{attrs:{label:"SECRET",prop:"secret"}},[2===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.secret,callback:function(r){e.$set(e.mediaServerForm,"secret",r)},expression:"mediaServerForm.secret"}}):e._e(),3===e.currentStep?t("el-input",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.secret,callback:function(r){e.$set(e.mediaServerForm,"secret",r)},expression:"mediaServerForm.secret"}}):e._e()],1),t("el-form-item",{attrs:{label:"自动配置媒体服务"}},[t("el-switch",{attrs:{disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.autoConfig,callback:function(r){e.$set(e.mediaServerForm,"autoConfig",r)},expression:"mediaServerForm.autoConfig"}})],1),t("el-form-item",{attrs:{label:"收流端口模式"}},[t("el-switch",{attrs:{"active-text":"多端口","inactive-text":"单端口",disabled:e.mediaServerForm.defaultServer},on:{change:e.portRangeChange},model:{value:e.mediaServerForm.rtpEnable,callback:function(r){e.$set(e.mediaServerForm,"rtpEnable",r)},expression:"mediaServerForm.rtpEnable"}})],1),e.mediaServerForm.rtpEnable?e._e():t("el-form-item",{attrs:{label:"收流端口",prop:"rtpProxyPort"}},[t("el-input",{attrs:{clearable:"",disabled:e.mediaServerForm.defaultServer},model:{value:e.mediaServerForm.rtpProxyPort,callback:function(r){e.$set(e.mediaServerForm,"rtpProxyPort",e._n(r))},expression:"mediaServerForm.rtpProxyPort"}})],1),e.mediaServerForm.rtpEnable?t("el-form-item",{attrs:{label:"收流端口"}},[t("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"起始",clearable:"",prop:"rtpPortRange1",disabled:e.mediaServerForm.defaultServer},on:{change:e.portRangeChange},model:{value:e.rtpPortRange1,callback:function(r){e.rtpPortRange1=r},expression:"rtpPortRange1"}}),e._v(" - "),t("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"终止",clearable:"",prop:"rtpPortRange2",disabled:e.mediaServerForm.defaultServer},on:{change:e.portRangeChange},model:{value:e.rtpPortRange2,callback:function(r){e.rtpPortRange2=r},expression:"rtpPortRange2"}})],1):e._e(),e.mediaServerForm.sendRtpEnable?t("el-form-item",{attrs:{label:"发流端口"}},[t("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"起始",clearable:"",prop:"rtpPortRange1",disabled:e.mediaServerForm.defaultServer},on:{change:e.portRangeChange},model:{value:e.sendRtpPortRange1,callback:function(r){e.sendRtpPortRange1=r},expression:"sendRtpPortRange1"}}),e._v(" - "),t("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"终止",clearable:"",prop:"rtpPortRange2",disabled:e.mediaServerForm.defaultServer},on:{change:e.portRangeChange},model:{value:e.sendRtpPortRange2,callback:function(r){e.sendRtpPortRange2=r},expression:"sendRtpPortRange2"}})],1):e._e(),t("el-form-item",[t("div",{staticStyle:{float:"right"}},[e.mediaServerForm.defaultServer?e._e():t("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("提交")]),e.mediaServerForm.defaultServer?e._e():t("el-button",{on:{click:e.close}},[e._v("取消")]),e.mediaServerForm.defaultServer?t("el-button",{on:{click:e.close}},[e._v("关闭")]):e._e()],1)])],1):e._e()],1)],1)],1)])],1)},l=[],s=(t("ac1f"),t("00b4"),t("a888")),n={name:"MediaServerEdit",directives:{elDragDialog:s["a"]},props:{},data:function(){var e=this,r=function(r,t,a){var i=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;return console.log(e.mediaServerForm.ip),i.test(e.mediaServerForm.ip)?(a(),!0):a(new Error("请输入有效的IP地址"))},t=function(r,t,a){var i=/^(([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]))$/;return i.test(e.mediaServerForm.httpPort)?(a(),!0):a(new Error("请输入有效的端口号"))};return{dialogWidth:0,defaultWidth:1e3,listChangeCallback:null,showDialog:!1,isLoging:!1,dialogLoading:!1,currentStep:1,platformList:[],serverCheck:0,recordServerCheck:0,mediaServerForm:{id:"",ip:"",autoConfig:!0,hookIp:"",sdpIp:"",streamIp:"",secret:"",httpPort:"",httpSSlPort:"",recordAssistPort:"",rtmpPort:"",rtmpSSlPort:"",rtpEnable:!1,rtpPortRange:"",sendRtpPortRange:"",rtpProxyPort:"",rtspPort:"",rtspSSLPort:"",type:"zlm"},rtpPortRange1:3e4,rtpPortRange2:30500,sendRtpPortRange1:5e4,sendRtpPortRange2:6e4,rules:{ip:[{required:!0,validator:r,message:"请输入有效的IP地址",trigger:"blur"}],httpPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],httpSSlPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],recordAssistPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtmpPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtmpSSlPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtpPortRange1:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtpPortRange2:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtpProxyPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtspPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],rtspSSLPort:[{required:!0,validator:t,message:"请输入有效的端口号",trigger:"blur"}],secret:[{required:!0,message:"请输入secret",trigger:"blur"}],timeout_ms:[{required:!0,message:"请输入FFmpeg推流成功超时时间",trigger:"blur"}],ffmpeg_cmd_key:[{required:!1,message:"请输入FFmpeg命令参数模板（可选）",trigger:"blur"}]}}},computed:{},created:function(){this.setDialogWidth()},methods:{setDialogWidth:function(){var e=document.body.clientWidth;e<this.defaultWidth?this.dialogWidth="100%":this.dialogWidth=this.defaultWidth+"px"},openDialog:function(e,r){if(this.showDialog=!0,this.listChangeCallback=r,null!=e&&(this.mediaServerForm=e,this.currentStep=3,e.rtpPortRange)){var t=this.mediaServerForm.rtpPortRange.split(","),a=this.mediaServerForm.sendRtpPortRange.split(",");t.length>0&&(this.rtpPortRange1=t[0],this.rtpPortRange2=t[1]),a.length>0&&(this.sendRtpPortRange1=a[0],this.sendRtpPortRange2=a[1])}},checkServer:function(){var e=this;this.serverCheck=0,this.$store.dispatch("server/checkMediaServer",this.mediaServerForm).then((function(r){parseInt(e.mediaServerForm.httpPort)!==parseInt(r.httpPort)&&e.$message({showClose:!0,message:"如果你正在使用docker部署你的媒体服务，请注意的端口映射。",type:"warning",duration:0});var t=e.mediaServerForm.httpPort;e.mediaServerForm=r,e.mediaServerForm.httpPort=t,e.mediaServerForm.autoConfig=!0,e.rtpPortRange1=3e4,e.rtpPortRange2=30500,e.sendRtpPortRange1=5e4,e.sendRtpPortRange2=6e4,e.serverCheck=1}))},next:function(){this.currentStep=2,this.defaultWidth=900,this.setDialogWidth()},onSubmit:function(){var e=this;this.dialogLoading=!0,this.$store.dispatch("server/saveMediaServer",this.mediaServerForm).then((function(r){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.listChangeCallback&&e.listChangeCallback(),e.close()}))},close:function(){this.showDialog=!1,this.dialogLoading=!1,this.mediaServerForm={id:"",ip:"",autoConfig:!0,hookIp:"",sdpIp:"",streamIp:"",secret:"",httpPort:"",httpSSlPort:"",recordAssistPort:"",rtmpPort:"",rtmpSSlPort:"",rtpEnable:!1,rtpPortRange:"",sendRtpPortRange:"",rtpProxyPort:"",rtspPort:"",rtspSSLPort:""},this.rtpPortRange1=30500,this.rtpPortRange2=30500,this.sendRtpPortRange1=5e4,this.sendRtpPortRange2=6e4,this.listChangeCallback=null,this.currentStep=1},portRangeChange:function(){this.mediaServerForm.rtpEnable&&(this.mediaServerForm.rtpPortRange=this.rtpPortRange1+","+this.rtpPortRange2,this.mediaServerForm.sendRtpPortRange=this.sendRtpPortRange1+","+this.sendRtpPortRange2)}}},d=n,m=t("2877"),c=Object(m["a"])(d,o,l,!1,null,null,null),p=c.exports,u={name:"MediaServer",components:{mediaServerEdit:p},data:function(){return{mediaServerList:[],updateLooper:!1,currentPage:1,count:15,total:0}},computed:{},mounted:function(){this.initData()},destroyed:function(){},methods:{initData:function(){this.getServerList()},currentChange:function(e){this.currentPage=e,this.getServerList()},handleSizeChange:function(e){this.count=e,this.getServerList()},getServerList:function(){var e=this;this.$store.dispatch("server/getMediaServerList").then((function(r){e.mediaServerList=r}))},add:function(){this.$refs.mediaServerEdit.openDialog(null,this.initData)},edit:function(e){this.$refs.mediaServerEdit.openDialog(e,this.initData)},del:function(e){var r=this;this.$confirm("确认删除此节点？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.$store.dispatch("server/deleteMediaServer",e.id).then((function(e){r.$message({type:"success",message:"删除成功!"})}))})).catch((function(){}))},getNumberByWidth:function(){for(var e=[1,2,3,4,6,8,12,24],r=window.innerWidth-30,t=20,a=360,i=(r+t)/(a+t),o=Math.ceil(24/i),l=24,s=0;s<e.length;s++){var n=e[s];if(s+1>=e.length)return 24;if(n<=o&&e[s+1]>o)return n}return l}}},v=u,S=(t("65d6"),Object(m["a"])(v,a,i,!1,null,null,null));r["default"]=S.exports},"65d6":function(e,r,t){"use strict";t("f859")},a888:function(e,r,t){"use strict";t("99af"),t("caad"),t("ac1f"),t("2532"),t("5319");var a={bind:function(e,r,t){var a=e.querySelector(".el-dialog__header"),i=e.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",i.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(e,r){return e.currentStyle[r]}:function(e,r){return getComputedStyle(e,!1)[r]}}();a.onmousedown=function(e){var r=e.clientX-a.offsetLeft,l=e.clientY-a.offsetTop,s=i.offsetWidth,n=i.offsetHeight,d=document.body.clientWidth,m=document.body.clientHeight,c=i.offsetLeft,p=d-i.offsetLeft-s,u=i.offsetTop,v=m-i.offsetTop-n,S=o(i,"left"),g=o(i,"top");S.includes("%")?(S=+document.body.clientWidth*(+S.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(S=+S.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var a=e.clientX-r,o=e.clientY-l;-a>c?a=-c:a>p&&(a=p),-o>u?o=-u:o>v&&(o=v),i.style.cssText+=";left:".concat(a+S,"px;top:").concat(o+g,"px;"),t.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}},i=function(e){e.directive("el-drag-dialog",a)};window.Vue&&(window["el-drag-dialog"]=a,Vue.use(i)),a.install=i;r["a"]=a},f859:function(e,r,t){}}]);