0 verbose cli C:\Driver-D\tools\nodejs\node.exe C:\Driver-D\tools\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.15.0
3 silly config load:file:C:\Driver-D\tools\nodejs\node_modules\npm\npmrc
4 silly config load:file:C:\Driver-E\工作\代码文件\others\wvp-GB28181-pro\web\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Driver-E\工作\代码文件\nodejs\node_global\etc\npmrc
7 verbose title npm cache clean
8 verbose argv "cache" "clean" "--force"
9 verbose logfile logs-max:10 dir:C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T14_25_19_562Z-
10 verbose logfile C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T14_25_19_562Z-debug-0.log
11 warn using --force Recommended protections disabled.
12 silly logfile start cleaning logs, removing 1 files
13 silly logfile done cleaning log files
14 verbose cwd C:\Driver-E\工作\代码文件\others\wvp-GB28181-pro\web
15 verbose os Windows_NT 10.0.26100
16 verbose node v22.15.0
17 verbose npm  v10.9.2
18 verbose exit 0
19 info ok
