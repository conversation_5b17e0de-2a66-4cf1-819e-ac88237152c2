(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-53584e02"],{2659:function(e,t,a){},"436b":function(e,t,a){},"8d79":function(e,t,a){"use strict";a("c585")},9751:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container",attrs:{id:"pushList"}},[e.streamPush?e._e():a("div",{staticStyle:{height:"calc(100vh - 124px)",display:"flex","flex-direction":"column"}},[a("el-form",{attrs:{inline:!0,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索"}},[a("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getPushList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),a("el-form-item",{attrs:{label:"流媒体"}},[a("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getPushList},model:{value:e.mediaServerId,callback:function(t){e.mediaServerId=t},expression:"mediaServerId"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(e.mediaServerList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"推流状态"}},[a("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getPushList},model:{value:e.pushing,callback:function(t){e.pushing=t},expression:"pushing"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"推流中",value:"true"}}),a("el-option",{attrs:{label:"已停止",value:"false"}})],1)],1),a("el-form-item",[a("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",type:"primary"},on:{click:e.addStream}},[e._v("添加 ")]),a("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-upload2"},on:{click:e.importChannel}},[e._v(" 通道导入 ")]),a("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-download"}},[a("a",{staticStyle:{"text-align":"center","text-decoration":"none"},attrs:{href:"/static/file/推流通道导入.zip",download:"推流通道导入.zip"}},[e._v("下载模板")])]),a("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-delete",disabled:0===e.multipleSelection.length,type:"danger"},on:{click:e.batchDel}},[e._v("移除 ")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.refresh()}}})],1)],1),a("div",{staticStyle:{flex:"1",overflow:"auto","min-height":"0","margin-bottom":"0"}},[a("el-table",{ref:"pushListTable",staticStyle:{width:"100%"},attrs:{size:"small",data:e.pushList,height:"100%",loading:e.loading,"row-key":function(e){return e.app+e.stream}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection","reserve-selection":!0,"min-width":"55"}}),a("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"150"}}),a("el-table-column",{attrs:{prop:"app",label:"应用名","min-width":"100"}}),a("el-table-column",{attrs:{prop:"stream",label:"流ID","min-width":"200"}}),a("el-table-column",{attrs:{label:"推流状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.pushing&&e.$myServerId!==t.row.serverId?a("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("推流中")]):e._e(),t.row.pushing&&e.$myServerId===t.row.serverId?a("el-tag",{attrs:{size:"medium"}},[e._v("推流中")]):e._e(),t.row.pushing?e._e():a("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("已停止")])]}}],null,!1,2164937881)}),a("el-table-column",{attrs:{prop:"gbDeviceId",label:"国标编码","min-width":"200"}}),a("el-table-column",{attrs:{label:"位置信息","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.gbLongitude&&t.row.gbLatitude?a("span",{attrs:{size:"medium"}},[e._v(e._s(t.row.gbLongitude)),a("br"),e._v(e._s(t.row.gbLatitude))]):e._e(),t.row.gbLongitude&&t.row.gbLatitude?e._e():a("span",{attrs:{size:"medium"}},[e._v("无")])]}}],null,!1,2365924002)}),a("el-table-column",{attrs:{prop:"mediaServerId",label:"流媒体","min-width":"150"}}),a("el-table-column",{attrs:{label:"开始时间","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[e._v(" "+e._s(null==t.row.pushTime?"-":t.row.pushTime)+" ")])]}}],null,!1,1401471641)}),a("el-table-column",{attrs:{label:"操作","min-width":"300",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"medium",loading:t.row.playLoading,icon:"el-icon-video-play",type:"text"},on:{click:function(a){return e.playPush(t.row)}}},[e._v("播放 ")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(a){return e.deletePush(t.row.id)}}},[e._v("删除")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{attrs:{size:"medium",icon:"el-icon-position",type:"text"},on:{click:function(a){return e.edit(t.row)}}},[e._v(" 编辑 ")]),a("el-button",{attrs:{size:"medium",icon:"el-icon-cloudy",type:"text"},on:{click:function(a){return e.queryCloudRecords(t.row)}}},[e._v("云端录像 ")])]}}],null,!1,1366293965)})],1)],1),a("el-pagination",{staticStyle:{"text-align":"right",height:"15px","line-height":"22px"},attrs:{size:"mini","current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),a("devicePlayer",{ref:"devicePlayer"}),a("addStreamTOGB",{ref:"addStreamTOGB"}),a("importChannel",{ref:"importChannel"}),e.streamPush?a("stream-push-edit",{staticStyle:{height:"calc(100vh - 90px)"},attrs:{"stream-push":e.streamPush,"close-edit":e.closeEdit}}):e._e()],1)},s=[],l=(a("99af"),a("d3b7"),a("0643"),a("4e3e"),a("159b"),a("0328")),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"addStreamProxy"}},[a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:" 加入",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[a("div",{staticStyle:{"margin-top":"1rem","margin-right":"100px"},attrs:{id:"shared"}},[a("el-form",{ref:"streamProxy",attrs:{rules:e.rules,model:e.proxyParam,"label-width":"140px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{clearable:""},model:{value:e.proxyParam.name,callback:function(t){e.$set(e.proxyParam,"name",t)},expression:"proxyParam.name"}})],1),a("el-form-item",{attrs:{label:"流应用名",prop:"app"}},[a("el-input",{attrs:{clearable:"",disabled:e.edit},model:{value:e.proxyParam.app,callback:function(t){e.$set(e.proxyParam,"app",t)},expression:"proxyParam.app"}})],1),a("el-form-item",{attrs:{label:"流ID",prop:"stream"}},[a("el-input",{attrs:{clearable:"",disabled:e.edit},model:{value:e.proxyParam.stream,callback:function(t){e.$set(e.proxyParam,"stream",t)},expression:"proxyParam.stream"}})],1),a("el-form-item",{attrs:{label:"国标编码",prop:"gbId"}},[a("el-input",{attrs:{placeholder:"设置国标编码可推送到国标",clearable:""},model:{value:e.proxyParam.gbId,callback:function(t){e.$set(e.proxyParam,"gbId",t)},expression:"proxyParam.gbId"}})],1),e.proxyParam.gbId?a("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[a("el-input",{attrs:{placeholder:"经度",clearable:""},model:{value:e.proxyParam.longitude,callback:function(t){e.$set(e.proxyParam,"longitude",t)},expression:"proxyParam.longitude"}})],1):e._e(),e.proxyParam.gbId?a("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[a("el-input",{attrs:{placeholder:"经度",clearable:""},model:{value:e.proxyParam.latitude,callback:function(t){e.$set(e.proxyParam,"latitude",t)},expression:"proxyParam.latitude"}})],1):e._e(),a("el-form-item",[a("div",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),a("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)},o=[],n=a("a888"),c={name:"PushStreamEdit",directives:{elDragDialog:n["a"]},props:{},data:function(){return{listChangeCallback:null,showDialog:!1,isLoging:!1,edit:!1,proxyParam:{name:null,app:null,stream:null,gbId:null,longitude:null,latitude:null},rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],app:[{required:!0,message:"请输入应用名",trigger:"blur"}],stream:[{required:!0,message:"请输入流ID",trigger:"blur"}],gbId:[{required:!0,message:"请输入国标编码",trigger:"blur"}]}}},computed:{},created:function(){},methods:{openDialog:function(e,t){this.showDialog=!0,this.listChangeCallback=t,null!=e?(this.proxyParam=e,this.edit=!0):(this.proxyParam={name:null,app:null,stream:null,gbId:null,longitude:null,latitude:null},this.edit=!1)},onSubmit:function(){var e=this;console.log("onSubmit"),this.edit?this.$store.dispatch("streamPush/saveToGb",this.proxyParam).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.showDialog=!1,null!=e.listChangeCallback&&e.listChangeCallback()})):this.$store.dispatch("streamPush/add",this.proxyParam).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.showDialog=!1,null!=e.listChangeCallback&&e.listChangeCallback()}))},close:function(){console.log("关闭加入GB"),this.showDialog=!1,this.$refs.streamProxy.resetFields()}}},u=c,d=a("2877"),m=Object(d["a"])(u,r,o,!1,null,null,null),h=m.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"importChannel"}},[a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"导入通道数据",width:"30rem",top:"2rem","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[a("div",[a("el-upload",{staticClass:"upload-box",attrs:{drag:"",action:e.uploadUrl,name:"file",headers:e.headers,"on-success":e.successHook,"on-error":e.errorHook}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传 csv / xls / xlsx 文件")])])],1)]),a("ShowErrorData",{ref:"showErrorData",attrs:{"gb-ids":e.errorGBIds,streams:e.errorStreams}})],1)},g=[],f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"importChannelShowErrorData"}},[a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"导入通道数据成功，但数据存在重复",width:"30rem",top:"2rem","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[a("div",[e._v(" 重复国标ID: "),a("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard",value:e.gbIds.join(","),expression:"gbIds.join(',')"}],staticStyle:{float:"right"},attrs:{type:"primary",size:"mini",icon:"el-icon-document-copy",title:"点击拷贝"},on:{success:function(t){return e.$message({type:"success",message:"成功拷贝到粘贴板"})}}},[e._v("复制")]),a("ul",{staticClass:"errDataBox"},e._l(e.gbIds,(function(t){return a("li",[e._v(" "+e._s(t)+" ")])})),0)],1),a("div",[e._v(" 重复App/stream: "),a("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard",value:e.streams.join(","),expression:"streams.join(',')"}],staticStyle:{float:"right"},attrs:{type:"primary",size:"mini",icon:"el-icon-document-copy",title:"点击拷贝"},on:{success:function(t){return e.$message({type:"success",message:"成功拷贝到粘贴板"})}}},[e._v("复制")]),a("ul",{staticClass:"errDataBox"},e._l(e.streams,(function(t){return a("li",[e._v(" "+e._s(t)+" ")])})),0)],1)])],1)},b=[],v={name:"ImportChannelShowErrorData",directives:{elDragDialog:n["a"]},props:["gbIds","streams"],data:function(){return{isLoging:!1,showDialog:!1}},computed:{},created:function(){},methods:{openDialog:function(){this.showDialog=!0},close:function(){this.showDialog=!1}}},y=v,w=(a("cbae"),Object(d["a"])(y,f,b,!1,null,null,null)),P=w.exports,x={name:"ImportChannel",directives:{elDragDialog:n["a"]},components:{ShowErrorData:P},data:function(){return{submitCallback:null,showDialog:!1,isLoging:!1,isEdit:!1,errorStreams:[],errorGBIds:[],headers:{"access-token":this.$store.getters.token},uploadUrl:(window.baseUrl?window.baseUrl:"")+"/api/push/upload"}},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.submitCallback=e},close:function(){this.showDialog=!1},successHook:function(e,t,a){0===e.code?this.$message({showClose:!0,message:e.msg,type:"success"}):1===e.code?(this.errorGBIds=e.data.gbId,this.errorStreams=e.data.stream,console.log(this.$refs),console.log(this.$refs.showErrorData),this.$refs.showErrorData.openDialog()):this.$message({showClose:!0,message:e.msg,type:"error"})},errorHook:function(e,t,a){this.$message({showClose:!0,message:e,type:"error"})}}},S=x,_=(a("bea2"),Object(d["a"])(S,p,g,!1,null,null,null)),C=_.exports,k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%"},attrs:{id:"ChannelEdit"}},[a("div",{staticClass:"page-header"},[a("div",{staticClass:"page-title"},[a("el-page-header",{attrs:{content:"编辑推流信息"},on:{back:e.close}})],1)]),a("el-tabs",{staticStyle:{padding:"1rem 0"},attrs:{"tab-position":"top"}},[a("el-tab-pane",{staticStyle:{"background-color":"#FFFFFF",padding:"1rem"},attrs:{label:"推流信息编辑"}},[a("el-divider",{attrs:{"content-position":"center"}},[e._v("基础信息")]),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.locading,expression:"locading"}],ref:"streamPushForm",staticClass:"channel-form",attrs:{"status-icon":"","label-width":"160px"}},[a("el-form-item",{attrs:{label:"应用名"}},[a("el-input",{attrs:{placeholder:"请输入应用名"},model:{value:e.streamPush.app,callback:function(t){e.$set(e.streamPush,"app",t)},expression:"streamPush.app"}})],1),a("el-form-item",{attrs:{label:"流ID"}},[a("el-input",{attrs:{placeholder:"请输入流ID"},model:{value:e.streamPush.stream,callback:function(t){e.$set(e.streamPush,"stream",t)},expression:"streamPush.stream"}})],1)],1),a("el-divider",{attrs:{"content-position":"center"}},[e._v("策略")]),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.locading,expression:"locading"}],ref:"streamPushForm",attrs:{"status-icon":"","label-width":"160px"}},[a("el-form-item",{staticStyle:{"text-align":"left"}},[a("el-checkbox",{model:{value:e.streamPush.startOfflinePush,callback:function(t){e.$set(e.streamPush,"startOfflinePush",t)},expression:"streamPush.startOfflinePush"}},[e._v("拉起离线推流")])],1)],1),a("el-form",{staticStyle:{"text-align":"right"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),a("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)],1),e.streamPush.id?a("el-tab-pane",{attrs:{label:"国标通道配置"}},[a("CommonChannelEdit",{ref:"commonChannelEdit",attrs:{"data-form":e.streamPush,cancel:e.close}})],1):e._e()],1)],1)},D=[],L=a("7317"),$={name:"ChannelEdit",components:{CommonChannelEdit:L["a"]},props:["streamPush","closeEdit"],data:function(){return{locading:!1}},created:function(){console.log(this.streamPush)},methods:{onSubmit:function(){var e=this;console.log(this.streamPush),this.locading=!0,this.streamPush.id?this.$store.dispatch("streamPush/update",this.streamPush).then((function(t){e.$message.success({showClose:!0,message:"保存成功"})})).finally((function(){e.locading=!1})):this.$store.dispatch("streamPush/add",this.streamPush).then((function(t){e.$message.success({showClose:!0,message:"保存成功"})})).finally((function(){e.locading=!1}))},close:function(){this.closeEdit()}}},I=$,E=(a("e4a6"),Object(d["a"])(I,k,D,!1,null,null,null)),z=E.exports,B={name:"PushList",components:{StreamPushEdit:z,devicePlayer:l["a"],addStreamTOGB:h,importChannel:C},data:function(){return{pushList:[],currentPusher:{},updateLooper:0,currentDeviceChannelsLenth:0,currentPage:1,count:15,total:0,searchSrt:"",pushing:"",mediaServerId:"",mediaServerList:[],multipleSelection:[],loading:!1,streamPush:null}},mounted:function(){this.initData(),this.updateLooper=setInterval(this.getPushList,2e3)},destroyed:function(){clearTimeout(this.updateLooper)},methods:{initData:function(){var e=this;this.loading=!0,this.$store.dispatch("server/getMediaServerList").then((function(t){e.mediaServerList=t})),this.getPushList()},currentChange:function(e){this.currentPage=e,this.getPushList()},handleSizeChange:function(e){this.count=e,this.getPushList()},getPushList:function(){var e=this;this.$store.dispatch("streamPush/queryList",{page:this.currentPage,count:this.count,query:this.searchSrt,pushing:this.pushing,mediaServerId:this.mediaServerId}).then((function(t){e.total=t.total,e.pushList=t.list,e.pushList.forEach((function(t){e.$set(t,"location",""),e.$set(t,"playLoading",!1),t.gbLongitude&&t.gbLatitude&&e.$set(t,"location",t.gbLongitude+","+t.gbLatitude)}))})).finally((function(){e.loading=!1}))},playPush:function(e){var t=this;e.playLoading=!0,this.$store.dispatch("streamPush/play",e.id).then((function(e){t.$refs.devicePlayer.openDialog("streamPlay",null,null,{streamInfo:e,hasAudio:!0})})).finally((function(){e.playLoading=!1}))},deletePush:function(e){var t=this;this.$confirm("确定删除通道?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,t.$store.dispatch("streamPush/remove",e).then((function(e){t.initData()}))})).catch((function(){}))},edit:function(e){this.streamPush=e},closeEdit:function(){this.streamPush=null,this.getPushList()},queryCloudRecords:function(e){this.$router.push("/cloudRecord/detail/".concat(e.app,"/").concat(e.stream))},importChannel:function(){this.$refs.importChannel.openDialog((function(){}))},addStream:function(){this.streamPush={}},batchDel:function(){var e=this;this.$confirm("确定删除选中的".concat(this.multipleSelection.length,"个通道?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){for(var t=[],a=0;a<e.multipleSelection.length;a++)t.push(e.multipleSelection[a].id);e.$store.dispatch("streamPush/batchRemove",t).then((function(t){e.initData(),e.$refs.pushListTable.clearSelection()}))})).catch((function(){}))},handleSelectionChange:function(e){this.multipleSelection=e},refresh:function(){this.initData()}}},T=B,N=(a("8d79"),Object(d["a"])(T,i,s,!1,null,null,null));t["default"]=N.exports},bea2:function(e,t,a){"use strict";a("e4e2")},c585:function(e,t,a){},cbae:function(e,t,a){"use strict";a("436b")},e4a6:function(e,t,a){"use strict";a("2659")},e4e2:function(e,t,a){}}]);