(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-baa1c8c2"],{"165c":function(e,t,l){"use strict";var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"生成国标编码",width:"65rem",top:"2rem",center:"","append-to-body":!0,"close-on-click-modal":!1,visible:e.showVideoDialog,"destroy-on-close":!1},on:{"update:visible":function(t){e.showVideoDialog=t}}},[l("el-tabs",{staticStyle:{padding:"0 1rem",margin:"auto 0"},on:{"tab-click":e.getRegionList},model:{value:e.active<PERSON><PERSON>,callback:function(t){e.activeKey=t},expression:"activeKey"}},[l("el-tab-pane",{attrs:{name:"0"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[0].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[0].meaning))])]),l("el-radio-group",{model:{value:e.allVal[0].val,callback:function(t){e.$set(e.allVal[0],"val",t)},expression:"allVal[0].val"}},e._l(e.regionList,(function(t){return l("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId)+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"1"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[1].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[1].meaning))])]),l("el-radio-group",{attrs:{disabled:e.allVal[1].lock},model:{value:e.allVal[1].val,callback:function(t){e.$set(e.allVal[1],"val",t)},expression:"allVal[1].val"}},e._l(e.regionList,(function(t){return l("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(2)}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(2))+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"2"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[2].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[2].meaning))])]),l("el-radio-group",{attrs:{disabled:e.allVal[2].lock},model:{value:e.allVal[2].val,callback:function(t){e.$set(e.allVal[2],"val",t)},expression:"allVal[2].val"}},e._l(e.regionList,(function(t){return l("el-radio",{key:t.deviceId,staticStyle:{"line-height":"2rem"},attrs:{label:t.deviceId.substring(4)}},[e._v(" "+e._s(t.name)+" - "+e._s(t.deviceId.substring(4))+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"3"}},[e._v(" 请手动输入基层接入单位编码,两位数字 "),l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[3].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[3].meaning))])]),l("el-input",{attrs:{type:"text",placeholder:"请输入内容",maxlength:"2",disabled:e.allVal[3].lock,"show-word-limit":""},model:{value:e.allVal[3].val,callback:function(t){e.$set(e.allVal[3],"val",t)},expression:"allVal[3].val"}})],1),l("el-tab-pane",{attrs:{name:"4"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[4].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[4].meaning))])]),l("el-radio-group",{attrs:{disabled:e.allVal[4].lock},model:{value:e.allVal[4].val,callback:function(t){e.$set(e.allVal[4],"val",t)},expression:"allVal[4].val"}},e._l(e.industryCodeTypeList,(function(t){return l("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"5"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[5].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[5].meaning))])]),l("el-radio-group",{attrs:{disabled:e.allVal[5].lock},model:{value:e.allVal[5].val,callback:function(t){e.$set(e.allVal[5],"val",t)},expression:"allVal[5].val"}},e._l(e.deviceTypeList,(function(t){return l("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"6"}},[l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[6].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[6].meaning))])]),l("el-radio-group",{attrs:{disabled:e.allVal[6].lock},model:{value:e.allVal[6].val,callback:function(t){e.$set(e.allVal[6],"val",t)},expression:"allVal[6].val"}},e._l(e.networkIdentificationTypeList,(function(t){return l("el-radio",{key:t.code,staticStyle:{"line-height":"2rem"},attrs:{label:t.code}},[e._v(" "+e._s(t.name)+" - "+e._s(t.code)+" ")])})),1)],1),l("el-tab-pane",{attrs:{name:"7"}},[e._v(" 请手动输入设备/用户序号, 六位数字 "),l("div",{attrs:{slot:"label"},slot:"label"},[l("div",{staticClass:"show-code-item"},[e._v(e._s(e.allVal[7].val))]),l("div",{staticStyle:{"text-align":"center"}},[e._v(e._s(e.allVal[7].meaning))])]),l("el-input",{attrs:{type:"text",placeholder:"请输入内容",maxlength:"6",disabled:e.allVal[7].lock,"show-word-limit":""},model:{value:e.allVal[7].val,callback:function(t){e.$set(e.allVal[7],"val",t)},expression:"allVal[7].val"}})],1)],1),l("el-form",{},[l("el-form-item",{staticStyle:{"margin-top":"22px","margin-bottom":"0"}},[l("div",{staticStyle:{float:"right"}},[l("el-button",{attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("保存")]),l("el-button",{on:{click:e.closeModel}},[e._v("取消")])],1)])],1)],1)},i=[],o=l("a888"),n={directives:{elDragDialog:o["a"]},props:{},data:function(){return{showVideoDialog:!1,activeKey:"0",allVal:[{id:[1,2],meaning:"省级编码",val:"11",type:"中心编码",lock:!1},{id:[3,4],meaning:"市级编码",val:"01",type:"中心编码",lock:!1},{id:[5,6],meaning:"区级编码",val:"01",type:"中心编码",lock:!1},{id:[7,8],meaning:"基层接入单位编码",val:"01",type:"中心编码",lock:!1},{id:[9,10],meaning:"行业编码",val:"00",type:"行业编码",lock:!1},{id:[11,13],meaning:"类型编码",val:"132",type:"类型编码",lock:!1},{id:[14],meaning:"网络标识编码",val:"7",type:"网络标识",lock:!1},{id:[15,20],meaning:"设备/用户序号",val:"000001",type:"序号",lock:!1}],regionList:[],deviceTypeList:[],industryCodeTypeList:[],networkIdentificationTypeList:[],endCallBck:null}},computed:{},methods:{openDialog:function(e,t,l,a){console.log(t),this.showVideoDialog=!0,this.activeKey="0",this.regionList=[],this.getRegionList(),"undefined"!==typeof t&&20===t.length&&(this.allVal[0].val=t.substring(0,2),this.allVal[1].val=t.substring(2,4),this.allVal[2].val=t.substring(4,6),this.allVal[3].val=t.substring(6,8),this.allVal[4].val=t.substring(8,10),this.allVal[5].val=t.substring(10,13),this.allVal[6].val=t.substring(13,14),this.allVal[7].val=t.substring(14)),console.log(this.allVal),"undefined"!==typeof l&&(this.allVal[l].lock=!0,this.allVal[l].val=a),this.endCallBck=e},getRegionList:function(){if("0"===this.activeKey||"1"===this.activeKey||"2"===this.activeKey){var e="";"1"===this.activeKey&&(e=this.allVal[0].val),"2"===this.activeKey&&(e=this.allVal[0].val+this.allVal[1].val),"0"!==this.activeKey&&""===e&&this.$message.error({showClose:!0,message:"请先选择上级行政区划"}),this.queryChildList(e)}else"4"===this.activeKey?(console.log(222),this.queryIndustryCodeList()):"5"===this.activeKey?this.queryDeviceTypeList():"6"===this.activeKey&&this.queryNetworkIdentificationTypeList()},queryChildList:function(e){var t=this;this.regionList=[],this.$store.dispatch("region/queryChildListInBase",e).then((function(e){t.regionList=e})).catch((function(e){t.$message.error({showClose:!0,message:e})}))},queryIndustryCodeList:function(){var e=this;this.industryCodeTypeList=[],this.$store.dispatch("commonChanel/getIndustryList").then((function(t){e.industryCodeTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},queryDeviceTypeList:function(){var e=this;this.deviceTypeList=[],this.$store.dispatch("commonChanel/getTypeList").then((function(t){e.deviceTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},queryNetworkIdentificationTypeList:function(){var e=this;this.networkIdentificationTypeList=[],this.$store.dispatch("commonChanel/getNetworkIdentificationList").then((function(t){e.networkIdentificationTypeList=t})).catch((function(t){e.$message.error({showClose:!0,message:t})}))},closeModel:function(){this.showVideoDialog=!1},handleOk:function(){var e=this.allVal[0].val+this.allVal[1].val+this.allVal[2].val+this.allVal[3].val+this.allVal[4].val+this.allVal[5].val+this.allVal[6].val+this.allVal[7].val;console.log(e),this.endCallBck&&this.endCallBck(e),this.showVideoDialog=!1}}},s=n,c=(l("6c17"),l("2877")),d=Object(c["a"])(s,a,i,!1,null,null,null);t["a"]=d.exports},"363b":function(e,t,l){"use strict";var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{attrs:{id:"chooseCivilCode"}},[l("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"选择行政区划",width:"30%",top:"5rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[l("RegionTree",{ref:"regionTree",attrs:{"show-header":!0,edit:!0,"enable-add-channel":!1,"click-event":e.treeNodeClickEvent,"on-channel-change":e.onChannelChange,"tree-height":"45vh"}}),l("el-form",[l("el-form-item",[l("div",{staticStyle:{"text-align":"right"}},[l("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("保存")]),l("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)],1)},i=[],o=l("a888"),n=l("94b9"),s={name:"ChooseCivilCode",directives:{elDragDialog:o["a"]},components:{RegionTree:n["a"]},props:{},data:function(){return{showDialog:!1,endCallback:!1,regionDeviceId:""}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.endCallback=e},onSubmit:function(){this.endCallback&&this.endCallback(this.regionDeviceId),this.close()},close:function(){this.showDialog=!1},treeNodeClickEvent:function(e){this.regionDeviceId=e.deviceId},onChannelChange:function(e){}}},c=s,d=l("2877"),r=Object(d["a"])(c,a,i,!1,null,null,null);t["a"]=r.exports},"54b8":function(e,t,l){},"6c17":function(e,t,l){"use strict";l("92b6")},"92b6":function(e,t,l){},b4b5:function(e,t,l){"use strict";l("54b8")},c2c8:function(e,t,l){"use strict";var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticStyle:{"border-right":"1px solid #EBEEF5",padding:"0 20px"},attrs:{id:"DeviceTree"}},[e.showHeader?l("div",{staticClass:"page-header"},[l("el-form",{attrs:{inline:!0,size:"mini"}},[l("el-form-item",{staticStyle:{visibility:"hidden"}},[l("el-input",{staticStyle:{"margin-right":"1rem",width:"12rem"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),l("el-form-item",{attrs:{label:"显示编号"}},[l("el-checkbox",{model:{value:e.showCode,callback:function(t){e.showCode=t},expression:"showCode"}})],1)],1)],1):e._e(),l("div",[e.showAlert&&e.edit?l("el-alert",{staticStyle:{"text-align":"left"},attrs:{title:"操作提示",description:"你可以使用右键菜单管理节点",type:"info"}}):e._e(),l("vue-easy-tree",{ref:"veTree",staticClass:"flow-tree",attrs:{"node-key":"treeId",height:e.treeHeight?e.treeHeight:"78vh",lazy:"",load:e.loadNode,data:e.treeData,props:e.props,"default-expanded-keys":[""]},on:{"node-contextmenu":e.contextmenuEventHandler,"node-click":e.nodeClickHandler},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node;t.data;return[l("span",{staticClass:"custom-tree-node"},[0===a.data.type&&e.chooseId!==a.data.deviceId?l("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#409EFF"}}):e._e(),0===a.data.type&&e.chooseId===a.data.deviceId?l("span",{staticClass:"iconfont icon-bianzubeifen3",staticStyle:{color:"#c60135"}}):e._e(),1===a.data.type&&"ON"===a.data.status?l("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#409EFF"}}):e._e(),1===a.data.type&&"ON"!==a.data.status?l("span",{staticClass:"iconfont icon-shexiangtou2",staticStyle:{color:"#808181"}}):e._e(),""!==a.data.deviceId&&e.showCode?l("span",{staticStyle:{"padding-left":"1px"},attrs:{title:a.data.deviceId}},[e._v(e._s(a.label)+"（编号："+e._s(a.data.deviceId)+"）")]):e._e(),""!==a.data.deviceId&&e.showCode?e._e():l("span",{staticStyle:{"padding-left":"1px"},attrs:{title:a.data.deviceId}},[e._v(e._s(a.label))])])]}}])})],1),l("groupEdit",{ref:"groupEdit"}),l("gbDeviceSelect",{ref:"gbDeviceSelect"}),l("gbChannelSelect",{ref:"gbChannelSelect",attrs:{"data-type":"group"}})],1)},i=[],o=(l("d3b7"),l("9331")),n=l.n(o),s=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{id:"groupEdit"}},[l("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"分组编辑",width:"40%",top:"2rem","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[l("div",{staticStyle:{"margin-top":"1rem","margin-right":"100px"},attrs:{id:"shared"}},[l("el-form",{ref:"form",attrs:{model:e.group,"label-width":"140px"}},[l("el-form-item",{attrs:{label:"节点编号",prop:"id"}},[l("el-input",{attrs:{placeholder:"请输入编码"},model:{value:e.group.deviceId,callback:function(t){e.$set(e.group,"deviceId",t)},expression:"group.deviceId"}},[l("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.buildDeviceIdCode(e.group.deviceId)}},slot:"append"},[e._v("生成")])],1)],1),l("el-form-item",{attrs:{label:"节点名称",prop:"name"}},[l("el-input",{attrs:{clearable:""},model:{value:e.group.name,callback:function(t){e.$set(e.group,"name",t)},expression:"group.name"}})],1),l("el-form-item",{attrs:{label:"行政区划",prop:"name"}},[l("el-input",{model:{value:e.group.civilCode,callback:function(t){e.$set(e.group,"civilCode",t)},expression:"group.civilCode"}},[l("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.buildCivilCode(e.group.civilCode)}},slot:"append"},[e._v("选择")])],1)],1),l("el-form-item",[l("div",{staticStyle:{float:"right"}},[l("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确认")]),l("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)]),l("channelCode",{ref:"channelCode"}),l("chooseCivilCode",{ref:"chooseCivilCode"})],1)},c=[],d=l("165c"),r=l("363b"),u=l("a888"),h={name:"GroupEdit",directives:{elDragDialog:u["a"]},components:{ChooseCivilCode:r["a"],channelCode:d["a"]},props:[],data:function(){return{submitCallback:null,showDialog:!1,loading:!1,level:0,group:{id:0,deviceId:"",name:"",parentDeviceId:"",businessGroup:"",civilCode:"",platformId:""}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),e&&(this.group=e),this.showDialog=!0,this.submitCallback=t},onSubmit:function(){var e=this;this.group.id?this.$store.dispatch("group/update",this.group).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.submitCallback&&e.submitCallback(e.group)})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})})):this.$store.dispatch("group/add",this.group).then((function(t){e.$message.success({showClose:!0,message:"保存成功"}),e.submitCallback&&e.submitCallback(e.group),e.close()})).catch((function(t){e.$message({showClose:!0,message:t,type:"error"})}))},buildDeviceIdCode:function(e){var t=this;console.log(this.group);var l=this.group.businessGroup?"216":"215";this.$refs.channelCode.openDialog((function(e){t.group.deviceId=e}),e,5,l)},buildCivilCode:function(e){var t=this;this.$refs.chooseCivilCode.openDialog((function(e){t.group.civilCode=e}))},close:function(){this.showDialog=!1,console.log(this.group)}}},v=h,p=l("2877"),g=Object(p["a"])(v,s,c,!1,null,null,null),m=g.exports,f=l("7d41"),b=l("1322"),C={name:"DeviceTree",components:{GbChannelSelect:b["a"],VueEasyTree:n.a,groupEdit:m,gbDeviceSelect:f["a"]},props:["edit","enableAddChannel","clickEvent","onChannelChange","showHeader","hasChannel","addChannelToGroup","treeHeight"],data:function(){return{props:{label:"name",id:"treeId"},showCode:!1,showAlert:!0,searchSrt:"",chooseId:"",treeData:[]}},created:function(){},destroyed:function(){},methods:{search:function(){},loadNode:function(e,t){var l=this;if(0===e.level)t([{treeId:"",deviceId:"",name:"根资源组",isLeaf:!1,type:0}]);else{if(e.data.leaf)return void t([]);this.$store.dispatch("group/getTreeList",{query:this.searchSrt,parent:e.data.id,hasChannel:this.hasChannel}).then((function(e){e.length>0&&(l.showAlert=!1),t(e)}))}},reset:function(){this.$forceUpdate()},contextmenuEventHandler:function(e,t,l,a){var i=this;if(this.edit){if(0===l.data.type){var o=[{label:"刷新节点",icon:"el-icon-refresh",disabled:!1,onClick:function(){i.refreshNode(l)}},{label:"新建节点",icon:"el-icon-plus",disabled:!1,onClick:function(){i.addGroup(t.id,l)}},{label:"编辑节点",icon:"el-icon-edit",disabled:1===l.level,onClick:function(){i.editGroup(t,l)}},{label:"删除节点",icon:"el-icon-delete",disabled:1===l.level,divided:!0,onClick:function(){i.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i.removeGroup(t.id,l)})).catch((function(){}))}}];this.enableAddChannel&&(o.push({label:"添加设备",icon:"el-icon-plus",disabled:l.level<=2,onClick:function(){i.addChannelFormDevice(t.id,l)}}),o.push({label:"移除设备",icon:"el-icon-delete",disabled:l.level<=2,divided:!0,onClick:function(){i.removeChannelFormDevice(t.id,l)}}),o.push({label:"添加通道",icon:"el-icon-plus",disabled:l.level<=2,onClick:function(){i.addChannel(t.id,l)}})),this.$contextmenu({items:o,event:e,customClass:"custom-class",zIndex:3e3})}return!1}},removeGroup:function(e,t){var l=this;this.$store.dispatch("group/deleteGroup",t.data.id).then((function(e){t.parent.loaded=!1,t.parent.expand(),l.onChannelChange&&l.onChannelChange(t.data.deviceId)}))},addChannelFormDevice:function(e,t){var l=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],i=0;i<e.length;i++)a.push(e[i].id);l.$store.dispatch("group/add",{parentId:t.data.deviceId,businessGroup:t.data.businessGroup,deviceIds:a}).then((function(e){l.$message.success({showClose:!0,message:"保存成功"}),l.onChannelChange&&l.onChannelChange(),console.log(t),t.loaded=!1,t.expand()})).finally((function(){l.loading=!1}))}))},removeChannelFormDevice:function(e,t){var l=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],i=0;i<e.length;i++)a.push(e[i].id);l.$store.dispatch("commonChanel/deleteDeviceFromGroup",a).then((function(e){l.$message.success({showClose:!0,message:"保存成功"}),l.onChannelChange&&l.onChannelChange(),t.loaded=!1,t.expand()})).finally((function(){l.loading=!1}))}))},addChannel:function(e,t){var l=this;this.$refs.gbChannelSelect.openDialog((function(e){console.log("选择的数据"),console.log(e),l.addChannelToGroup(t.data.deviceId,t.data.businessGroup,e)}))},refreshNode:function(e){console.log(e),e.loaded=!1,e.expand()},refresh:function(e){console.log("刷新节点： "+e);var t=this.$refs.veTree.getNode(e);t&&(t.loaded=!1,t.expand())},addGroup:function(e,t){this.$refs.groupEdit.openDialog({id:0,name:"",deviceId:"",civilCode:"",parentDeviceId:t.level>2?t.data.deviceId:"",parentId:t.data.id,businessGroup:t.level>2?t.data.businessGroup:t.data.deviceId},(function(e){console.log(t),t.loaded=!1,t.expand()}),e)},editGroup:function(e,t){console.log(t),this.$refs.groupEdit.openDialog(t.data,(function(e){console.log(t),t.parent.loaded=!1,t.parent.expand()}),e)},nodeClickHandler:function(e,t,l){this.chooseId=e.deviceId,this.clickEvent&&this.clickEvent(e)}}},y=C,k=(l("b4b5"),Object(p["a"])(y,a,i,!1,null,null,null));t["a"]=k.exports}}]);