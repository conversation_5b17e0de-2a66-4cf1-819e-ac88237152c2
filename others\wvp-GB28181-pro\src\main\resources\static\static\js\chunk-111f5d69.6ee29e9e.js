(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-111f5d69"],{"201a":function(e,t,a){"use strict";a("aa25")},"5f53":function(e,t,a){"use strict";a("e266")},"7d41":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.getDeviceListLoading,expression:"getDeviceListLoading"}],attrs:{id:"addUser"}},[a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"添加国标设备通道",width:"60%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"append-to-body":""},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[a("el-form",{attrs:{inline:!0,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索"}},[a("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getDeviceList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),a("el-form-item",{attrs:{label:"在线状态"}},[a("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.getDeviceList},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"在线",value:"true"}}),a("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.getDeviceList()}}}),a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确 定")])],1)],1),a("el-table",{staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"medium",data:e.deviceList,height:e.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"160"}}),a("el-table-column",{attrs:{prop:"deviceId",label:"设备编号","min-width":"200"}}),a("el-table-column",{attrs:{prop:"channelCount",label:"通道数","min-width":"120"}}),a("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"120"}}),a("el-table-column",{attrs:{label:"地址","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.hostAddress?a("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.hostAddress))]):e._e(),t.row.hostAddress?e._e():a("el-tag",{attrs:{size:"medium"}},[e._v("未知")])],1)]}}])}),a("el-table-column",{attrs:{label:"状态","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.onLine?a("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),t.row.onLine?e._e():a("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")])],1)]}}])})],1),a("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[10,25,35,50,200,1e3,5e4],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1)],1)},n=[],i=(a("d3b7"),a("a888")),s={name:"GbDeviceSelect",directives:{elDragDialog:i["a"]},props:{},data:function(){return{showDialog:!1,deviceList:[],currentDevice:{},searchSrt:"",online:null,videoComponentList:[],updateLooper:0,currentDeviceChannelsLenth:0,winHeight:580,currentPage:1,count:10,total:0,getDeviceListLoading:!1,multipleSelection:[]}},computed:{},mounted:function(){this.initData()},methods:{initData:function(){this.getDeviceList()},currentChange:function(e){this.currentPage=e,this.getDeviceList()},handleSizeChange:function(e){this.count=e,this.getDeviceList()},handleSelectionChange:function(e){this.multipleSelection=e},getDeviceList:function(){var e=this;this.getDeviceListLoading=!0,this.$store.dispatch("device/queryDevices",{page:this.currentPage,count:this.count,query:this.searchSrt,status:this.online}).then((function(t){e.total=t.total,e.deviceList=t.list})).finally((function(){return[e.getDeviceListLoading=!1]}))},openDialog:function(e){this.listChangeCallback=e,this.showDialog=!0},onSubmit:function(){this.listChangeCallback&&this.listChangeCallback(this.multipleSelection),this.showDialog=!1},close:function(){this.showDialog=!1}}},r=s,o=(a("201a"),a("2877")),c=Object(o["a"])(r,l,n,!1,null,null,null);t["a"]=c.exports},a888:function(e,t,a){"use strict";a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");var l={bind:function(e,t,a){var l=e.querySelector(".el-dialog__header"),n=e.querySelector(".el-dialog");l.style.cssText+=";cursor:move;",n.style.cssText+=";top:0px;";var i=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();l.onmousedown=function(e){var t=e.clientX-l.offsetLeft,s=e.clientY-l.offsetTop,r=n.offsetWidth,o=n.offsetHeight,c=document.body.clientWidth,u=document.body.clientHeight,h=n.offsetLeft,d=c-n.offsetLeft-r,m=n.offsetTop,p=u-n.offsetTop-o,f=i(n,"left"),g=i(n,"top");f.includes("%")?(f=+document.body.clientWidth*(+f.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(f=+f.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var l=e.clientX-t,i=e.clientY-s;-l>h?l=-h:l>d&&(l=d),-i>m?i=-m:i>p&&(i=p),n.style.cssText+=";left:".concat(l+f,"px;top:").concat(i+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}},n=function(e){e.directive("el-drag-dialog",l)};window.Vue&&(window["el-drag-dialog"]=l,Vue.use(n)),l.install=n;t["a"]=l},aa25:function(e,t,a){},c8bf:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container",attrs:{id:"app"}},[e.platform?e._e():a("div",{staticStyle:{height:"calc(100vh - 124px)"}},[a("el-form",{attrs:{inline:!0,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索"}},[a("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getPlatformList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),a("el-form-item",[a("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.addParentPlatform}},[e._v("添加 ")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.refresh()}}})],1)],1),a("el-table",{staticStyle:{width:"100%"},attrs:{size:"small",data:e.platformList,height:"calc(100% - 64px)",loading:e.loading}},[a("el-table-column",{attrs:{prop:"name",label:"名称"}}),a("el-table-column",{attrs:{prop:"serverGBId",label:"平台编号","min-width":"200"}}),a("el-table-column",{attrs:{label:"是否启用","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.enable&&e.myServerId!==t.row.serverId?a("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("已启用")]):e._e(),t.row.enable&&e.myServerId===t.row.serverId?a("el-tag",{attrs:{size:"medium"}},[e._v("已启用")]):e._e(),t.row.enable?e._e():a("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("未启用")])],1)]}}],null,!1,3937613589)}),a("el-table-column",{attrs:{label:"状态","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.status?a("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),t.row.status?e._e():a("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")])],1)]}}],null,!1,1110199412)}),a("el-table-column",{attrs:{label:"地址","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[a("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.serverIp)+":"+e._s(t.row.serverPort))])],1)]}}],null,!1,4083345229)}),a("el-table-column",{attrs:{prop:"deviceGBId",label:"设备国标编号","min-width":"200"}}),a("el-table-column",{attrs:{prop:"transport",label:"信令传输模式","min-width":"120"}}),a("el-table-column",{attrs:{prop:"channelCount",label:"通道数","min-width":"120"}}),a("el-table-column",{attrs:{label:"订阅信息","min-width":"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.alarmSubscribe?a("i",{staticClass:"iconfont icon-gbaojings subscribe-on ",staticStyle:{"font-size":"20px"},attrs:{title:"报警订阅"}}):e._e(),t.row.alarmSubscribe?e._e():a("i",{staticClass:"iconfont icon-gbaojings subscribe-off ",staticStyle:{"font-size":"20px"},attrs:{title:"报警订阅"}}),t.row.catalogSubscribe?a("i",{staticClass:"iconfont icon-gjichus subscribe-on",attrs:{title:"目录订阅"}}):e._e(),t.row.catalogSubscribe?e._e():a("i",{staticClass:"iconfont icon-gjichus subscribe-off",attrs:{title:"目录订阅"}}),t.row.mobilePositionSubscribe?a("i",{staticClass:"iconfont icon-gxunjians subscribe-on",attrs:{title:"位置订阅"}}):e._e(),t.row.mobilePositionSubscribe?e._e():a("i",{staticClass:"iconfont icon-gxunjians subscribe-off",attrs:{title:"位置订阅"}})]}}],null,!1,1509823963)}),a("el-table-column",{attrs:{label:"操作","min-width":"260",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(a){return e.editPlatform(t.row)}}},[e._v("编辑")]),a("el-button",{attrs:{size:"medium",icon:"el-icon-share",type:"text"},on:{click:function(a){return e.chooseChannel(t.row)}}},[e._v("通道共享 ")]),a("el-button",{attrs:{size:"medium",icon:"el-icon-top",type:"text",loading:e.pushChannelLoading},on:{click:function(a){return e.pushChannel(t.row)}}},[e._v("推送通道 ")]),a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(a){return e.deletePlatform(t.row)}}},[e._v("删除 ")])]}}],null,!1,1339252002)})],1),a("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),e.platform?a("platformEdit",{ref:"platformEdit",attrs:{"close-edit":e.closeEdit,"device-ips":e.deviceIps},model:{value:e.platform,callback:function(t){e.platform=t},expression:"platform"}}):e._e(),a("shareChannel",{ref:"shareChannel"})],1)},n=[],i=(a("d3b7"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{attrs:{id:"chooseChannel"}},[e.showDialog?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"},{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:"通道共享",top:"2rem",width:"80%","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[a("shareChannelAdd",{ref:"shareChannelAdd",attrs:{"platform-id":e.platformId}})],1):e._e()],1)}),s=[],r=a("a888"),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"background-color":"#FFFFFF",display:"grid","grid-template-columns":"83px minmax(0, 1fr)"},attrs:{id:"shareChannelAdd"}},[a("el-tabs",{attrs:{"tab-position":"left"},on:{"tab-click":e.search},model:{value:e.hasShare,callback:function(t){e.hasShare=t},expression:"hasShare"}},[a("el-tab-pane",{attrs:{label:"未共享",name:"false"}}),a("el-tab-pane",{attrs:{label:"已共享",name:"true"}})],1),a("div",{staticStyle:{padding:"0 2rem"}},[a("el-form",{attrs:{inline:!0,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索"}},[a("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),a("el-form-item",{attrs:{label:"在线状态"}},[a("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"在线",value:"true"}}),a("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(Object.values(e.$channelTypeList),(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",["true"!==e.hasShare?a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.add()}}},[e._v(" 添加 ")]):e._e(),"true"===e.hasShare?a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.remove()}}},[e._v(" 移除 ")]):e._e(),"true"!==e.hasShare?a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.addByDevice()}}},[e._v("按设备添加")]):e._e(),"true"===e.hasShare?a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.removeByDevice()}}},[e._v("按设备移除")]):e._e(),"true"!==e.hasShare?a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.addAll()}}},[e._v("全部添加")]):e._e(),"true"===e.hasShare?a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.removeAll()}}},[e._v("全部移除")]):e._e()],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.getChannelList()}}})],1)],1),a("el-table",{ref:"channelListTable",attrs:{size:"small",data:e.channelList,height:e.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",selectable:e.selectable}}),a("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"180"}}),a("el-table-column",{attrs:{prop:"gbDeviceId",label:"编号","min-width":"180"}}),"true"===e.hasShare?a("el-table-column",{attrs:{label:"自定义名称","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"—"},slot:"—"},[a("el-input",{attrs:{size:"mini",placeholder:"不填按原名称"},model:{value:t.row.customName,callback:function(a){e.$set(t.row,"customName",a)},expression:"scope.row.customName"}})],1)]}}],null,!1,2023062843)}):e._e(),"true"===e.hasShare?a("el-table-column",{attrs:{label:"自定义编号","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"—"},slot:"—"},[a("el-input",{attrs:{size:"mini",placeholder:"不填按原编号"},model:{value:t.row.customDeviceId,callback:function(a){e.$set(t.row,"customDeviceId",a)},expression:"scope.row.customDeviceId"}})],1)]}}],null,!1,3207752213)}):e._e(),"true"===e.hasShare?a("el-table-column",{attrs:{label:"","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return e.saveCustom(t.row)}}},[e._v("保存 ")])]}}],null,!1,978729479)}):e._e(),a("el-table-column",{attrs:{prop:"gbManufacturer",label:"厂家","min-width":"100"}}),a("el-table-column",{attrs:{label:"类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[a("el-tag",{style:e.$channelTypeList[t.row.dataType].style,attrs:{size:"medium",effect:"plain",type:"success"}},[e._v(e._s(e.$channelTypeList[t.row.dataType].name))])],1)]}}])}),a("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.gbStatus?a("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.gbStatus?a("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}])})],1),a("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}}),a("gbDeviceSelect",{ref:"gbDeviceSelect"})],1)],1)},c=[],u=a("7d41"),h={name:"ShareChannelAdd",components:{gbDeviceSelect:u["a"]},props:["platformId"],data:function(){return{channelList:[],searchSrt:"",channelType:"",online:"",hasShare:"false",winHeight:window.innerHeight-300,currentPage:1,count:15,total:0,loading:!1,loadSnap:{},multipleSelection:[]}},created:function(){this.initData()},destroyed:function(){},methods:{initData:function(){this.getChannelList()},currentChange:function(e){this.currentPage=e,this.initData()},handleSizeChange:function(e){this.count=e,this.getChannelList()},getChannelList:function(){var e=this;this.$store.dispatch("platform/getChannelList",{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType,platformId:this.platformId,hasShare:this.hasShare}).then((function(t){e.total=t.total,e.channelList=t.list,e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))})).catch((function(e){console.log(e)}))},handleSelectionChange:function(e){this.multipleSelection=e},selectable:function(e,t){return""!==this.hasShare||!e.platformId},add:function(e){for(var t=this,a=[],l=0;l<this.multipleSelection.length;l++)a.push(this.multipleSelection[l].gbId);0!==a.length?(this.loading=!0,this.$store.dispatch("platform/addChannel",{platformId:this.platformId,channelIds:a}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.getChannelList()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))):this.$message.info({showClose:!0,message:"请选择通道"})},addAll:function(e){var t=this;this.$confirm("确定全部添加？","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,t.$store.dispatch("platform/addChannel",{platformId:t.platformId,all:!0}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.getChannelList()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))})).catch((function(){}))},addByDevice:function(e){var t=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],l=0;l<e.length;l++)a.push(e[l].id);t.$store.dispatch("platform/addChannelByDevice",{platformId:t.platformId,deviceIds:a}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.initData()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))}))},removeByDevice:function(e){var t=this;this.$refs.gbDeviceSelect.openDialog((function(e){for(var a=[],l=0;l<e.length;l++)a.push(e[l].id);t.$store.dispatch("platform/removeChannelByDevice",{platformId:t.platformId,deviceIds:a}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.initData()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))}))},remove:function(e){for(var t=this,a=[],l=0;l<this.multipleSelection.length;l++)a.push(this.multipleSelection[l].gbId);0!==a.length?(this.loading=!0,this.$store.dispatch("platform/removeChannel",{platformId:this.platformId,channelIds:a}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.getChannelList()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))):this.$message.info({showClose:!0,message:"请选择通道"})},removeAll:function(e){var t=this;this.$confirm("确定全部移除？","提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,t.$store.dispatch("platform/removeChannel",{platformId:t.platformId,all:!0}).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.getChannelList()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))})).catch((function(){}))},saveCustom:function(e){var t=this;this.$store.dispatch("platform/updateCustomChannel",e).then((function(){t.$message.success({showClose:!0,message:"保存成功"}),t.initData()})).catch((function(e){t.$message.error({showClose:!0,message:e})}))},search:function(){this.currentPage=1,this.total=0,this.initData()},refresh:function(){this.initData()}}},d=h,m=a("2877"),p=Object(m["a"])(d,o,c,!1,null,null,null),f=p.exports,g={name:"ChooseChannel",directives:{elDragDialog:r["a"]},components:{shareChannelAdd:f},props:{},data:function(){return{loading:!1,tabActiveName:"gbChannel",catalogTabActiveName:"addShare",platformId:"",showDialog:!1,chooseData:{}}},computed:{},methods:{openDialog:function(e,t){this.platformId=e,this.showDialog=!0,this.closeCallback=t},close:function(){this.closeCallback()}}},v=g,b=Object(m["a"])(v,i,s,!1,null,null,null),w=b.exports,S=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%"},attrs:{id:"PlatformEdit"}},[a("div",{staticStyle:{"text-align":"right","margin-top":"1rem","background-color":"#FFFFFF","padding-top":"2rem"},attrs:{id:"shared"}},[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:11}},[a("el-form",{ref:"platform1",attrs:{rules:e.rules,model:e.value,size:"medium","label-width":"160px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{model:{value:e.value.name,callback:function(t){e.$set(e.value,"name",t)},expression:"value.name"}})],1),a("el-form-item",{attrs:{label:"SIP服务国标编码",prop:"serverGBId"}},[a("el-input",{attrs:{clearable:""},on:{input:e.serverGBIdChange},model:{value:e.value.serverGBId,callback:function(t){e.$set(e.value,"serverGBId",t)},expression:"value.serverGBId"}})],1),a("el-form-item",{attrs:{label:"SIP服务国标域",prop:"serverGBDomain"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.serverGBDomain,callback:function(t){e.$set(e.value,"serverGBDomain",t)},expression:"value.serverGBDomain"}})],1),a("el-form-item",{attrs:{label:"SIP服务IP",prop:"serverIp"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.serverIp,callback:function(t){e.$set(e.value,"serverIp",t)},expression:"value.serverIp"}})],1),a("el-form-item",{attrs:{label:"SIP服务端口",prop:"serverPort"}},[a("el-input",{attrs:{clearable:"",type:"number"},model:{value:e.value.serverPort,callback:function(t){e.$set(e.value,"serverPort",t)},expression:"value.serverPort"}})],1),a("el-form-item",{attrs:{label:"设备国标编号",prop:"deviceGBId"}},[a("el-input",{attrs:{clearable:""},on:{input:e.deviceGBIdChange},model:{value:e.value.deviceGBId,callback:function(t){e.$set(e.value,"deviceGBId",t)},expression:"value.deviceGBId"}})],1),a("el-form-item",{attrs:{label:"本地IP",prop:"deviceIp"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择与上级相通的网卡"},model:{value:e.value.deviceIp,callback:function(t){e.$set(e.value,"deviceIp",t)},expression:"value.deviceIp"}},e._l(e.deviceIps,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),a("el-form-item",{attrs:{label:"本地端口",prop:"devicePort"}},[a("el-input",{attrs:{disabled:!0,type:"number"},model:{value:e.value.devicePort,callback:function(t){e.$set(e.value,"devicePort",t)},expression:"value.devicePort"}})],1),a("el-form-item",{attrs:{label:"SIP认证用户名",prop:"username"}},[a("el-input",{model:{value:e.value.username,callback:function(t){e.$set(e.value,"username",t)},expression:"value.username"}})],1),a("el-form-item",{attrs:{label:"SIP认证密码",prop:"password"}},[a("el-input",{model:{value:e.value.password,callback:function(t){e.$set(e.value,"password",t)},expression:"value.password"}})],1),a("el-form-item",{attrs:{label:"注册周期(秒)",prop:"expires"}},[a("el-input",{model:{value:e.value.expires,callback:function(t){e.$set(e.value,"expires",t)},expression:"value.expires"}})],1),a("el-form-item",{attrs:{label:"心跳周期(秒)",prop:"keepTimeout"}},[a("el-input",{model:{value:e.value.keepTimeout,callback:function(t){e.$set(e.value,"keepTimeout",t)},expression:"value.keepTimeout"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form",{ref:"platform2",attrs:{rules:e.rules,model:e.value,size:"medium","label-width":"160px"}},[a("el-form-item",{attrs:{label:"SDP发流IP",prop:"sendStreamIp"}},[a("el-input",{model:{value:e.value.sendStreamIp,callback:function(t){e.$set(e.value,"sendStreamIp",t)},expression:"value.sendStreamIp"}})],1),a("el-form-item",{attrs:{label:"信令传输",prop:"transport"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择信令传输方式"},model:{value:e.value.transport,callback:function(t){e.$set(e.value,"transport",t)},expression:"value.transport"}},[a("el-option",{attrs:{label:"UDP",value:"UDP"}}),a("el-option",{attrs:{label:"TCP",value:"TCP"}})],1)],1),a("el-form-item",{attrs:{label:"保密属性"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择保密属性"},model:{value:e.value.secrecy,callback:function(t){e.$set(e.value,"secrecy",t)},expression:"value.secrecy"}},[a("el-option",{attrs:{label:"不涉密",value:0}}),a("el-option",{attrs:{label:"涉密",value:1}})],1)],1),a("el-form-item",{attrs:{label:"目录分组",prop:"catalogGroup"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择目录分组"},model:{value:e.value.catalogGroup,callback:function(t){e.$set(e.value,"catalogGroup",t)},expression:"value.catalogGroup"}},[a("el-option",{attrs:{label:"1",value:"1"}}),a("el-option",{attrs:{label:"2",value:"2"}}),a("el-option",{attrs:{label:"4",value:"4"}}),a("el-option",{attrs:{label:"8",value:"8"}})],1)],1),a("el-form-item",{attrs:{label:"字符集",prop:"characterSet"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择字符集"},model:{value:e.value.characterSet,callback:function(t){e.$set(e.value,"characterSet",t)},expression:"value.characterSet"}},[a("el-option",{attrs:{label:"GB2312",value:"GB2312"}}),a("el-option",{attrs:{label:"UTF-8",value:"UTF-8"}})],1)],1),a("el-form-item",{attrs:{label:"行政区划",prop:"civilCode"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.civilCode,callback:function(t){e.$set(e.value,"civilCode",t)},expression:"value.civilCode"}})],1),a("el-form-item",{attrs:{label:"平台厂商",prop:"manufacturer"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.manufacturer,callback:function(t){e.$set(e.value,"manufacturer",t)},expression:"value.manufacturer"}})],1),a("el-form-item",{attrs:{label:"平台型号",prop:"model"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.model,callback:function(t){e.$set(e.value,"model",t)},expression:"value.model"}})],1),a("el-form-item",{attrs:{label:"平台安装地址",prop:"address"}},[a("el-input",{attrs:{clearable:""},model:{value:e.value.address,callback:function(t){e.$set(e.value,"address",t)},expression:"value.address"}})],1),a("el-form-item",{attrs:{label:"其他选项"}},[a("div",{staticStyle:{"text-align":"left"}},[a("el-checkbox",{attrs:{label:"启用"},on:{change:e.checkExpires},model:{value:e.value.enable,callback:function(t){e.$set(e.value,"enable",t)},expression:"value.enable"}}),a("el-checkbox",{attrs:{label:"RTCP保活"},on:{change:e.rtcpCheckBoxChange},model:{value:e.value.rtcp,callback:function(t){e.$set(e.value,"rtcp",t)},expression:"value.rtcp"}}),a("el-checkbox",{attrs:{label:"消息通道"},model:{value:e.value.asMessageChannel,callback:function(t){e.$set(e.value,"asMessageChannel",t)},expression:"value.asMessageChannel"}}),a("el-checkbox",{attrs:{label:"主动推送通道"},model:{value:e.value.autoPushChannel,callback:function(t){e.$set(e.value,"autoPushChannel",t)},expression:"value.autoPushChannel"}}),a("el-checkbox",{attrs:{label:"推送平台信息","true-label":1,"false-label":0},model:{value:e.value.catalogWithPlatform,callback:function(t){e.$set(e.value,"catalogWithPlatform",t)},expression:"value.catalogWithPlatform"}}),a("el-checkbox",{attrs:{label:"推送分组信息","true-label":1,"false-label":0},model:{value:e.value.catalogWithGroup,callback:function(t){e.$set(e.value,"catalogWithGroup",t)},expression:"value.catalogWithGroup"}}),a("el-checkbox",{attrs:{label:"推送行政区划","true-label":1,"false-label":0},model:{value:e.value.catalogWithRegion,callback:function(t){e.$set(e.value,"catalogWithRegion",t)},expression:"value.catalogWithRegion"}})],1)]),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v(e._s(e.onSubmit_text)+" ")]),a("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)],1)],1)],1)])},y=[],C=a("c14f"),x=a("1da1"),k={name:"PlatformEdit",components:{},props:["value","closeEdit","deviceIps"],data:function(){var e=this,t=function(){var t=Object(x["a"])(Object(C["a"])().m((function t(a,l,n){var i;return Object(C["a"])().w((function(t){while(1)switch(t.n){case 0:if(console.log(l),""!==l){t.n=1;break}n(new Error("请输入设备国标编号")),t.n=3;break;case 1:return t.n=2,e.deviceGBIdExit(l);case 2:i=t.v,i?n(new Error("设备国标编号格式错误或已存在")):n();case 3:return t.a(2)}}),t)})));return function(e,a,l){return t.apply(this,arguments)}}();return{listChangeCallback:null,showDialog:!1,isLoging:!1,onSubmit_text:"保存",rules:{name:[{required:!0,message:"请输入平台名称",trigger:"blur"}],serverGBId:[{required:!0,message:"请输入SIP服务国标编码",trigger:"blur"}],serverGBDomain:[{required:!0,message:"请输入SIP服务国标域",trigger:"blur"}],serverIp:[{required:!0,message:"请输入SIP服务IP",trigger:"blur"}],serverPort:[{required:!0,message:"请输入SIP服务端口",trigger:"blur"}],deviceGBId:[{validator:t,trigger:"blur"}],username:[{required:!1,message:"请输入SIP认证用户名",trigger:"blur"}],password:[{required:!1,message:"请输入SIP认证密码",trigger:"blur"}],expires:[{required:!0,message:"请输入注册周期",trigger:"blur"}],keepTimeout:[{required:!0,message:"请输入心跳周期",trigger:"blur"}],transport:[{required:!0,message:"请选择信令传输",trigger:"blur"}],characterSet:[{required:!0,message:"请选择编码字符集",trigger:"blur"}],deviceIp:[{required:!0,message:"请选择本地IP",trigger:"blur"}]},saveLoading:!1}},watch:{value:function(e,t){this.streamProxy=e}},created:function(){},methods:{onSubmit:function(){var e=this;this.saveLoading=!0,this.value.id?this.$store.dispatch("platform/update",this.value).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.closeEdit&&e.closeEdit()})).catch((function(e){console.log(e)})).finally((function(){e.saveLoading=!1})):this.$store.dispatch("platform/add",this.value).then((function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.closeEdit&&e.closeEdit()})).catch((function(e){console.log(e)})).finally((function(){e.saveLoading=!1}))},serverGBIdChange:function(){this.value.serverGBId.length>10&&(this.value.serverGBDomain=this.value.serverGBId.substr(0,10))},deviceGBIdChange:function(){this.value.username=this.value.deviceGBId},checkExpires:function(){this.value.enable&&"0"===this.value.expires&&(this.value.expires="3600")},rtcpCheckBoxChange:function(e){e&&this.$message({showClose:!0,message:"开启RTCP保活需要上级平台支持，可以避免无效推流",type:"warning"})},deviceGBIdExit:function(){var e=Object(x["a"])(Object(C["a"])().m((function e(t){var a;return Object(C["a"])().w((function(e){while(1)switch(e.n){case 0:return a=!1,e.n=1,this.$store.dispatch("platform/exit",t).then((function(e){a=e})).catch((function(e){console.log(e)}));case 1:return e.a(2,a)}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.closeEdit()}}},I=k,_=Object(m["a"])(I,S,y,!1,null,null,null),$=_.exports,P=a("2b0e"),D={name:"Platform",components:{shareChannel:w,platformEdit:$},data:function(){return{loading:!1,platformList:[],deviceIps:[],defaultPlatform:null,platform:null,pushChannelLoading:!1,searchSrt:"",currentPage:1,count:15,total:0}},computed:{Vue:function(){return P["default"]},myServerId:function(){return this.$store.getters.serverId}},mounted:function(){this.initData(),this.updateLooper=setInterval(this.initData,1e4)},destroyed:function(){clearTimeout(this.updateLooper)},methods:{addParentPlatform:function(){this.platform=this.defaultPlatform},editPlatform:function(e){this.platform=e},closeEdit:function(){this.platform=null,this.getPlatformList()},deletePlatform:function(e){var t=this;this.$confirm("确认删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deletePlatformCommit(e)}))},deletePlatformCommit:function(e){var t=this;this.loading=!0,this.$store.dispatch("platform/remove",e.id).then((function(){t.$message.success({showClose:!0,message:"删除成功"}),t.initData()})).catch((function(e){t.loading=!1,t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))},chooseChannel:function(e){this.$refs.shareChannel.openDialog(e.id,this.initData)},pushChannel:function(e){var t=this;this.pushChannelLoading=!0,this.$store.dispatch("platform/pushChannel",e.id).then((function(e){t.$message.success({showClose:!0,message:"推送成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.pushChannelLoading=!1}))},initData:function(){var e=this;this.$store.dispatch("platform/getServerConfig").then((function(t){e.deviceIps=t.deviceIp.split(","),e.defaultPlatform={id:null,enable:!0,ptz:!0,rtcp:!1,asMessageChannel:!1,autoPushChannel:!1,name:null,serverGBId:null,serverGBDomain:null,serverIp:null,serverPort:null,deviceGBId:t.username,deviceIp:e.deviceIps[0],devicePort:t.devicePort,username:t.username,password:t.password,expires:3600,keepTimeout:60,transport:"UDP",characterSet:"GB2312",startOfflinePush:!1,customGroup:!1,catalogWithPlatform:0,catalogWithGroup:0,catalogWithRegion:0,manufacturer:null,model:null,address:null,secrecy:1,catalogGroup:1,civilCode:null,sendStreamIp:t.sendStreamIp}})),this.getPlatformList()},currentChange:function(e){this.currentPage=e,this.getPlatformList()},handleSizeChange:function(e){this.count=e,this.getPlatformList()},getPlatformList:function(){var e=this;this.$store.dispatch("platform/query",{count:this.count,page:this.currentPage,query:this.searchSrt}).then((function(t){e.total=t.total,e.platformList=t.list})).catch((function(e){console.log(e)}))},refresh:function(){this.initData()}}},L=D,z=(a("5f53"),Object(m["a"])(L,l,n,!1,null,null,null));t["default"]=z.exports},e266:function(e,t,a){}}]);