{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750429501489}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}