{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750431501057}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}