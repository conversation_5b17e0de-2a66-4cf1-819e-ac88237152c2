{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750430527680}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}