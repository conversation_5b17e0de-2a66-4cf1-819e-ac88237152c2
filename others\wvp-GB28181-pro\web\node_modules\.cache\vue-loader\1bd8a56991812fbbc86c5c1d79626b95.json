{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750424407219}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCBoMjY1d2ViIGZyb20gJy4uL2NvbW1vbi9oMjY1d2ViLnZ1ZScNCmltcG9ydCBWaWRlb1RpbWVsaW5lIGZyb20gJy4uL2NvbW1vbi9WaWRlb1RpbWVMaW5lL2luZGV4LnZ1ZScNCmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50Jw0KaW1wb3J0IHNjcmVlbmZ1bGwgZnJvbSAnc2NyZWVuZnVsbCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQ2xvdWRSZWNvcmREZXRhaWwnLA0KICBjb21wb25lbnRzOiB7DQogICAgaDI2NXdlYiwgVmlkZW9UaW1lbGluZQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzaG93U2lkZWJhcjogZmFsc2UsDQogICAgICBhcHA6IHRoaXMuJHJvdXRlLnBhcmFtcy5hcHAsDQogICAgICBzdHJlYW06IHRoaXMuJHJvdXRlLnBhcmFtcy5zdHJlYW0sDQogICAgICBtZWRpYVNlcnZlcklkOiBudWxsLA0KICAgICAgZGF0ZUZpbGVzT2JqOiBbXSwNCiAgICAgIG1lZGlhU2VydmVyTGlzdDogW10sDQogICAgICBkZXRhaWxGaWxlczogW10sDQogICAgICB2aWRlb1VybDogbnVsbCwNCiAgICAgIHN0cmVhbUluZm86IG51bGwsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGNob29zZURhdGU6IG51bGwsDQogICAgICBwbGF5VGltZTogbnVsbCwNCiAgICAgIHBsYXllclRpbWU6IG51bGwsDQogICAgICBwbGF5U3BlZWQ6IDEsDQogICAgICBjaG9vc2VGaWxlSW5kZXg6IG51bGwsDQogICAgICBxdWVyeURhdGU6IG5ldyBEYXRlKCksDQogICAgICBjdXJyZW50UGFnZTogMSwNCiAgICAgIGNvdW50OiAxMDAwMDAwLCAvLyBUT0RPIOWIhumhteWvvOiHtOa7kei9qOinhumikeacieaViOWAvOaXoOazleiOt+WPluWujOWFqA0KICAgICAgdG90YWw6IDAsDQogICAgICBwbGF5TG9hZGluZzogZmFsc2UsDQogICAgICBzaG93VGltZTogdHJ1ZSwNCiAgICAgIGlzRnVsbFNjcmVlbjogZmFsc2UsDQogICAgICBwbGF5U2Vla1ZhbHVlOiAwLA0KICAgICAgcGxheWluZzogZmFsc2UsDQogICAgICB0YXNrVGltZVJhbmdlOiBbXSwNCiAgICAgIHRpbWVGb3JtYXQ6ICcwMDowMDowMCcsDQogICAgICBpbml0VGltZTogbnVsbCwNCiAgICAgIHRpbWVsaW5lQ29udHJvbDogZmFsc2UsDQogICAgICBzaG93T3RoZXJTcGVlZDogdHJ1ZSwNCiAgICAgIHRpbWVTZWdtZW50czogW10sDQogICAgICBzZWVrVGltZXI6IG51bGwsIC8vIHNlZWvpmLLmipblrprml7blmagNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgY2VsbENsYXNzTmFtZTogKGRhdGUpID0+IHsNCiAgICAgICAgICAvLyDpgJrov4fmmL7npLrkuIDkuKrngrnmoIfor4bov5nkuIDlpKnmnInlvZXlg48NCiAgICAgICAgICBjb25zdCB0aW1lID0gbW9tZW50KGRhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCcpDQogICAgICAgICAgaWYgKHRoaXMuZGF0ZUZpbGVzT2JqW3RpbWVdKSB7DQogICAgICAgICAgICByZXR1cm4gJ2RhdGEtcGlja2VyLXRydWUnDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJldHVybiAnZGF0YS1waWNrZXItZmFsc2UnDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgcGxheVNwZWVkUmFuZ2U6IFsxLCAyLCA0LCA2LCA4LCAxNl0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgYm94U3R5bGUoKSB7DQogICAgICBpZiAodGhpcy5zaG93U2lkZWJhcikgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGRpc3BsYXk6ICdncmlkJywNCiAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiAnMjEwcHggbWlubWF4KDAsIDFmciknDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgZGlzcGxheTogJ2dyaWQnLCBncmlkVGVtcGxhdGVDb2x1bW5zOiAnMCBtaW5tYXgoMCwgMWZyKScNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd1RpbWVWYWx1ZSgpIHsNCiAgICAgIHJldHVybiBtb21lbnQodGhpcy5wbGF5VGltZSkuZm9ybWF0KCdZWVlZLU1NLUREIEhIOm1tOnNzJykNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy8g5p+l6K+i5b2T5bm05pyJ6KeG6aKR55qE5pel5pyfDQogICAgdGhpcy5nZXREYXRlSW5ZZWFyKCgpID0+IHsNCiAgICAgIGlmIChPYmplY3QudmFsdWVzKHRoaXMuZGF0ZUZpbGVzT2JqKS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuY2hvb3NlRGF0ZSA9IE9iamVjdC52YWx1ZXModGhpcy5kYXRlRmlsZXNPYmopW09iamVjdC52YWx1ZXModGhpcy5kYXRlRmlsZXNPYmopLmxlbmd0aCAtIDFdDQogICAgICAgIHRoaXMuZGF0ZUNoYW5nZSgpDQogICAgICB9DQogICAgfSkNCiAgfSwNCiAgZGVzdHJveWVkKCkgew0KICAgIHRoaXMuJGRlc3Ryb3koJ3JlY29yZFZpZGVvUGxheWVyJykNCiAgICAvLyDmuIXnkIblrprml7blmagNCiAgICBpZiAodGhpcy5zZWVrVGltZXIpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnNlZWtUaW1lcikNCiAgICAgIHRoaXMuc2Vla1RpbWVyID0gbnVsbA0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHNpZGViYXJDb250cm9sKCkgew0KICAgICAgdGhpcy5zaG93U2lkZWJhciA9ICF0aGlzLnNob3dTaWRlYmFyDQogICAgfSwNCiAgICBzbmFwKCkgew0KICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5zY3JlZW5zaG90KCkNCiAgICB9LA0KICAgIHBsYXlMYXN0KCkgew0KICAgICAgLy8g5pKt5pS+5LiK5LiA5LiqDQogICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggPT09IDApIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmNob29zZUZpbGUodGhpcy5jaG9vc2VGaWxlSW5kZXggLSAxKQ0KICAgIH0sDQogICAgcGxheU5leHQoKSB7DQogICAgICAvLyDmkq3mlL7kuIvkuIDkuKoNCiAgICAgIGlmICh0aGlzLmNob29zZUZpbGVJbmRleCA9PT0gdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGggLSAxKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5jaG9vc2VGaWxlKHRoaXMuY2hvb3NlRmlsZUluZGV4ICsgMSkNCiAgICB9LA0KICAgIGNoYW5nZVBsYXlTcGVlZChzcGVlZCkgew0KICAgICAgY29uc29sZS5sb2coc3BlZWQpDQogICAgICAvLyDlgI3pgJ/mkq3mlL4NCiAgICAgIHRoaXMucGxheVNwZWVkID0gc3BlZWQNCiAgICAgIGlmICh0aGlzLnN0cmVhbUluZm8pIHsNCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2Nsb3VkUmVjb3JkL3NwZWVkJywgew0KICAgICAgICAgIG1lZGlhU2VydmVySWQ6IHRoaXMuc3RyZWFtSW5mby5tZWRpYVNlcnZlcklkLA0KICAgICAgICAgIGFwcDogdGhpcy5zdHJlYW1JbmZvLmFwcCwNCiAgICAgICAgICBzdHJlYW06IHRoaXMuc3RyZWFtSW5mby5zdHJlYW0sDQogICAgICAgICAgc3BlZWQ6IHRoaXMucGxheVNwZWVkLA0KICAgICAgICAgIHNjaGVtYTogJ3RzJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnNldFBsYXliYWNrUmF0ZSh0aGlzLnBsYXlTcGVlZCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCforr7nva7mkq3mlL7lgI3pgJ/ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICBzZWVrQmFja3dhcmQoKSB7DQogICAgICAvLyDlv6vpgIDkupTnp5INCiAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSAtPSA1ICogMTAwMA0KICAgICAgdGhpcy5wbGF5UmVjb3JkKCkNCiAgICB9LA0KICAgIHNlZWtGb3J3YXJkKCkgew0KICAgICAgLy8g5b+r6L+b5LqU56eSDQogICAgICB0aGlzLnBsYXlTZWVrVmFsdWUgKz0gNSAqIDEwMDANCiAgICAgIHRoaXMucGxheVJlY29yZCgpDQogICAgfSwNCiAgICBzdG9wUExheSgpIHsNCiAgICAgIC8vIOWBnOatog0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLmRlc3Ryb3koKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+WBnOatouaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgIH0NCiAgICAgIC8vIOmHjee9ruaSreaUvueKtuaAgeWSjOinhumikVVSTO+8jOehruS/neS4i+asoeaSreaUvuaXtumHjeaWsOWKoOi9vQ0KICAgICAgdGhpcy5wbGF5aW5nID0gZmFsc2UNCiAgICAgIHRoaXMudmlkZW9VcmwgPSBudWxsDQogICAgICB0aGlzLnBsYXlMb2FkaW5nID0gZmFsc2UNCg0KICAgICAgLy8g6YeN572u5Yiw56ys5LiA5Liq5paH5Lu25ZKM5Yid5aeL5pe26Ze0DQogICAgICB0aGlzLmNob29zZUZpbGVJbmRleCA9IDANCiAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSA9IDANCiAgICAgIHRoaXMucGxheWVyVGltZSA9IDANCiAgICAgIGlmICh0aGlzLmRldGFpbEZpbGVzICYmIHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnBsYXlUaW1lID0gdGhpcy5kZXRhaWxGaWxlc1swXS5zdGFydFRpbWUNCiAgICAgICAgLy8g5ZCM5q2l5pu05paw5pe26Ze06L205pi+56S65Yiw56ys5LiA5Liq5paH5Lu255qE5byA5aeL5pe26Ze0DQogICAgICAgIGlmICh0aGlzLiRyZWZzLlRpbWVsaW5lKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5UaW1lbGluZS5zZXRUaW1lKHRoaXMucGxheVRpbWUpDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5YGc5q2i5pe26YeN572u5YCN6YCf5Li6MVgNCiAgICAgIGlmICh0aGlzLnBsYXlTcGVlZCAhPT0gMSkgew0KICAgICAgICB0aGlzLmNoYW5nZVBsYXlTcGVlZCgxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgcGF1c2VQbGF5KCkgew0KICAgICAgLy8g5pqC5YGcDQogICAgICB0cnkgew0KICAgICAgICBpZiAodGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllcikgew0KICAgICAgICAgIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGF1c2UoKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+aaguWBnOaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KICAgIHBsYXkoKSB7DQogICAgICAvLyDmo4Dmn6Xmkq3mlL7lmajmmK/lkKblrZjlnKjkuJTlt7LliqDovb0NCiAgICAgIGlmICh0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmDQogICAgICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5sb2FkZWQgJiYNCiAgICAgICAgICAhdGhpcy5wbGF5TG9hZGluZykgew0KICAgICAgICAvLyDlsJ3or5XmgaLlpI3mkq3mlL4NCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnVuUGF1c2UoKQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIC8vIOW/veeVpUFib3J0RXJyb3LvvIzov5npgJrluLjmmK/nlLHkuo7lv6vpgJ/nmoRwbGF5L3BhdXNl5pON5L2c5a+86Ie055qEDQogICAgICAgICAgaWYgKGVycm9yLm5hbWUgIT09ICdBYm9ydEVycm9yJykgew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfmgaLlpI3mkq3mlL7lpLHotKXvvIzph43mlrDliqDovb3op4bpopE6JywgZXJyb3IpDQogICAgICAgICAgICB0aGlzLnBsYXlSZWNvcmQoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5pKt5pS+5Zmo5pyq5Yqg6L295oiW5bey6KKr6ZSA5q+B77yM6YeN5paw5Yqg6L296KeG6aKRDQogICAgICAgIHRoaXMucGxheVJlY29yZCgpDQogICAgICB9DQogICAgfSwNCiAgICBmdWxsU2NyZWVuKCkgew0KICAgICAgLy8g5YWo5bGPDQogICAgICBpZiAodGhpcy5pc0Z1bGxTY3JlZW4pIHsNCiAgICAgICAgc2NyZWVuZnVsbC5leGl0KCkNCiAgICAgICAgdGhpcy5pc0Z1bGxTY3JlZW4gPSBmYWxzZQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGNvbnN0IHBsYXllcldpZHRoID0gdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wbGF5ZXJXaWR0aA0KICAgICAgY29uc3QgcGxheWVySGVpZ2h0ID0gdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wbGF5ZXJIZWlnaHQNCiAgICAgIHNjcmVlbmZ1bGwucmVxdWVzdChkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncGxheWVyQm94JykpDQogICAgICBzY3JlZW5mdWxsLm9uKCdjaGFuZ2UnLCAoZXZlbnQpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5yZXNpemUocGxheWVyV2lkdGgsIHBsYXllckhlaWdodCkNCiAgICAgICAgdGhpcy5pc0Z1bGxTY3JlZW4gPSBzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbg0KICAgICAgfSkNCiAgICAgIHRoaXMuaXNGdWxsU2NyZWVuID0gdHJ1ZQ0KICAgIH0sDQogICAgZGF0ZUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuZGV0YWlsRmlsZXMgPSBbXQ0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDENCiAgICAgIGNvbnN0IGNob29zZUZ1bGxEYXRlID0gbmV3IERhdGUodGhpcy5jaG9vc2VEYXRlICsgJyAnICsgdGhpcy50aW1lRm9ybWF0KQ0KICAgICAgaWYgKGNob29zZUZ1bGxEYXRlLmdldEZ1bGxZZWFyKCkgIT09IHRoaXMucXVlcnlEYXRlLmdldEZ1bGxZZWFyKCkgfHwNCiAgICAgICAgY2hvb3NlRnVsbERhdGUuZ2V0TW9udGgoKSAhPT0gdGhpcy5xdWVyeURhdGUuZ2V0TW9udGgoKSkgew0KICAgICAgICB0aGlzLnF1ZXJ5RGF0ZSA9IGNob29zZUZ1bGxEYXRlDQogICAgICAgIHRoaXMuZ2V0RGF0ZUluWWVhcigpDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5UmVjb3JkRGV0YWlscygpDQogICAgfSwNCiAgICBpbmZpbml0ZVNjcm9sbCgpIHsNCiAgICAgIGlmICh0aGlzLnRvdGFsID4gdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50UGFnZSsrDQogICAgICAgIHRoaXMucXVlcnlSZWNvcmREZXRhaWxzKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIHF1ZXJ5UmVjb3JkRGV0YWlsczogZnVuY3Rpb24oY2FsbGJhY2spIHsNCiAgICAgIHRoaXMudGltZVNlZ21lbnRzID0gW10NCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdjbG91ZFJlY29yZC9xdWVyeUxpc3QnLCB7DQogICAgICAgIGFwcDogdGhpcy5hcHAsDQogICAgICAgIHN0cmVhbTogdGhpcy5zdHJlYW0sDQogICAgICAgIHN0YXJ0VGltZTogdGhpcy5jaG9vc2VEYXRlICsgJyAwMDowMDowMCcsDQogICAgICAgIGVuZFRpbWU6IHRoaXMuY2hvb3NlRGF0ZSArICcgMjM6NTk6NTknLA0KICAgICAgICBwYWdlOiB0aGlzLmN1cnJlbnRQYWdlLA0KICAgICAgICBjb3VudDogdGhpcy5jb3VudCwNCiAgICAgICAgbWVkaWFTZXJ2ZXJJZDogdGhpcy5tZWRpYVNlcnZlcklkLA0KICAgICAgICBhc2NPcmRlcjogdHJ1ZQ0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oZGF0YSA9PiB7DQogICAgICAgICAgdGhpcy50b3RhbCA9IGRhdGEudG90YWwNCiAgICAgICAgICB0aGlzLmRldGFpbEZpbGVzID0gdGhpcy5kZXRhaWxGaWxlcy5jb25jYXQoZGF0YS5saXN0KQ0KICAgICAgICAgIGNvbnN0IHRlbXAgPSBuZXcgU2V0KCkNCiAgICAgICAgICB0aGlzLmluaXRUaW1lID0gTnVtYmVyLnBhcnNlSW50KHRoaXMuZGV0YWlsRmlsZXNbMF0uc3RhcnRUaW1lKQ0KICAgICAgICAgIC8vIOWIneWni+WMluaSreaUvuaXtumXtOS4uuesrOS4gOS4quaWh+S7tueahOW8gOWni+aXtumXtA0KICAgICAgICAgIGlmICh0aGlzLnBsYXlUaW1lID09PSBudWxsKSB7DQogICAgICAgICAgICB0aGlzLnBsYXlUaW1lID0gdGhpcy5kZXRhaWxGaWxlc1swXS5zdGFydFRpbWUNCiAgICAgICAgICAgIHRoaXMucGxheWVyVGltZSA9IDANCiAgICAgICAgICB9DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmRldGFpbEZpbGVzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgICB0ZW1wLmFkZCh0aGlzLmRldGFpbEZpbGVzW2ldLm1lZGlhU2VydmVySWQpDQogICAgICAgICAgICB0aGlzLnRpbWVTZWdtZW50cy5wdXNoKHsNCiAgICAgICAgICAgICAgYmVnaW5UaW1lOiBOdW1iZXIucGFyc2VJbnQodGhpcy5kZXRhaWxGaWxlc1tpXS5zdGFydFRpbWUpLA0KICAgICAgICAgICAgICBlbmRUaW1lOiBOdW1iZXIucGFyc2VJbnQodGhpcy5kZXRhaWxGaWxlc1tpXS5lbmRUaW1lKSwNCiAgICAgICAgICAgICAgY29sb3I6ICcjMDE5MDFkJywNCiAgICAgICAgICAgICAgc3RhcnRSYXRpbzogMC43LA0KICAgICAgICAgICAgICBlbmRSYXRpbzogMC44NSwNCiAgICAgICAgICAgICAgaW5kZXg6IGkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMubWVkaWFTZXJ2ZXJMaXN0ID0gQXJyYXkuZnJvbSh0ZW1wKQ0KICAgICAgICAgIGlmICh0aGlzLm1lZGlhU2VydmVyTGlzdC5sZW5ndGggPT09IDEpIHsNCiAgICAgICAgICAgIHRoaXMubWVkaWFTZXJ2ZXJJZCA9IHRoaXMubWVkaWFTZXJ2ZXJMaXN0WzBdDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coZXJyb3IpDQogICAgICAgIH0pDQogICAgICAgIC5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIGlmIChjYWxsYmFjaykgY2FsbGJhY2soKQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgY2hvb3NlRmlsZShpbmRleCkgew0KICAgICAgdGhpcy5jaG9vc2VGaWxlSW5kZXggPSBpbmRleA0KICAgICAgLy8g6K6+572u5pKt5pS+5pe26Ze05Li66YCJ5Lit5paH5Lu255qE5byA5aeL5pe26Ze0DQogICAgICBjb25zdCBzZWxlY3RlZEZpbGUgPSB0aGlzLmRldGFpbEZpbGVzW2luZGV4XQ0KICAgICAgdGhpcy5wbGF5VGltZSA9IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUNCiAgICAgIHRoaXMucGxheWVyVGltZSA9IDAgLy8g6YeN572u5pKt5pS+5Zmo5pe26Ze0DQoNCiAgICAgIC8vIOiuoeeul+WIsOmAieS4reaWh+S7tueahHNlZWvlgLzvvIjntK/orqHliY3pnaLmiYDmnInmlofku7bnmoTml7bplb/vvIkNCiAgICAgIGxldCBzZWVrVmFsdWUgPSAwDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGluZGV4OyBpKyspIHsNCiAgICAgICAgc2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbaV0udGltZUxlbg0KICAgICAgfQ0KICAgICAgdGhpcy5wbGF5U2Vla1ZhbHVlID0gc2Vla1ZhbHVlDQoNCiAgICAgIC8vIOWQjOatpeabtOaWsOaXtumXtOi9tOaYvuekuuWIsOaWh+S7tuW8gOWni+aXtumXtA0KICAgICAgaWYgKHRoaXMuJHJlZnMuVGltZWxpbmUpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5UaW1lbGluZS5zZXRUaW1lKHRoaXMucGxheVRpbWUpDQogICAgICB9DQoNCiAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KGluZGV4KQ0KICAgIH0sDQogICAgcGxheVJlY29yZCgpIHsNCiAgICAgIC8vIOehruS/neaSreaUvuWZqOeKtuaAgeato+ehru+8jOWmguaenOayoeacieWcqOaSreaUvuWImemUgOavgemHjeW7ug0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgJiYgIXRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWluZykgew0KICAgICAgICAgIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIuZGVzdHJveSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign6ZSA5q+B5pKt5pS+5Zmo5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLnBsYXlMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2Nsb3VkUmVjb3JkL2xvYWRSZWNvcmQnLCB7DQogICAgICAgIGFwcDogdGhpcy5hcHAsDQogICAgICAgIHN0cmVhbTogdGhpcy5zdHJlYW0sDQogICAgICAgIGRhdGU6IHRoaXMuY2hvb3NlRGF0ZQ0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oZGF0YSA9PiB7DQogICAgICAgICAgdGhpcy5zdHJlYW1JbmZvID0gZGF0YQ0KICAgICAgICAgIGlmIChsb2NhdGlvbi5wcm90b2NvbCA9PT0gJ2h0dHBzOicpIHsNCiAgICAgICAgICAgIHRoaXMudmlkZW9VcmwgPSBkYXRhWydodHRwc19mbXA0J10gKyAnJnRpbWU9JyArIG5ldyBEYXRlKCkuZ2V0VGltZSgpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMudmlkZW9VcmwgPSBkYXRhWydmbXA0J10gKyAnJnRpbWU9JyArIG5ldyBEYXRlKCkuZ2V0VGltZSgpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuc2Vla1JlY29yZCgpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnJvcikNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMucGxheUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgcGxheVJlY29yZEJ5RmlsZUluZGV4KGZpbGVJbmRleCkgew0KICAgICAgY29uc29sZS5sb2coJ+aSreaUvuaMh+WumuaWh+S7tue0ouW8lTonLCBmaWxlSW5kZXgsICfvvIxzZWVr5YC8OicsIHRoaXMucGxheVNlZWtWYWx1ZSkNCiAgICAgIC8vIOehruS/neaSreaUvuWZqOeKtuaAgeato+ehru+8jOWmguaenOayoeacieWcqOaSreaUvuWImemUgOavgemHjeW7ug0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgJiYgIXRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWluZykgew0KICAgICAgICAgIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIuZGVzdHJveSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign6ZSA5q+B5pKt5pS+5Zmo5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLnBsYXlMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2Nsb3VkUmVjb3JkL2xvYWRSZWNvcmRCeUZpbGVJbmRleCcsIHsNCiAgICAgICAgYXBwOiB0aGlzLmFwcCwNCiAgICAgICAgc3RyZWFtOiB0aGlzLnN0cmVhbSwNCiAgICAgICAgZGF0ZTogdGhpcy5jaG9vc2VEYXRlLA0KICAgICAgICBmaWxlSW5kZXg6IGZpbGVJbmRleA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oZGF0YSA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+WKoOi9veaWh+S7tuaIkOWKnzonLCBkYXRhKQ0KICAgICAgICAgIHRoaXMuc3RyZWFtSW5mbyA9IGRhdGENCiAgICAgICAgICBpZiAobG9jYXRpb24ucHJvdG9jb2wgPT09ICdodHRwczonKSB7DQogICAgICAgICAgICB0aGlzLnZpZGVvVXJsID0gZGF0YVsnaHR0cHNfZm1wNCddICsgJyZ0aW1lPScgKyBuZXcgRGF0ZSgpLmdldFRpbWUoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLnZpZGVvVXJsID0gZGF0YVsnZm1wNCddICsgJyZ0aW1lPScgKyBuZXcgRGF0ZSgpLmdldFRpbWUoKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOabtOaWsOaSreaUvuaXtumXtOeKtuaAgQ0KICAgICAgICAgIGlmICh0aGlzLmRldGFpbEZpbGVzW2ZpbGVJbmRleF0pIHsNCiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IHRoaXMuZGV0YWlsRmlsZXNbZmlsZUluZGV4XQ0KICAgICAgICAgICAgLy8g6K6h566X5paH5Lu25YaF55qE5YGP56e75pe26Ze0DQogICAgICAgICAgICBsZXQgYmFzZVNlZWtWYWx1ZSA9IDANCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmlsZUluZGV4OyBpKyspIHsNCiAgICAgICAgICAgICAgYmFzZVNlZWtWYWx1ZSArPSB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnN0IG9mZnNldEluRmlsZSA9IHRoaXMucGxheVNlZWtWYWx1ZSAtIGJhc2VTZWVrVmFsdWUNCiAgICAgICAgICAgIHRoaXMucGxheVRpbWUgPSBzZWxlY3RlZEZpbGUuc3RhcnRUaW1lICsgb2Zmc2V0SW5GaWxlDQogICAgICAgICAgICB0aGlzLnBsYXllclRpbWUgPSBvZmZzZXRJbkZpbGUNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpoLmnpzmnIlzZWVr5YC877yM5YiZ6L+b6KGMc2Vla+WumuS9jQ0KICAgICAgICAgIGlmICh0aGlzLnBsYXlTZWVrVmFsdWUgPiAwKSB7DQogICAgICAgICAgICAvLyDorqHnrpflvZPliY3mlofku7blhoXnmoTnm7jlr7lzZWVr5YC8DQogICAgICAgICAgICBsZXQgYmFzZVNlZWtWYWx1ZSA9IDANCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmlsZUluZGV4OyBpKyspIHsNCiAgICAgICAgICAgICAgYmFzZVNlZWtWYWx1ZSArPSB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnN0IGZpbGVTZWVrVmFsdWUgPSB0aGlzLnBsYXlTZWVrVmFsdWUgLSBiYXNlU2Vla1ZhbHVlDQoNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmiafooYxzZWVr5a6a5L2NIC0g5YWo5bGAc2Vla+WAvDonLCB0aGlzLnBsYXlTZWVrVmFsdWUsICfvvIzmlofku7blhoVzZWVr5YC8OicsIGZpbGVTZWVrVmFsdWUpDQoNCiAgICAgICAgICAgIC8vIOajgOafpXNlZWvlgLzmmK/lkKblnKjlvZPliY3mlofku7bojIPlm7TlhoUNCiAgICAgICAgICAgIGlmIChmaWxlU2Vla1ZhbHVlID49IDAgJiYgZmlsZVNlZWtWYWx1ZSA8PSB0aGlzLmRldGFpbEZpbGVzW2ZpbGVJbmRleF0udGltZUxlbikgew0KICAgICAgICAgICAgICB0aGlzLnNlZWtSZWNvcmQoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdzZWVr5YC86LaF5Ye65b2T5YmN5paH5Lu26IyD5Zu077yM6Lez6L+Hc2Vla+aTjeS9nCAtIGZpbGVTZWVrVmFsdWU6JywgZmlsZVNlZWtWYWx1ZSwgJ++8jOaWh+S7tuaXtumVvzonLCB0aGlzLmRldGFpbEZpbGVzW2ZpbGVJbmRleF0udGltZUxlbikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5Yqg6L295paH5Lu25aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB9KQ0KICAgICAgICAuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgdGhpcy5wbGF5TG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBzZWVrUmVjb3JkKCkgew0KICAgICAgLy8g5qOA5p+l5pKt5pS+5Zmo5ZKM5rWB5L+h5oGv5piv5ZCm5YeG5aSH5aW9DQogICAgICBpZiAoIXRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgfHwgIXRoaXMuc3RyZWFtSW5mbykgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+aSreaUvuWZqOaIlua1geS/oeaBr+acquWHhuWkh+Wlve+8jOi3s+i/h3NlZWvmk43kvZwnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g6Ziy5oqW5aSE55CG77ya5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoDQogICAgICBpZiAodGhpcy5zZWVrVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuc2Vla1RpbWVyKQ0KICAgICAgfQ0KDQogICAgICAvLyDlu7bov5/miafooYxzZWVr77yM6YG/5YWN6aKR57mB5pON5L2cDQogICAgICB0aGlzLnNlZWtUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLmRvU2Vla1JlY29yZCgpDQogICAgICB9LCAzMDApIC8vIDMwMG1z6Ziy5oqWDQogICAgfSwNCiAgICBhc3luYyBkb1NlZWtSZWNvcmQoKSB7DQogICAgICAvLyDlho3mrKHmo4Dmn6XnirbmgIENCiAgICAgIGlmICghdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllciB8fCAhdGhpcy5zdHJlYW1JbmZvKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5oiW5rWB5L+h5oGv5pyq5YeG5aSH5aW977yM5Y+W5raIc2Vla+aTjeS9nCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDorqHnrpflvZPliY3mlofku7blhoXnmoTnm7jlr7lzZWVr5YC8DQogICAgICBsZXQgYmFzZVNlZWtWYWx1ZSA9IDANCiAgICAgIGlmICh0aGlzLmNob29zZUZpbGVJbmRleCAhPT0gbnVsbCkgew0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY2hvb3NlRmlsZUluZGV4OyBpKyspIHsNCiAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbaV0udGltZUxlbg0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOW9k+WJjeaWh+S7tuWGheeahHNlZWvlgLzvvIjmr6vnp5LvvIkNCiAgICAgIGNvbnN0IGZpbGVTZWVrVmFsdWUgPSB0aGlzLnBsYXlTZWVrVmFsdWUgLSBiYXNlU2Vla1ZhbHVlDQoNCiAgICAgIGNvbnNvbGUubG9nKCfmiafooYxzZWVr5a6a5L2NIC0g5YWo5bGAc2Vla+WAvDonLCB0aGlzLnBsYXlTZWVrVmFsdWUsICdtc++8jOaWh+S7tuWGhXNlZWvlgLw6JywgZmlsZVNlZWtWYWx1ZSwgJ21zJykNCg0KICAgICAgLy8g6aqM6K+Bc2Vla+WAvOaYr+WQpuWcqOWQiOeQhuiMg+WbtOWGhQ0KICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsICYmIHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdKSB7DQogICAgICAgIGNvbnN0IGN1cnJlbnRGaWxlID0gdGhpcy5kZXRhaWxGaWxlc1t0aGlzLmNob29zZUZpbGVJbmRleF0NCiAgICAgICAgaWYgKGZpbGVTZWVrVmFsdWUgPCAwIHx8IGZpbGVTZWVrVmFsdWUgPiBjdXJyZW50RmlsZS50aW1lTGVuKSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCdzZWVr5YC86LaF5Ye65b2T5YmN5paH5Lu26IyD5Zu077yM6LCD5pW05Yiw5paH5Lu26L6555WMIC0gZmlsZVNlZWtWYWx1ZTonLCBmaWxlU2Vla1ZhbHVlLCAn77yM5paH5Lu25pe26ZW/OicsIGN1cnJlbnRGaWxlLnRpbWVMZW4pDQogICAgICAgICAgLy8g6LCD5pW05Yiw5paH5Lu26L6555WMDQogICAgICAgICAgY29uc3QgYWRqdXN0ZWRTZWVrVmFsdWUgPSBNYXRoLm1heCgwLCBNYXRoLm1pbihmaWxlU2Vla1ZhbHVlLCBjdXJyZW50RmlsZS50aW1lTGVuKSkNCiAgICAgICAgICB0aGlzLnBsYXlTZWVrVmFsdWUgPSBiYXNlU2Vla1ZhbHVlICsgYWRqdXN0ZWRTZWVrVmFsdWUNCiAgICAgICAgICBjb25zb2xlLmxvZygn6LCD5pW05ZCO55qEc2Vla+WAvDonLCB0aGlzLnBsYXlTZWVrVmFsdWUpDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g6K6w5b2V5pKt5pS+54q25oCB77yM55So5LqOc2Vla+WQjuaBouWkjQ0KICAgICAgY29uc3Qgd2FzUGxheWluZyA9IHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWluZw0KDQogICAgICAvLyDmmoLlgZzmkq3mlL7lmajvvIzpgb/lhY1zZWVr5pe255qE54q25oCB5Yay56qBDQogICAgICBpZiAod2FzUGxheWluZyAmJiB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBhdXNlKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wYXVzZSgpDQogICAgICAgICAgLy8g57uZ5pqC5YGc5pON5L2c5LiA54K55pe26Ze05a6M5oiQDQogICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmmoLlgZzmkq3mlL7lmajml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g6YeN5paw6K6h566X5pyA57uI55qE5paH5Lu25YaFc2Vla+WAvO+8iOWPr+iDveW3sue7j+iiq+iwg+aVtOi/h++8iQ0KICAgICAgY29uc3QgZmluYWxGaWxlU2Vla1ZhbHVlID0gdGhpcy5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZQ0KDQogICAgICAvLyDlhbPplK7kv67lpI3vvJrlr7nkuo7mjInmlofku7bntKLlvJXliqDovb3nmoTmtYHvvIxaTE1lZGlhS2l05pyf5pyb55qE5piv5paH5Lu25YaF55qE55u45a+55pe26Ze0DQogICAgICAvLyDkvYbmmK/lr7nkuo7nrKzkuIDkuKrmlofku7bvvIjntKLlvJUw77yJ77yM5aaC5p6c5piv6YCa6L+HbG9hZFJlY29yZOWKoOi9veeahO+8jOWPr+iDveacn+acm+WFqOWxgOaXtumXtA0KICAgICAgbGV0IHNlZWtWYWx1ZVRvU2VuZCA9IGZpbmFsRmlsZVNlZWtWYWx1ZQ0KDQogICAgICAvLyDmo4Dmn6XmtYHlkI3np7DmmK/lkKbljIXlkKvmlofku7bntKLlvJXvvIhfMCwgXzEsIF8y562J77yJDQogICAgICBpZiAodGhpcy5zdHJlYW1JbmZvLnN0cmVhbSAmJiB0aGlzLnN0cmVhbUluZm8uc3RyZWFtLmluY2x1ZGVzKCdfJyArIHRoaXMuY2hvb3NlRmlsZUluZGV4KSkgew0KICAgICAgICAvLyDov5nmmK/mjInmlofku7bntKLlvJXliqDovb3nmoTmtYHvvIzkvb/nlKjmlofku7blhoXnm7jlr7nml7bpl7QNCiAgICAgICAgc2Vla1ZhbHVlVG9TZW5kID0gZmluYWxGaWxlU2Vla1ZhbHVlDQogICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDmjInmlofku7bntKLlvJXliqDovb3nmoTmtYHvvIzkvb/nlKjmlofku7blhoVzZWVr5YC8OicsIHNlZWtWYWx1ZVRvU2VuZCwgJ21zJykNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOi/meWPr+iDveaYr+aVtOS9k+W9leWDj+a1ge+8jOS9v+eUqOWFqOWxgOaXtumXtA0KICAgICAgICBzZWVrVmFsdWVUb1NlbmQgPSB0aGlzLnBsYXlTZWVrVmFsdWUNCiAgICAgICAgY29uc29sZS5sb2coJ+ajgOa1i+WIsOaVtOS9k+W9leWDj+a1ge+8jOS9v+eUqOWFqOWxgHNlZWvlgLw6Jywgc2Vla1ZhbHVlVG9TZW5kLCAnbXMnKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvc2VlaycsIHsNCiAgICAgICAgbWVkaWFTZXJ2ZXJJZDogdGhpcy5zdHJlYW1JbmZvLm1lZGlhU2VydmVySWQsDQogICAgICAgIGFwcDogdGhpcy5zdHJlYW1JbmZvLmFwcCwNCiAgICAgICAgc3RyZWFtOiB0aGlzLnN0cmVhbUluZm8uc3RyZWFtLA0KICAgICAgICBzZWVrOiBzZWVrVmFsdWVUb1NlbmQsDQogICAgICAgIHNjaGVtYTogJ2ZtcDQnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+WQjuerr3NlZWvmk43kvZzmiJDlip8gLSDlj5HpgIHnmoRzZWVr5YC8OicsIHNlZWtWYWx1ZVRvU2VuZCwgJ21zJykNCg0KICAgICAgICAgIC8vIOWQjuerr3NlZWvmiJDlip/lkI7vvIzlkIzmraXliY3nq6/mkq3mlL7lmagNCiAgICAgICAgICB0aGlzLnN5bmNQbGF5ZXJTZWVrKHdhc1BsYXlpbmcpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICAvLyDpnZnpu5jlpITnkIZzZWVr6ZSZ6K+v77yM5LiN5b2x5ZON55So5oi35L2T6aqMDQogICAgICAgICAgY29uc29sZS53YXJuKCdzZWVr5pON5L2c5aSx6LSlOicsIGVycm9yKQ0KDQogICAgICAgICAgLy8g5Y2z5L2/5ZCO56uvc2Vla+Wksei0pe+8jOS5n+WwneivleWJjeerr3NlZWsNCiAgICAgICAgICB0aGlzLnN5bmNQbGF5ZXJTZWVrKHdhc1BsYXlpbmcpDQogICAgICAgIH0pDQogICAgfSwNCiAgICBzeW5jUGxheWVyU2Vlayh3YXNQbGF5aW5nKSB7DQogICAgICAvLyDorqHnrpfmkq3mlL7lmajpnIDopoFzZWVr5Yiw55qE5pe26Ze077yI56eS77yJDQogICAgICAvLyBwbGF5U2Vla1ZhbHVl5piv5LuO5b2V5YOP5byA5aeL55qE5q+r56eS5pWw77yM6ZyA6KaB6L2s5o2i5Li65b2T5YmN5paH5Lu25YaF55qE56eS5pWwDQogICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggIT09IG51bGwgJiYgdGhpcy5kZXRhaWxGaWxlc1t0aGlzLmNob29zZUZpbGVJbmRleF0pIHsNCiAgICAgICAgbGV0IGJhc2VTZWVrVmFsdWUgPSAwDQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5jaG9vc2VGaWxlSW5kZXg7IGkrKykgew0KICAgICAgICAgIGJhc2VTZWVrVmFsdWUgKz0gdGhpcy5kZXRhaWxGaWxlc1tpXS50aW1lTGVuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDorqHnrpflnKjlvZPliY3mlofku7blhoXnmoTlgY/np7vml7bpl7TvvIjmr6vnp5LvvIkNCiAgICAgICAgY29uc3Qgb2Zmc2V0SW5GaWxlID0gdGhpcy5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZQ0KICAgICAgICAvLyDovazmjaLkuLrnp5INCiAgICAgICAgY29uc3Qgc2Vla1RpbWVJblNlY29uZHMgPSBvZmZzZXRJbkZpbGUgLyAxMDAwDQoNCiAgICAgICAgY29uc29sZS5sb2coJ+WJjeerr+aSreaUvuWZqHNlZWvliLA6Jywgc2Vla1RpbWVJblNlY29uZHMsICfnp5LvvIjmlofku7blhoXlgY/np7vvvIknKQ0KDQogICAgICAgIC8vIOeri+WNs+abtOaWsOaYvuekuuaXtumXtO+8jOS4jeetieW+heaSreaUvuWZqOWbnuiwgw0KICAgICAgICB0aGlzLnVwZGF0ZURpc3BsYXlUaW1lKCkNCg0KICAgICAgICAvLyDlu7bov5/kuIDngrnml7bpl7TvvIznoa7kv53lkI7nq69zZWVr5pON5L2c5a6M5oiQDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vliY3nq6/mkq3mlL7lmahzZWVr5pON5L2cJykNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo54q25oCB5qOA5p+lOicsIHsNCiAgICAgICAgICAgIHBsYXllckV4aXN0czogISF0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLA0KICAgICAgICAgICAgc2Vla01ldGhvZEV4aXN0czogISEodGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllciAmJiB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnNlZWspLA0KICAgICAgICAgICAgcGxheWVyTG9hZGVkOiAhISh0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIubG9hZGVkKSwNCiAgICAgICAgICAgIHBsYXlpbmc6ICEhKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgJiYgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wbGF5aW5nKQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICBpZiAodGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllciAmJiB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnNlZWspIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfosIPnlKjmkq3mlL7lmahzZWVr5pa55rOV77yM55uu5qCH5pe26Ze0OicsIHNlZWtUaW1lSW5TZWNvbmRzLCAn56eSJykNCiAgICAgICAgICAgIGNvbnN0IHNlZWtTdWNjZXNzID0gdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5zZWVrKHNlZWtUaW1lSW5TZWNvbmRzKQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqHNlZWvmlrnms5Xov5Tlm57lgLw6Jywgc2Vla1N1Y2Nlc3MpDQoNCiAgICAgICAgICAgIC8vIHNlZWvmiJDlip/lkI7lho3mrKHmm7TmlrDmmL7npLrml7bpl7QNCiAgICAgICAgICAgIGlmIChzZWVrU3VjY2Vzcykgew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5YmN56uv5pKt5pS+5Zmoc2Vla+aIkOWKnycpDQogICAgICAgICAgICAgIC8vIOW7tui/n+S4gOeCueaXtumXtOWGjeabtOaWsO+8jOehruS/neaSreaUvuWZqOWGhemDqOeKtuaAgeW3suabtOaWsA0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLnVwZGF0ZURpc3BsYXlUaW1lKCkNCiAgICAgICAgICAgICAgfSwgMTAwKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfliY3nq6/mkq3mlL7lmahzZWVr5aSx6LSl77yM5bCd6K+V6YeN5paw5Yqg6L295pKt5pS+5ZmoJykNCiAgICAgICAgICAgICAgLy8g5aaC5p6cc2Vla+Wksei0pe+8jOWwneivlemHjeaWsOWKoOi9veaSreaUvuWZqOWIsOebruagh+S9jee9rg0KICAgICAgICAgICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggIT09IG51bGwpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnBsYXlSZWNvcmRCeUZpbGVJbmRleCh0aGlzLmNob29zZUZpbGVJbmRleCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDmgaLlpI3mkq3mlL7nirbmgIENCiAgICAgICAgICAgIGlmICh3YXNQbGF5aW5nKSB7DQogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmICF0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBsYXlpbmcpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmgaLlpI3mkq3mlL7nirbmgIEnKQ0KICAgICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci51blBhdXNlKCkNCiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpUFib3J0RXJyb3LvvIzov5npgJrluLjmmK/nlLHkuo7lv6vpgJ/nmoRwbGF5L3BhdXNl5pON5L2c5a+86Ie055qEDQogICAgICAgICAgICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsNCiAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+aBouWkjeaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcikNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSwgNTAwKSAvLyDlop7liqDlu7bov5/ml7bpl7TvvIznoa7kv51zZWVr5pON5L2c5a6M5YWo5a6M5oiQDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5LiN5pSv5oyBc2Vla+aTjeS9nO+8jOWwneivlemHjeaWsOWKoOi9veaSreaUvuWZqCcpDQogICAgICAgICAgICAvLyDlpoLmnpzkuI3mlK/mjIFzZWVr77yM6YeN5paw5Yqg6L295pKt5pS+5Zmo5Yiw55uu5qCH5L2N572uDQogICAgICAgICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggIT09IG51bGwpIHsNCiAgICAgICAgICAgICAgdGhpcy5wbGF5UmVjb3JkQnlGaWxlSW5kZXgodGhpcy5jaG9vc2VGaWxlSW5kZXgpDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8vIOWmguaenOS4jeaUr+aMgXNlZWvvvIzoh7PlsJHmgaLlpI3mkq3mlL7nirbmgIENCiAgICAgICAgICAgIGlmICh3YXNQbGF5aW5nKSB7DQogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmICF0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBsYXlpbmcpIHsNCiAgICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIudW5QYXVzZSgpDQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgICAgICAvLyDlv73nlaVBYm9ydEVycm9y77yM6L+Z6YCa5bi45piv55Sx5LqO5b+r6YCf55qEcGxheS9wYXVzZeaTjeS9nOWvvOiHtOeahA0KICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IubmFtZSAhPT0gJ0Fib3J0RXJyb3InKSB7DQogICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfmgaLlpI3mkq3mlL7ml7blh7rnjrDplJnor686JywgZXJyb3IpDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0sIDUwMCkgLy8g5aKe5Yqg5bu26L+f5pe26Ze077yM56Gu5L+d5pON5L2c5a6M5YWo5a6M5oiQDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9LCA4MDApIC8vIOWinuWKoOW7tui/n+aXtumXtO+8jOe7meWQjuerr3NlZWvmk43kvZzmm7TlpJrml7bpl7QNCiAgICAgIH0NCiAgICB9LA0KICAgIHVwZGF0ZURpc3BsYXlUaW1lKCkgew0KICAgICAgLy8g5omL5Yqo5pu05paw5pi+56S65pe26Ze077yM56Gu5L+dc2Vla+WQjuaXtumXtOaYvuekuuato+ehrg0KICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsICYmIHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdKSB7DQogICAgICAgIGNvbnN0IHNlbGVjdGVkRmlsZSA9IHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdDQoNCiAgICAgICAgLy8g6K6h566X5b2T5YmN5paH5Lu255qE5Z+656GAc2Vla+WAvA0KICAgICAgICBsZXQgYmFzZVNlZWtWYWx1ZSA9IDANCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmNob29zZUZpbGVJbmRleDsgaSsrKSB7DQogICAgICAgICAgYmFzZVNlZWtWYWx1ZSArPSB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOiuoeeul+WcqOW9k+WJjeaWh+S7tuWGheeahOWBj+enu+aXtumXtO+8iOavq+enku+8iQ0KICAgICAgICBjb25zdCBvZmZzZXRJbkZpbGUgPSB0aGlzLnBsYXlTZWVrVmFsdWUgLSBiYXNlU2Vla1ZhbHVlDQoNCiAgICAgICAgLy8g5pu05pawcGxheVRpbWXkuLrnm67moIfml7bpl7QNCiAgICAgICAgdGhpcy5wbGF5VGltZSA9IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgKyBvZmZzZXRJbkZpbGUNCg0KICAgICAgICAvLyDlkIzml7bmm7TmlrBwbGF5ZXJUaW1lDQogICAgICAgIHRoaXMucGxheWVyVGltZSA9IG9mZnNldEluRmlsZQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCfmiYvliqjmm7TmlrDmmL7npLrml7bpl7Q6Jywgew0KICAgICAgICAgIHBsYXlUaW1lOiB0aGlzLnBsYXlUaW1lLA0KICAgICAgICAgIHBsYXllclRpbWU6IHRoaXMucGxheWVyVGltZSwNCiAgICAgICAgICBvZmZzZXRJbkZpbGU6IG9mZnNldEluRmlsZSwNCiAgICAgICAgICBzZWxlY3RlZEZpbGVTdGFydFRpbWU6IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGRvd25sb2FkRmlsZShmaWxlKSB7DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvZ2V0UGxheVBhdGgnLCBmaWxlLmlkKQ0KICAgICAgICAudGhlbihkYXRhID0+IHsNCiAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpDQogICAgICAgICAgbGluay50YXJnZXQgPSAnX2JsYW5rJw0KICAgICAgICAgIGlmIChsb2NhdGlvbi5wcm90b2NvbCA9PT0gJ2h0dHBzOicpIHsNCiAgICAgICAgICAgIGxpbmsuaHJlZiA9IGRhdGEuaHR0cHNQYXRoICsgJyZzYXZlX25hbWU9JyArIGZpbGUuZmlsZU5hbWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgbGluay5ocmVmID0gZGF0YS5odHRwUGF0aCArICcmc2F2ZV9uYW1lPScgKyBmaWxlLmZpbGVOYW1lDQogICAgICAgICAgfQ0KICAgICAgICAgIGxpbmsuY2xpY2soKQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coZXJyb3IpDQogICAgICAgIH0pDQogICAgfSwNCiAgICBiYWNrVG9MaXN0KCkgew0KICAgICAgdGhpcy4kcm91dGVyLmJhY2soKQ0KICAgIH0sDQogICAgZ2V0RmlsZVNob3dOYW1lKGl0ZW0pIHsNCiAgICAgIHJldHVybiBtb21lbnQoaXRlbS5zdGFydFRpbWUpLmZvcm1hdCgnSEg6bW06c3MnKSArICctJyArIG1vbWVudChpdGVtLmVuZFRpbWUpLmZvcm1hdCgnSEg6bW06c3MnKQ0KICAgIH0sDQoNCiAgICBzaG93UGxheVRpbWVDaGFuZ2UodmFsKSB7DQogICAgICAvLyB2YWzmmK/mkq3mlL7lmajnmoTlvZPliY3mkq3mlL7ml7bpl7TvvIjnp5LvvInvvIzpnIDopoHovazmjaLkuLrnu53lr7nml7bpl7TmiLMNCiAgICAgIGlmICh0aGlzLmNob29zZUZpbGVJbmRleCAhPT0gbnVsbCAmJiB0aGlzLmRldGFpbEZpbGVzW3RoaXMuY2hvb3NlRmlsZUluZGV4XSkgew0KICAgICAgICBjb25zdCBzZWxlY3RlZEZpbGUgPSB0aGlzLmRldGFpbEZpbGVzW3RoaXMuY2hvb3NlRmlsZUluZGV4XQ0KDQogICAgICAgIC8vIOiuoeeul+W9k+WJjeaSreaUvueahOe7neWvueaXtumXtO+8muaWh+S7tuW8gOWni+aXtumXtCArIOaSreaUvuWZqOW9k+WJjeaXtumXtA0KICAgICAgICBjb25zdCBuZXdQbGF5VGltZSA9IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgKyAodmFsICogMTAwMCkNCiAgICAgICAgY29uc3QgbmV3UGxheWVyVGltZSA9IHZhbCAqIDEwMDANCg0KICAgICAgICAvLyDlpoLmnpzmraPlnKjmi5bliqjml7bpl7TovbTvvIzlv73nlaXmkq3mlL7lmajnmoTml7bpl7Tlm57osIPvvIzpgb/lhY3lhrLnqoENCiAgICAgICAgaWYgKHRoaXMudGltZWxpbmVDb250cm9sKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+ato+WcqOaLluWKqOaXtumXtOi9tO+8jOW/veeVpeaSreaUvuWZqOaXtumXtOWbnuiwgycpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmm7TmlrDml7bpl7QNCiAgICAgICAgdGhpcy5wbGF5VGltZSA9IG5ld1BsYXlUaW1lDQogICAgICAgIHRoaXMucGxheWVyVGltZSA9IG5ld1BsYXllclRpbWUNCg0KICAgICAgICAvLyBjb25zb2xlLmxvZygn5pKt5pS+5Zmo5pe26Ze05pu05pawOicsIHsNCiAgICAgICAgLy8gICBwbGF5ZXJTZWNvbmRzOiB2YWwsDQogICAgICAgIC8vICAgcGxheVRpbWU6IHRoaXMucGxheVRpbWUsDQogICAgICAgIC8vICAgcGxheWVyVGltZTogdGhpcy5wbGF5ZXJUaW1lDQogICAgICAgIC8vIH0pDQogICAgICB9DQogICAgfSwNCiAgICBwbGF5aW5nQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5wbGF5aW5nID0gdmFsDQogICAgfSwNCiAgICBwbGF5VGltZUNoYW5nZSh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT09IHRoaXMucGxheVRpbWUpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLnBsYXlUaW1lID0gdmFsDQogICAgfSwNCiAgICB0aW1lbGluZU1vdXNlRG93bigpIHsNCiAgICAgIHRoaXMudGltZWxpbmVDb250cm9sID0gdHJ1ZQ0KICAgIH0sDQogICAgb25TZWVrRmluaXNoKCkgew0KICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqHNlZWvlrozmiJDlm57osIMnKQ0KICAgICAgLy8g5pKt5pS+5Zmoc2Vla+WujOaIkOWQju+8jOehruS/neaYvuekuuaXtumXtOato+ehrg0KICAgICAgdGhpcy51cGRhdGVEaXNwbGF5VGltZSgpDQogICAgfSwNCiAgICBtb3VzZXVwVGltZWxpbmUoKSB7DQogICAgICBpZiAoIXRoaXMudGltZWxpbmVDb250cm9sKSB7DQogICAgICAgIHRoaXMudGltZWxpbmVDb250cm9sID0gZmFsc2UNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLnRpbWVsaW5lQ29udHJvbCA9IGZhbHNlDQoNCiAgICAgIGNvbnNvbGUubG9nKCfml7bpl7TovbTmi5bliqjnu5PmnZ/vvIzlvZPliY3ml7bpl7Q6JywgdGhpcy5wbGF5VGltZSwgJ++8jOaWh+S7tuWIl+ihqOmVv+W6pjonLCB0aGlzLmRldGFpbEZpbGVzLmxlbmd0aCkNCg0KICAgICAgLy8g5p+l5om+5ouW5Yqo5pe26Ze054K55a+55bqU55qE5paH5Lu2DQogICAgICBsZXQgdGFyZ2V0RmlsZUluZGV4ID0gLTENCiAgICAgIGxldCB0aW1lT2Zmc2V0SW5GaWxlID0gMCAvLyDlnKjmlofku7blhoXnmoTml7bpl7TlgY/np7vvvIjmr6vnp5LvvIkNCg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmRldGFpbEZpbGVzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmRldGFpbEZpbGVzW2ldDQogICAgICAgIGNvbnNvbGUubG9nKGDmo4Dmn6Xmlofku7Yke2l9OiAke2l0ZW0uc3RhcnRUaW1lfSAtICR7aXRlbS5lbmRUaW1lfSwg5b2T5YmN5pe26Ze0OiAke3RoaXMucGxheVRpbWV9YCkNCiAgICAgICAgaWYgKHRoaXMucGxheVRpbWUgPj0gaXRlbS5zdGFydFRpbWUgJiYgdGhpcy5wbGF5VGltZSA8PSBpdGVtLmVuZFRpbWUpIHsNCiAgICAgICAgICB0YXJnZXRGaWxlSW5kZXggPSBpDQogICAgICAgICAgdGltZU9mZnNldEluRmlsZSA9IHRoaXMucGxheVRpbWUgLSBpdGVtLnN0YXJ0VGltZQ0KICAgICAgICAgIGNvbnNvbGUubG9nKGDmib7liLDnm67moIfmlofku7Yke2l977yM5paH5Lu25YaF5YGP56e777yaJHt0aW1lT2Zmc2V0SW5GaWxlfW1zYCkNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGlmICh0YXJnZXRGaWxlSW5kZXggPT09IC0xKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5ouW5Yqo5pe26Ze054K55LiN5Zyo5Lu75L2V5paH5Lu26IyD5Zu05YaF77yM5p+l5om+5pyA6L+R55qE5paH5Lu2JykNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw57K+56Gu5Yy56YWN77yM5p+l5om+5pyA6L+R55qE5paH5Lu2DQogICAgICAgIGxldCBtaW5EaXN0YW5jZSA9IEluZmluaXR5DQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmRldGFpbEZpbGVzW2ldDQogICAgICAgICAgY29uc3QgZGlzdGFuY2VUb1N0YXJ0ID0gTWF0aC5hYnModGhpcy5wbGF5VGltZSAtIGl0ZW0uc3RhcnRUaW1lKQ0KICAgICAgICAgIGNvbnN0IGRpc3RhbmNlVG9FbmQgPSBNYXRoLmFicyh0aGlzLnBsYXlUaW1lIC0gaXRlbS5lbmRUaW1lKQ0KICAgICAgICAgIGNvbnN0IG1pbkZpbGVEaXN0YW5jZSA9IE1hdGgubWluKGRpc3RhbmNlVG9TdGFydCwgZGlzdGFuY2VUb0VuZCkNCg0KICAgICAgICAgIGlmIChtaW5GaWxlRGlzdGFuY2UgPCBtaW5EaXN0YW5jZSkgew0KICAgICAgICAgICAgbWluRGlzdGFuY2UgPSBtaW5GaWxlRGlzdGFuY2UNCiAgICAgICAgICAgIHRhcmdldEZpbGVJbmRleCA9IGkNCiAgICAgICAgICAgIC8vIOWmguaenOabtOaOpei/keW8gOWni+aXtumXtO+8jOWBj+enu+S4ujDvvJvlpoLmnpzmm7TmjqXov5Hnu5PmnZ/ml7bpl7TvvIzlgY/np7vkuLrmlofku7bml7bplb8NCiAgICAgICAgICAgIHRpbWVPZmZzZXRJbkZpbGUgPSBkaXN0YW5jZVRvU3RhcnQgPCBkaXN0YW5jZVRvRW5kID8gMCA6IGl0ZW0udGltZUxlbg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0YXJnZXRGaWxlSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5peg5rOV5om+5Yiw5Lu75L2V5Y+v5pKt5pS+55qE5paH5Lu2JykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKGDkvb/nlKjmnIDov5HnmoTmlofku7Yke3RhcmdldEZpbGVJbmRleH3vvIzlgY/np7vvvJoke3RpbWVPZmZzZXRJbkZpbGV9bXNgKQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZyhg5ouW5Yqo5Yiw5paH5Lu2JHt0YXJnZXRGaWxlSW5kZXh977yM5pe26Ze05YGP56e777yaJHt0aW1lT2Zmc2V0SW5GaWxlfW1zYCkNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm6ZyA6KaB5YiH5o2i5paH5Lu2DQogICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggIT09IHRhcmdldEZpbGVJbmRleCkgew0KICAgICAgICBjb25zb2xlLmxvZyhg5YiH5o2i5paH5Lu277ya5LuOJHt0aGlzLmNob29zZUZpbGVJbmRleH3liLAke3RhcmdldEZpbGVJbmRleH1gKQ0KICAgICAgICAvLyDliIfmjaLliLDnm67moIfmlofku7YNCiAgICAgICAgdGhpcy5jaG9vc2VGaWxlSW5kZXggPSB0YXJnZXRGaWxlSW5kZXgNCg0KICAgICAgICAvLyDorqHnrpfliLDnm67moIfmlofku7bnmoRzZWVr5YC877yI57Sv6K6h5YmN6Z2i5omA5pyJ5paH5Lu255qE5pe26ZW/77yJDQogICAgICAgIGxldCBzZWVrVmFsdWUgPSAwDQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFyZ2V0RmlsZUluZGV4OyBpKyspIHsNCiAgICAgICAgICBzZWVrVmFsdWUgKz0gdGhpcy5kZXRhaWxGaWxlc1tpXS50aW1lTGVuDQogICAgICAgIH0NCiAgICAgICAgLy8g5Yqg5LiK5paH5Lu25YaF55qE5YGP56e75pe26Ze0DQogICAgICAgIHNlZWtWYWx1ZSArPSB0aW1lT2Zmc2V0SW5GaWxlDQogICAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSA9IHNlZWtWYWx1ZQ0KDQogICAgICAgIGNvbnNvbGUubG9nKGDorqHnrpfnmoRzZWVr5YC877yaJHtzZWVrVmFsdWV9bXNgKQ0KDQogICAgICAgIC8vIOWKoOi9veebruagh+aWh+S7tuW5tnNlZWvliLDmjIflrprkvY3nva4NCiAgICAgICAgdGhpcy5wbGF5UmVjb3JkQnlGaWxlSW5kZXgodGFyZ2V0RmlsZUluZGV4KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc29sZS5sb2coYOWcqOW9k+WJjeaWh+S7tiR7dGFyZ2V0RmlsZUluZGV4feWGhXNlZWvliLDlgY/np7vvvJoke3RpbWVPZmZzZXRJbkZpbGV9bXNgKQ0KICAgICAgICAvLyDlnKjlvZPliY3mlofku7blhoVzZWVrDQogICAgICAgIC8vIOiuoeeul+W9k+WJjeaWh+S7tueahOWfuuehgHNlZWvlgLwNCiAgICAgICAgbGV0IGJhc2VTZWVrVmFsdWUgPSAwDQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFyZ2V0RmlsZUluZGV4OyBpKyspIHsNCiAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbaV0udGltZUxlbg0KICAgICAgICB9DQogICAgICAgIC8vIOWKoOS4iuaWh+S7tuWGheeahOWBj+enu+aXtumXtA0KICAgICAgICB0aGlzLnBsYXlTZWVrVmFsdWUgPSBiYXNlU2Vla1ZhbHVlICsgdGltZU9mZnNldEluRmlsZQ0KDQogICAgICAgIGNvbnNvbGUubG9nKGDmlofku7blhoVzZWVr5YC877yaJHt0aGlzLnBsYXlTZWVrVmFsdWV9bXNgKQ0KDQogICAgICAgIC8vIOS8mOWMlu+8muWmguaenOa1geS/oeaBr+WtmOWcqO+8jOebtOaOpXNlZWvvvJvlkKbliJnph43mlrDliqDovb0NCiAgICAgICAgaWYgKHRoaXMuc3RyZWFtSW5mbyAmJiB0aGlzLnN0cmVhbUluZm8uYXBwICYmIHRoaXMuc3RyZWFtSW5mby5zdHJlYW0pIHsNCiAgICAgICAgICB0aGlzLnNlZWtSZWNvcmQoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmtYHkv6Hmga/kuI3lrZjlnKjvvIzph43mlrDliqDovb3mlofku7YnKQ0KICAgICAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KHRhcmdldEZpbGVJbmRleCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0VGltZUZvckZpbGUoZmlsZSkgew0KICAgICAgY29uc3Qgc3RhclRpbWUgPSBuZXcgRGF0ZShmaWxlLnN0YXJ0VGltZSAqIDEwMDApDQogICAgICBsZXQgZW5kVGltZSA9IG5ldyBEYXRlKGZpbGUuZW5kVGltZSAqIDEwMDApDQogICAgICBpZiAodGhpcy5jaGVja0lzT3ZlcjI0aChzdGFyVGltZSwgZW5kVGltZSkpIHsNCiAgICAgICAgZW5kVGltZSA9IG5ldyBEYXRlKHRoaXMuY2hvb3NlRGF0ZSArICcgJyArICcyMzo1OTo1OScpDQogICAgICB9DQogICAgICByZXR1cm4gW3N0YXJUaW1lLCBlbmRUaW1lLCBlbmRUaW1lLmdldFRpbWUoKSAtIHN0YXJUaW1lLmdldFRpbWUoKV0NCiAgICB9LA0KICAgIGNoZWNrSXNPdmVyMjRoKHN0YXJUaW1lLCBlbmRUaW1lKSB7DQogICAgICByZXR1cm4gc3RhclRpbWUgPiBlbmRUaW1lDQogICAgfSwNCiAgICBwbGF5VGltZUZvcm1hdCh2YWwpIHsNCiAgICAgIGNvbnN0IGggPSBwYXJzZUludCh2YWwgLyAzNjAwKQ0KICAgICAgY29uc3QgbSA9IHBhcnNlSW50KCh2YWwgLSBoICogMzYwMCkgLyA2MCkNCiAgICAgIGNvbnN0IHMgPSBwYXJzZUludCh2YWwgLSBoICogMzYwMCAtIG0gKiA2MCkNCg0KICAgICAgbGV0IGhTdHIgPSBoDQogICAgICBsZXQgbVN0ciA9IG0NCiAgICAgIGxldCBzU3RyID0gcw0KICAgICAgaWYgKGggPCAxMCkgew0KICAgICAgICBoU3RyID0gJzAnICsgaFN0cg0KICAgICAgfQ0KICAgICAgaWYgKG0gPCAxMCkgew0KICAgICAgICBtU3RyID0gJzAnICsgbVN0cg0KICAgICAgICBzDQogICAgICB9DQogICAgICBpZiAocyA8IDEwKSB7DQogICAgICAgIHNTdHIgPSAnMCcgKyBzU3RyDQogICAgICB9DQogICAgICByZXR1cm4gaFN0ciArICc6JyArIG1TdHIgKyAnOicgKyBzU3RyDQogICAgfSwNCiAgICBnZXREYXRlSW5ZZWFyKGNhbGxiYWNrKSB7DQogICAgICB0aGlzLmRhdGVGaWxlc09iaiA9IHt9DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvcXVlcnlMaXN0QnlEYXRhJywgew0KICAgICAgICBhcHA6IHRoaXMuYXBwLA0KICAgICAgICBzdHJlYW06IHRoaXMuc3RyZWFtLA0KICAgICAgICB5ZWFyOiB0aGlzLnF1ZXJ5RGF0ZS5nZXRGdWxsWWVhcigpLA0KICAgICAgICBtb250aDogdGhpcy5xdWVyeURhdGUuZ2V0TW9udGgoKSArIDEsDQogICAgICAgIG1lZGlhU2VydmVySWQ6IHRoaXMubWVkaWFTZXJ2ZXJJZA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgICAgdGhpcy5kYXRlRmlsZXNPYmpbZGF0YVtpXV0gPSBkYXRhW2ldDQogICAgICAgICAgICB9DQogICAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmRhdGVGaWxlc09iaikNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGNhbGxiYWNrKSBjYWxsYmFjaygpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnJvcikNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIGdvQmFjaygpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvY2xvdWRSZWNvcmQnKQ0KICAgIH0NCiAgfQ0KfQ0K"}, null]}