{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750429800769}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}