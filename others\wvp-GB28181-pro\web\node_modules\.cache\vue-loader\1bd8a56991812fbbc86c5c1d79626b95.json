{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750430124076}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}