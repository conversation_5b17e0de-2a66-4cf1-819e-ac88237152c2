{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750431834581}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}