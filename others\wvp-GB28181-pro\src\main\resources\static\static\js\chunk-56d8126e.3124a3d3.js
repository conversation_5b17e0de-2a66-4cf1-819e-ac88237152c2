(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56d8126e"],{"07ac":function(e,t,i){var a=i("23e7"),o=i("6f53").values;a({target:"Object",stat:!0},{values:function(e){return o(e)}})},"25eb":function(e,t,i){var a=i("23e7"),o=i("c20d");a({target:"Number",stat:!0,forced:Number.parseInt!=o},{parseInt:o})},"5d60":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container",attrs:{id:"recordDetail"}},[i("div",{style:e.boxStyle},[i("div",[this.$route.query.mediaServerId?i("div",{staticClass:"page-header-btn",staticStyle:{"padding-right":"1rem"}},[i("b",[e._v("节点：")]),e._v(" "+e._s(e.mediaServerId)+" ")]):e._e(),this.$route.params.mediaServerId?i("div",[i("span",[e._v("流媒体："+e._s(this.$route.params.mediaServerId))])]):e._e(),i("div",{staticClass:"record-list-box-box"},[e.showSidebar?i("div",[i("el-date-picker",{staticStyle:{width:"190px"},attrs:{size:"mini","picker-options":e.pickerOptions,type:"date","value-format":"yyyy-MM-dd",placeholder:"日期"},on:{change:function(t){return e.dateChange()}},model:{value:e.chooseDate,callback:function(t){e.chooseDate=t},expression:"chooseDate"}})],1):e._e(),i("div",{staticClass:"record-list-box",staticStyle:{height:"calc(100vh - 170px)",overflow:"auto"}},[e.detailFiles.length>0?i("ul",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:e.infiniteScroll,expression:"infiniteScroll"}],staticClass:"infinite-list record-list"},e._l(e.detailFiles,(function(t,a){return i("li",{key:a,staticClass:"infinite-list-item record-list-item"},[e.chooseFileIndex!==a?i("el-tag",{on:{click:function(t){return e.chooseFile(a)}}},[i("i",{staticClass:"el-icon-video-camera"}),e._v(" "+e._s(e.getFileShowName(t))+" ")]):e._e(),e.chooseFileIndex===a?i("el-tag",{attrs:{type:"danger"}},[i("i",{staticClass:"el-icon-video-camera"}),e._v(" "+e._s(e.getFileShowName(t))+" ")]):e._e(),i("a",{staticClass:"el-icon-download",staticStyle:{color:"#409EFF","font-weight":"600","margin-left":"10px"},attrs:{target:"_blank"},on:{click:function(i){return e.downloadFile(t)}}})],1)})),0):e._e(),0===e.detailFiles.length?i("div",{staticClass:"record-list-no-val"},[e._v("暂无数据")]):e._e()])])]),i("div",{attrs:{id:"playerBox"}},[i("div",{staticClass:"playBox",staticStyle:{height:"calc(100% - 90px)",width:"100%","background-color":"#000000"}},[e.playLoading?i("div",{staticStyle:{position:"relative",left:"calc(50% - 32px)",top:"43%","z-index":"100",color:"#fff",float:"left","text-align":"center"}},[i("div",{staticClass:"el-icon-loading"}),i("div",{staticStyle:{width:"100%","line-height":"2rem"}},[e._v("正在加载")])]):e._e(),i("h265web",{ref:"recordVideoPlayer",attrs:{"video-url":e.videoUrl,height:"calc(100vh - 250px)","show-button":!1},on:{playTimeChange:e.showPlayTimeChange,playStatusChange:e.playingChange,seekFinish:e.onSeekFinish}})],1),i("div",{staticClass:"player-option-box"},[i("VideoTimeline",{ref:"Timeline",attrs:{"init-time":e.initTime,"time-segments":e.timeSegments,"init-zoom-index":4},on:{timeChange:e.playTimeChange,mousedown:e.timelineMouseDown,mouseup:e.mouseupTimeline}}),e.showTime?i("div",{staticClass:"time-line-show"},[e._v(e._s(e.showTimeValue))]):e._e()],1),i("div",{staticStyle:{height:"40px","background-color":"#383838",display:"grid","grid-template-columns":"1fr 600px 1fr"}},[i("div",{staticStyle:{"text-align":"left"}},[i("div",{staticClass:"record-play-control",staticStyle:{"background-color":"transparent","box-shadow":"0 0 10px transparent"}},[i("a",{staticClass:"record-play-control-item iconfont icon-list",attrs:{target:"_blank",title:"列表"},on:{click:function(t){return e.sidebarControl()}}}),i("a",{staticClass:"record-play-control-item iconfont icon-camera1196054easyiconnet",attrs:{target:"_blank",title:"截图"},on:{click:function(t){return e.snap()}}})])]),i("div",{staticStyle:{"text-align":"center"}},[i("div",{staticClass:"record-play-control"},[e.chooseFileIndex>0?i("a",{staticClass:"record-play-control-item iconfont icon-diyigeshipin",attrs:{target:"_blank",title:"上一个"},on:{click:function(t){return e.playLast()}}}):i("a",{staticClass:"record-play-control-item iconfont icon-diyigeshipin",staticStyle:{color:"#acacac",cursor:"not-allowed"},attrs:{target:"_blank",title:"上一个"}}),i("a",{staticClass:"record-play-control-item iconfont icon-kuaijin",attrs:{target:"_blank",title:"快退五秒"},on:{click:function(t){return e.seekBackward()}}}),i("a",{staticClass:"record-play-control-item iconfont icon-stop1",staticStyle:{"font-size":"14px"},attrs:{target:"_blank",title:"停止"},on:{click:function(t){return e.stopPLay()}}}),e.playing?i("a",{staticClass:"record-play-control-item iconfont icon-zanting",attrs:{target:"_blank",title:"暂停"},on:{click:function(t){return e.pausePlay()}}}):e._e(),e.playing?e._e():i("a",{staticClass:"record-play-control-item iconfont icon-kaishi",attrs:{target:"_blank",title:"播放"},on:{click:function(t){return e.play()}}}),i("a",{staticClass:"record-play-control-item iconfont icon-houtui",attrs:{target:"_blank",title:"快进五秒"},on:{click:function(t){return e.seekForward()}}}),e.chooseFileIndex<e.detailFiles.length-1?i("a",{staticClass:"record-play-control-item iconfont icon-zuihouyigeshipin",attrs:{target:"_blank",title:"下一个"},on:{click:function(t){return e.playNext()}}}):i("a",{staticClass:"record-play-control-item iconfont icon-zuihouyigeshipin",staticStyle:{color:"#acacac",cursor:"not-allowed"},attrs:{target:"_blank",title:"下一个"},on:{click:function(t){return e.playNext()}}}),i("el-dropdown",{on:{command:e.changePlaySpeed}},[i("a",{staticClass:"record-play-control-item record-play-control-speed",attrs:{target:"_blank",title:"倍速播放"}},[e._v(e._s(e.playSpeed)+"X")]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.playSpeedRange,(function(t){return i("el-dropdown-item",{key:t,attrs:{command:t}},[e._v(e._s(t)+"X")])})),1)],1)],1)]),i("div",{staticStyle:{"text-align":"right"}},[i("div",{staticClass:"record-play-control",staticStyle:{"background-color":"transparent","box-shadow":"0 0 10px transparent"}},[e.isFullScreen?i("a",{staticClass:"record-play-control-item iconfont icon-suoxiao1",attrs:{target:"_blank",title:"全屏"},on:{click:function(t){return e.fullScreen()}}}):i("a",{staticClass:"record-play-control-item iconfont icon-fangdazhanshi",attrs:{target:"_blank",title:"全屏"},on:{click:function(t){return e.fullScreen()}}})])])])])])])},o=[],s=i("c14f"),n=i("1da1"),r=(i("99af"),i("a630"),i("caad"),i("b0c0"),i("25eb"),i("d3b7"),i("07ac"),i("6062"),i("2532"),i("3ca3"),i("ddb0"),i("4f91")),l=i("76f0"),c=i("c1df"),d=i.n(c),h=i("93bf"),u=i.n(h),f={name:"CloudRecordDetail",components:{h265web:r["a"],VideoTimeline:l["a"]},data:function(){var e=this;return{showSidebar:!1,app:this.$route.params.app,stream:this.$route.params.stream,mediaServerId:null,dateFilesObj:[],mediaServerList:[],detailFiles:[],videoUrl:null,streamInfo:null,loading:!1,chooseDate:null,playTime:null,playerTime:null,playSpeed:1,chooseFileIndex:null,queryDate:new Date,currentPage:1,count:1e6,total:0,playLoading:!1,showTime:!0,isFullScreen:!1,playSeekValue:0,playing:!1,taskTimeRange:[],timeFormat:"00:00:00",initTime:null,timelineControl:!1,showOtherSpeed:!0,timeSegments:[],seekTimer:null,pickerOptions:{cellClassName:function(t){var i=d()(t).format("YYYY-MM-DD");return e.dateFilesObj[i]?"data-picker-true":"data-picker-false"}},playSpeedRange:[1,2,4,6,8,16]}},computed:{boxStyle:function(){return this.showSidebar?{display:"grid",gridTemplateColumns:"210px minmax(0, 1fr)"}:{display:"grid",gridTemplateColumns:"0 minmax(0, 1fr)"}},showTimeValue:function(){return d()(this.playTime).format("YYYY-MM-DD HH:mm:ss")}},mounted:function(){var e=this;this.getDateInYear((function(){Object.values(e.dateFilesObj).length>0&&(e.chooseDate=Object.values(e.dateFilesObj)[Object.values(e.dateFilesObj).length-1],e.dateChange())}))},destroyed:function(){this.$destroy("recordVideoPlayer"),this.seekTimer&&(clearTimeout(this.seekTimer),this.seekTimer=null)},methods:{sidebarControl:function(){this.showSidebar=!this.showSidebar},snap:function(){this.$refs.recordVideoPlayer.screenshot()},playLast:function(){0!==this.chooseFileIndex&&this.chooseFile(this.chooseFileIndex-1)},playNext:function(){this.chooseFileIndex!==this.detailFiles.length-1&&this.chooseFile(this.chooseFileIndex+1)},changePlaySpeed:function(e){console.log(e),this.playSpeed=e,this.streamInfo&&this.$store.dispatch("cloudRecord/speed",{mediaServerId:this.streamInfo.mediaServerId,app:this.streamInfo.app,stream:this.streamInfo.stream,speed:this.playSpeed,schema:"ts"});try{this.$refs.recordVideoPlayer&&this.$refs.recordVideoPlayer.setPlaybackRate(this.playSpeed)}catch(t){console.warn("设置播放倍速时出现错误:",t)}},seekBackward:function(){this.playSeekValue-=5e3,this.playRecord()},seekForward:function(){this.playSeekValue+=5e3,this.playRecord()},stopPLay:function(){try{this.$refs.recordVideoPlayer&&this.$refs.recordVideoPlayer.destroy()}catch(e){console.warn("停止播放时出现错误:",e)}this.playing=!1,this.videoUrl=null,this.playLoading=!1,this.chooseFileIndex=0,this.playSeekValue=0,this.playerTime=0,this.detailFiles&&this.detailFiles.length>0&&this.detailFiles[0]?void 0!==this.detailFiles[0].startTime?(this.playTime=this.detailFiles[0].startTime,this.$refs.Timeline&&this.$refs.Timeline.setTime(this.playTime)):console.warn("第一个文件的startTime未定义"):console.warn("没有可用的录像文件"),1!==this.playSpeed&&this.changePlaySpeed(1)},pausePlay:function(){try{this.$refs.recordVideoPlayer&&this.$refs.recordVideoPlayer.pause()}catch(e){console.warn("暂停播放时出现错误:",e)}},play:function(){if(this.$refs.recordVideoPlayer&&this.$refs.recordVideoPlayer.loaded&&!this.playLoading)try{this.$refs.recordVideoPlayer.unPause()}catch(e){"AbortError"!==e.name&&(console.warn("恢复播放失败，重新加载视频:",e),this.playRecord())}else this.playRecord()},fullScreen:function(){var e=this;if(this.isFullScreen)return u.a.exit(),void(this.isFullScreen=!1);var t=this.$refs.recordVideoPlayer.playerWidth,i=this.$refs.recordVideoPlayer.playerHeight;u.a.request(document.getElementById("playerBox")),u.a.on("change",(function(a){e.$refs.recordVideoPlayer.resize(t,i),e.isFullScreen=u.a.isFullscreen})),this.isFullScreen=!0},dateChange:function(){this.detailFiles=[],this.currentPage=1;var e=new Date(this.chooseDate+" "+this.timeFormat);e.getFullYear()===this.queryDate.getFullYear()&&e.getMonth()===this.queryDate.getMonth()||(this.queryDate=e,this.getDateInYear()),this.queryRecordDetails()},infiniteScroll:function(){this.total>this.detailFiles.length&&(this.currentPage++,this.queryRecordDetails())},queryRecordDetails:function(e){var t=this;this.timeSegments=[],this.$store.dispatch("cloudRecord/queryList",{app:this.app,stream:this.stream,startTime:this.chooseDate+" 00:00:00",endTime:this.chooseDate+" 23:59:59",page:this.currentPage,count:this.count,mediaServerId:this.mediaServerId,ascOrder:!0}).then((function(e){t.total=e.total,t.detailFiles=t.detailFiles.concat(e.list);var i=new Set;if(t.detailFiles&&t.detailFiles.length>0){t.initTime=Number.parseInt(t.detailFiles[0].startTime),null===t.playTime&&(t.playTime=t.detailFiles[0].startTime,t.playerTime=0);for(var a=0;a<t.detailFiles.length;a++)i.add(t.detailFiles[a].mediaServerId),t.timeSegments.push({beginTime:Number.parseInt(t.detailFiles[a].startTime),endTime:Number.parseInt(t.detailFiles[a].endTime),color:"#01901d",startRatio:.7,endRatio:.85,index:a});t.mediaServerList=Array.from(i),1===t.mediaServerList.length&&(t.mediaServerId=t.mediaServerList[0])}else console.warn("没有找到录像文件")})).catch((function(e){console.log(e)})).finally((function(){t.loading=!1,e&&e()}))},chooseFile:function(e){if(!this.detailFiles||e<0||e>=this.detailFiles.length)console.error("无效的文件索引:",e,"，文件列表长度:",this.detailFiles?this.detailFiles.length:0);else{this.chooseFileIndex=e;var t=this.detailFiles[e];if(t&&void 0!==t.startTime){this.playTime=t.startTime,this.playerTime=0;for(var i=0,a=0;a<e;a++)this.detailFiles[a]&&void 0!==this.detailFiles[a].timeLen&&(i+=this.detailFiles[a].timeLen);this.playSeekValue=i,this.$refs.Timeline&&this.$refs.Timeline.setTime(this.playTime),this.playRecordByFileIndex(e)}else console.error("选中的文件数据无效:",t)}},playRecord:function(){var e=this;try{this.$refs.recordVideoPlayer&&!this.$refs.recordVideoPlayer.playing&&this.$refs.recordVideoPlayer.destroy()}catch(t){console.warn("销毁播放器时出现错误:",t)}this.playLoading=!0,this.$store.dispatch("cloudRecord/loadRecord",{app:this.app,stream:this.stream,date:this.chooseDate}).then((function(t){e.streamInfo=t,"https:"===location.protocol?e.videoUrl=t["https_fmp4"]+"&time="+(new Date).getTime():e.videoUrl=t["fmp4"]+"&time="+(new Date).getTime(),e.seekRecord()})).catch((function(e){console.log(e)})).finally((function(){e.playLoading=!1}))},playRecordByFileIndex:function(e){var t=this;console.log("播放指定文件索引:",e,"，seek值:",this.playSeekValue);try{this.$refs.recordVideoPlayer&&!this.$refs.recordVideoPlayer.playing&&this.$refs.recordVideoPlayer.destroy()}catch(i){console.warn("销毁播放器时出现错误:",i)}this.playLoading=!0,this.$store.dispatch("cloudRecord/loadRecordByFileIndex",{app:this.app,stream:this.stream,date:this.chooseDate,fileIndex:e}).then((function(i){if(console.log("加载文件成功:",i),t.streamInfo=i,"https:"===location.protocol?t.videoUrl=i["https_fmp4"]+"&time="+(new Date).getTime():t.videoUrl=i["fmp4"]+"&time="+(new Date).getTime(),t.detailFiles[e]){for(var a=t.detailFiles[e],o=0,s=0;s<e;s++)o+=t.detailFiles[s].timeLen;var n=t.playSeekValue-o;t.playTime=a.startTime+n,t.playerTime=n}if(t.playSeekValue>0){for(var r=0,l=0;l<e;l++)r+=t.detailFiles[l].timeLen;var c=t.playSeekValue-r;console.log("执行seek定位 - 全局seek值:",t.playSeekValue,"，文件内seek值:",c),c>=0&&c<=t.detailFiles[e].timeLen?t.seekRecord():console.warn("seek值超出当前文件范围，跳过seek操作 - fileSeekValue:",c,"，文件时长:",t.detailFiles[e].timeLen)}})).catch((function(e){console.log("加载文件失败:",e)})).finally((function(){t.playLoading=!1}))},seekRecord:function(){var e=this;this.$refs.recordVideoPlayer&&this.streamInfo?(this.seekTimer&&clearTimeout(this.seekTimer),this.seekTimer=setTimeout((function(){e.doSeekRecord()}),300)):console.warn("播放器或流信息未准备好，跳过seek操作")},doSeekRecord:function(){var e=this;return Object(n["a"])(Object(s["a"])().m((function t(){var i,a,o,n,r,l,c,d,h;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:if(e.$refs.recordVideoPlayer&&e.streamInfo){t.n=1;break}return console.warn("播放器或流信息未准备好，取消seek操作"),t.a(2);case 1:if(i=0,null!==e.chooseFileIndex)for(a=0;a<e.chooseFileIndex;a++)i+=e.detailFiles[a].timeLen;if(o=e.playSeekValue-i,console.log("执行seek定位 - 全局seek值:",e.playSeekValue,"ms，文件内seek值:",o,"ms"),null!==e.chooseFileIndex&&e.detailFiles[e.chooseFileIndex]&&(n=e.detailFiles[e.chooseFileIndex],(o<0||o>n.timeLen)&&(console.warn("seek值超出当前文件范围，调整到文件边界 - fileSeekValue:",o,"，文件时长:",n.timeLen),r=Math.max(0,Math.min(o,n.timeLen)),e.playSeekValue=i+r,console.log("调整后的seek值:",e.playSeekValue))),l=e.$refs.recordVideoPlayer.playing,!l||!e.$refs.recordVideoPlayer.pause){t.n=5;break}return t.p=2,e.$refs.recordVideoPlayer.pause(),t.n=3,new Promise((function(e){return setTimeout(e,100)}));case 3:t.n=5;break;case 4:t.p=4,h=t.v,console.warn("暂停播放器时出现错误:",h);case 5:c=e.playSeekValue-i,d=c,e.streamInfo.stream&&e.streamInfo.stream.includes("_"+e.chooseFileIndex)?(d=c,console.log("检测到按文件索引加载的流，使用文件内seek值:",d,"ms")):(d=e.playSeekValue,console.log("检测到整体录像流，使用全局seek值:",d,"ms")),e.$store.dispatch("cloudRecord/seek",{mediaServerId:e.streamInfo.mediaServerId,app:e.streamInfo.app,stream:e.streamInfo.stream,seek:d,schema:"fmp4"}).then((function(){console.log("后端seek操作成功 - 发送的seek值:",d,"ms"),e.syncPlayerSeek(l)})).catch((function(t){console.warn("seek操作失败:",t),e.syncPlayerSeek(l)}));case 6:return t.a(2)}}),t,null,[[2,4]])})))()},syncPlayerSeek:function(e){var t=this;if(null!==this.chooseFileIndex&&this.detailFiles[this.chooseFileIndex]){for(var i=0,a=0;a<this.chooseFileIndex;a++)i+=this.detailFiles[a].timeLen;var o=this.playSeekValue-i,s=o/1e3;console.log("前端播放器seek到:",s,"秒（文件内偏移）"),this.updateDisplayTime(),setTimeout((function(){if(console.log("开始前端播放器seek操作"),console.log("播放器状态检查:",{playerExists:!!t.$refs.recordVideoPlayer,seekMethodExists:!(!t.$refs.recordVideoPlayer||!t.$refs.recordVideoPlayer.seek),playerLoaded:!(!t.$refs.recordVideoPlayer||!t.$refs.recordVideoPlayer.loaded),playing:!(!t.$refs.recordVideoPlayer||!t.$refs.recordVideoPlayer.playing)}),t.$refs.recordVideoPlayer&&t.$refs.recordVideoPlayer.seek){console.log("调用播放器seek方法，目标时间:",s,"秒");var i=t.$refs.recordVideoPlayer.seek(s);console.log("播放器seek方法返回值:",i),i?(console.log("前端播放器seek成功"),setTimeout((function(){t.updateDisplayTime()}),100)):(console.warn("前端播放器seek失败，尝试重新加载播放器"),null!==t.chooseFileIndex&&t.playRecordByFileIndex(t.chooseFileIndex)),e&&setTimeout((function(){if(t.$refs.recordVideoPlayer&&!t.$refs.recordVideoPlayer.playing){console.log("恢复播放状态");try{t.$refs.recordVideoPlayer.unPause()}catch(e){"AbortError"!==e.name&&console.warn("恢复播放时出现错误:",e)}}}),500)}else console.warn("播放器不支持seek操作，尝试重新加载播放器"),null!==t.chooseFileIndex&&t.playRecordByFileIndex(t.chooseFileIndex),e&&setTimeout((function(){if(t.$refs.recordVideoPlayer&&!t.$refs.recordVideoPlayer.playing)try{t.$refs.recordVideoPlayer.unPause()}catch(e){"AbortError"!==e.name&&console.warn("恢复播放时出现错误:",e)}}),500)}),800)}},updateDisplayTime:function(){if(null!==this.chooseFileIndex&&this.detailFiles&&this.chooseFileIndex>=0&&this.chooseFileIndex<this.detailFiles.length){var e=this.detailFiles[this.chooseFileIndex];if(!e||void 0===e.startTime)return void console.warn("选中的文件数据无效，无法更新显示时间:",e);for(var t=0,i=0;i<this.chooseFileIndex;i++)this.detailFiles[i]&&void 0!==this.detailFiles[i].timeLen&&(t+=this.detailFiles[i].timeLen);var a=this.playSeekValue-t;this.playTime=e.startTime+a,this.playerTime=a,console.log("手动更新显示时间:",{playTime:this.playTime,playerTime:this.playerTime,offsetInFile:a,selectedFileStartTime:e.startTime})}else console.warn("无法更新显示时间 - 文件索引无效:",{chooseFileIndex:this.chooseFileIndex,detailFilesLength:this.detailFiles?this.detailFiles.length:0})},downloadFile:function(e){this.$store.dispatch("cloudRecord/getPlayPath",e.id).then((function(t){var i=document.createElement("a");i.target="_blank","https:"===location.protocol?i.href=t.httpsPath+"&save_name="+e.fileName:i.href=t.httpPath+"&save_name="+e.fileName,i.click()})).catch((function(e){console.log(e)}))},backToList:function(){this.$router.back()},getFileShowName:function(e){return e&&void 0!==e.startTime&&void 0!==e.endTime?d()(e.startTime).format("HH:mm:ss")+"-"+d()(e.endTime).format("HH:mm:ss"):(console.warn("文件数据无效，无法格式化显示名称:",e),"无效时间")},showPlayTimeChange:function(e){if(null!==this.chooseFileIndex&&this.detailFiles&&this.chooseFileIndex>=0&&this.chooseFileIndex<this.detailFiles.length){var t=this.detailFiles[this.chooseFileIndex];if(!t||void 0===t.startTime)return void console.warn("选中的文件数据无效，无法更新播放时间:",t);var i=t.startTime+1e3*e,a=1e3*e;if(this.timelineControl)return void console.log("正在拖动时间轴，忽略播放器时间回调");this.playTime=i,this.playerTime=a}},playingChange:function(e){this.playing=e},playTimeChange:function(e){e!==this.playTime&&(this.playTime=e)},timelineMouseDown:function(){this.timelineControl=!0},onSeekFinish:function(){console.log("播放器seek完成回调"),this.updateDisplayTime()},mouseupTimeline:function(){if(this.timelineControl){this.timelineControl=!1,console.log("时间轴拖动结束，当前时间:",this.playTime,"，文件列表长度:",this.detailFiles.length);for(var e=-1,t=0,i=0;i<this.detailFiles.length;i++){var a=this.detailFiles[i];if(a&&void 0!==a.startTime&&void 0!==a.endTime){if(console.log("检查文件".concat(i,": ").concat(a.startTime," - ").concat(a.endTime,", 当前时间: ").concat(this.playTime)),this.playTime>=a.startTime&&this.playTime<=a.endTime){e=i,t=this.playTime-a.startTime,console.log("找到目标文件".concat(i,"，文件内偏移：").concat(t,"ms"));break}}else console.warn("文件".concat(i,"数据无效:"),a)}if(-1===e){console.warn("拖动时间点不在任何文件范围内，查找最近的文件");for(var o=1/0,s=0;s<this.detailFiles.length;s++){var n=this.detailFiles[s];if(n&&void 0!==n.startTime&&void 0!==n.endTime&&void 0!==n.timeLen){var r=Math.abs(this.playTime-n.startTime),l=Math.abs(this.playTime-n.endTime),c=Math.min(r,l);c<o&&(o=c,e=s,t=r<l?0:n.timeLen)}else console.warn("文件".concat(s,"数据无效，跳过:"),n)}if(-1===e)return void console.error("无法找到任何可播放的文件");console.log("使用最近的文件".concat(e,"，偏移：").concat(t,"ms"))}if(console.log("拖动到文件".concat(e,"，时间偏移：").concat(t,"ms")),this.chooseFileIndex!==e){console.log("切换文件：从".concat(this.chooseFileIndex,"到").concat(e)),this.chooseFileIndex=e;for(var d=0,h=0;h<e;h++)this.detailFiles[h]&&void 0!==this.detailFiles[h].timeLen&&(d+=this.detailFiles[h].timeLen);d+=t,this.playSeekValue=d,console.log("计算的seek值：".concat(d,"ms")),this.playRecordByFileIndex(e)}else{console.log("在当前文件".concat(e,"内seek到偏移：").concat(t,"ms"));for(var u=0,f=0;f<e;f++)this.detailFiles[f]&&void 0!==this.detailFiles[f].timeLen&&(u+=this.detailFiles[f].timeLen);this.playSeekValue=u+t,console.log("文件内seek值：".concat(this.playSeekValue,"ms")),this.streamInfo&&this.streamInfo.app&&this.streamInfo.stream?this.seekRecord():(console.log("流信息不存在，重新加载文件"),this.playRecordByFileIndex(e))}}else this.timelineControl=!1},getTimeForFile:function(e){if(!e||void 0===e.startTime||void 0===e.endTime)return console.warn("文件数据无效，无法计算时间:",e),[new Date,new Date,0];var t=new Date(1e3*e.startTime),i=new Date(1e3*e.endTime);return this.checkIsOver24h(t,i)&&(i=new Date(this.chooseDate+" 23:59:59")),[t,i,i.getTime()-t.getTime()]},checkIsOver24h:function(e,t){return e>t},playTimeFormat:function(e){var t=parseInt(e/3600),i=parseInt((e-3600*t)/60),a=parseInt(e-3600*t-60*i),o=t,s=i,n=a;return t<10&&(o="0"+o),i<10&&(s="0"+s),a<10&&(n="0"+n),o+":"+s+":"+n},getDateInYear:function(e){var t=this;this.dateFilesObj={},this.$store.dispatch("cloudRecord/queryListByData",{app:this.app,stream:this.stream,year:this.queryDate.getFullYear(),month:this.queryDate.getMonth()+1,mediaServerId:this.mediaServerId}).then((function(i){if(i.length>0){for(var a=0;a<i.length;a++)t.dateFilesObj[i[a]]=i[a];console.log(t.dateFilesObj)}e&&e()})).catch((function(e){console.log(e)}))},goBack:function(){this.$router.push("/cloudRecord")}}},m=f,p=(i("ead4"),i("2877")),y=Object(p["a"])(m,a,o,!1,null,null,null);t["default"]=y.exports},6062:function(e,t,i){"use strict";var a=i("6d61"),o=i("6566");e.exports=a("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),o)},6566:function(e,t,i){"use strict";var a=i("9bf2").f,o=i("7c73"),s=i("e2cc"),n=i("0366"),r=i("19aa"),l=i("2266"),c=i("7dd0"),d=i("2626"),h=i("83ab"),u=i("f183").fastKey,f=i("69f3"),m=f.set,p=f.getterFor;e.exports={getConstructor:function(e,t,i,c){var d=e((function(e,a){r(e,d,t),m(e,{type:t,index:o(null),first:void 0,last:void 0,size:0}),h||(e.size=0),void 0!=a&&l(a,e[c],e,i)})),f=p(t),y=function(e,t,i){var a,o,s=f(e),n=v(e,t);return n?n.value=i:(s.last=n={index:o=u(t,!0),key:t,value:i,previous:a=s.last,next:void 0,removed:!1},s.first||(s.first=n),a&&(a.next=n),h?s.size++:e.size++,"F"!==o&&(s.index[o]=n)),e},v=function(e,t){var i,a=f(e),o=u(t);if("F"!==o)return a.index[o];for(i=a.first;i;i=i.next)if(i.key==t)return i};return s(d.prototype,{clear:function(){var e=this,t=f(e),i=t.index,a=t.first;while(a)a.removed=!0,a.previous&&(a.previous=a.previous.next=void 0),delete i[a.index],a=a.next;t.first=t.last=void 0,h?t.size=0:e.size=0},delete:function(e){var t=this,i=f(t),a=v(t,e);if(a){var o=a.next,s=a.previous;delete i.index[a.index],a.removed=!0,s&&(s.next=o),o&&(o.previous=s),i.first==a&&(i.first=o),i.last==a&&(i.last=s),h?i.size--:t.size--}return!!a},forEach:function(e){var t,i=f(this),a=n(e,arguments.length>1?arguments[1]:void 0,3);while(t=t?t.next:i.first){a(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!v(this,e)}}),s(d.prototype,i?{get:function(e){var t=v(this,e);return t&&t.value},set:function(e,t){return y(this,0===e?0:e,t)}}:{add:function(e){return y(this,e=0===e?0:e,e)}}),h&&a(d.prototype,"size",{get:function(){return f(this).size}}),d},setStrong:function(e,t,i){var a=t+" Iterator",o=p(t),s=p(a);c(e,t,(function(e,t){m(this,{type:a,target:e,state:o(e),kind:t,last:void 0})}),(function(){var e=s(this),t=e.kind,i=e.last;while(i&&i.removed)i=i.previous;return e.target&&(e.last=i=i?i.next:e.state.first)?"keys"==t?{value:i.key,done:!1}:"values"==t?{value:i.value,done:!1}:{value:[i.key,i.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),i?"entries":"values",!i,!0),d(t)}}},"6d61":function(e,t,i){"use strict";var a=i("23e7"),o=i("da84"),s=i("94ca"),n=i("6eeb"),r=i("f183"),l=i("2266"),c=i("19aa"),d=i("861d"),h=i("d039"),u=i("1c7e"),f=i("d44e"),m=i("7156");e.exports=function(e,t,i){var p=-1!==e.indexOf("Map"),y=-1!==e.indexOf("Weak"),v=p?"set":"add",g=o[e],F=g&&g.prototype,k=g,T={},x=function(e){var t=F[e];n(F,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(y&&!d(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return y&&!d(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(y&&!d(e))&&t.call(this,0===e?0:e)}:function(e,i){return t.call(this,0===e?0:e,i),this})};if(s(e,"function"!=typeof g||!(y||F.forEach&&!h((function(){(new g).entries().next()})))))k=i.getConstructor(t,e,p,v),r.REQUIRED=!0;else if(s(e,!0)){var S=new k,w=S[v](y?{}:-0,1)!=S,b=h((function(){S.has(1)})),I=u((function(e){new g(e)})),V=!y&&h((function(){var e=new g,t=5;while(t--)e[v](t,t);return!e.has(-0)}));I||(k=t((function(t,i){c(t,k,e);var a=m(new g,t,k);return void 0!=i&&l(i,a[v],a,p),a})),k.prototype=F,F.constructor=k),(b||V)&&(x("delete"),x("has"),p&&x("get")),(V||w)&&x(v),y&&F.clear&&delete F.clear}return T[e]=k,a({global:!0,forced:k!=g},T),f(k,e),y||i.setStrong(k,e,p),k}},"6f53":function(e,t,i){var a=i("83ab"),o=i("df75"),s=i("fc6a"),n=i("d1e7").f,r=function(e){return function(t){var i,r=s(t),l=o(r),c=l.length,d=0,h=[];while(c>d)i=l[d++],a&&!n.call(r,i)||h.push(e?[i,r[i]]:r[i]);return h}};e.exports={entries:r(!0),values:r(!1)}},7531:function(e,t,i){},bb2f:function(e,t,i){var a=i("d039");e.exports=!a((function(){return Object.isExtensible(Object.preventExtensions({}))}))},c20d:function(e,t,i){var a=i("da84"),o=i("58a8").trim,s=i("5899"),n=a.parseInt,r=/^[+-]?0[Xx]/,l=8!==n(s+"08")||22!==n(s+"0x16");e.exports=l?function(e,t){var i=o(String(e));return n(i,t>>>0||(r.test(i)?16:10))}:n},ead4:function(e,t,i){"use strict";i("7531")},f183:function(e,t,i){var a=i("d012"),o=i("861d"),s=i("5135"),n=i("9bf2").f,r=i("90e3"),l=i("bb2f"),c=r("meta"),d=0,h=Object.isExtensible||function(){return!0},u=function(e){n(e,c,{value:{objectID:"O"+ ++d,weakData:{}}})},f=function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!s(e,c)){if(!h(e))return"F";if(!t)return"E";u(e)}return e[c].objectID},m=function(e,t){if(!s(e,c)){if(!h(e))return!0;if(!t)return!1;u(e)}return e[c].weakData},p=function(e){return l&&y.REQUIRED&&h(e)&&!s(e,c)&&u(e),e},y=e.exports={REQUIRED:!1,fastKey:f,getWeakData:m,onFreeze:p};a[c]=!0}}]);