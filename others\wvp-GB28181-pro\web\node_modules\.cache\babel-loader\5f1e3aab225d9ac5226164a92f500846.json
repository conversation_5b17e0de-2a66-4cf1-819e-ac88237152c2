{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750424407219}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}