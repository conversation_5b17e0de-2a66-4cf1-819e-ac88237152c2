{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750428182803}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}