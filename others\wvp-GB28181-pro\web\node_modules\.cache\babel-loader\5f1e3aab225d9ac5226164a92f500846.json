{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750429501489}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}