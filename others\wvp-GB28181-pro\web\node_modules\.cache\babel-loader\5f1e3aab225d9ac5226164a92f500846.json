{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750430124076}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvciBmcm9tICJDOi9Ecml2ZXItRS9cdTVERTVcdTRGNUMvXHU0RUUzXHU3ODAxXHU2NTg3XHU0RUY2L290aGVycy93dnAtR0IyODE4MS1wcm8vd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvci5qcyI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJDOi9Ecml2ZXItRS9cdTVERTVcdTRGNUMvXHU0RUUzXHU3ODAxXHU2NTg3XHU0RUY2L290aGVycy93dnAtR0IyODE4MS1wcm8vd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZnJvbS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5wYXJzZS1pbnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnZhbHVlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IGgyNjV3ZWIgZnJvbSAnLi4vY29tbW9uL2gyNjV3ZWIudnVlJzsKaW1wb3J0IFZpZGVvVGltZWxpbmUgZnJvbSAnLi4vY29tbW9uL1ZpZGVvVGltZUxpbmUvaW5kZXgudnVlJzsKaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQnOwppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDbG91ZFJlY29yZERldGFpbCcsCiAgY29tcG9uZW50czogewogICAgaDI2NXdlYjogaDI2NXdlYiwKICAgIFZpZGVvVGltZWxpbmU6IFZpZGVvVGltZWxpbmUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgcmV0dXJuIHsKICAgICAgc2hvd1NpZGViYXI6IGZhbHNlLAogICAgICBhcHA6IHRoaXMuJHJvdXRlLnBhcmFtcy5hcHAsCiAgICAgIHN0cmVhbTogdGhpcy4kcm91dGUucGFyYW1zLnN0cmVhbSwKICAgICAgbWVkaWFTZXJ2ZXJJZDogbnVsbCwKICAgICAgZGF0ZUZpbGVzT2JqOiBbXSwKICAgICAgbWVkaWFTZXJ2ZXJMaXN0OiBbXSwKICAgICAgZGV0YWlsRmlsZXM6IFtdLAogICAgICB2aWRlb1VybDogbnVsbCwKICAgICAgc3RyZWFtSW5mbzogbnVsbCwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNob29zZURhdGU6IG51bGwsCiAgICAgIHBsYXlUaW1lOiBudWxsLAogICAgICBwbGF5ZXJUaW1lOiBudWxsLAogICAgICBwbGF5U3BlZWQ6IDEsCiAgICAgIGNob29zZUZpbGVJbmRleDogbnVsbCwKICAgICAgcXVlcnlEYXRlOiBuZXcgRGF0ZSgpLAogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgY291bnQ6IDEwMDAwMDAsCiAgICAgIC8vIFRPRE8g5YiG6aG15a+86Ie05ruR6L2o6KeG6aKR5pyJ5pWI5YC85peg5rOV6I635Y+W5a6M5YWoCiAgICAgIHRvdGFsOiAwLAogICAgICBwbGF5TG9hZGluZzogZmFsc2UsCiAgICAgIHNob3dUaW1lOiB0cnVlLAogICAgICBpc0Z1bGxTY3JlZW46IGZhbHNlLAogICAgICBwbGF5U2Vla1ZhbHVlOiAwLAogICAgICBwbGF5aW5nOiBmYWxzZSwKICAgICAgdGFza1RpbWVSYW5nZTogW10sCiAgICAgIHRpbWVGb3JtYXQ6ICcwMDowMDowMCcsCiAgICAgIGluaXRUaW1lOiBudWxsLAogICAgICB0aW1lbGluZUNvbnRyb2w6IGZhbHNlLAogICAgICBzaG93T3RoZXJTcGVlZDogdHJ1ZSwKICAgICAgdGltZVNlZ21lbnRzOiBbXSwKICAgICAgc2Vla1RpbWVyOiBudWxsLAogICAgICAvLyBzZWVr6Ziy5oqW5a6a5pe25ZmoCiAgICAgIHBpY2tlck9wdGlvbnM6IHsKICAgICAgICBjZWxsQ2xhc3NOYW1lOiBmdW5jdGlvbiBjZWxsQ2xhc3NOYW1lKGRhdGUpIHsKICAgICAgICAgIC8vIOmAmui/h+aYvuekuuS4gOS4queCueagh+ivhui/meS4gOWkqeacieW9leWDjwogICAgICAgICAgdmFyIHRpbWUgPSBtb21lbnQoZGF0ZSkuZm9ybWF0KCdZWVlZLU1NLUREJyk7CiAgICAgICAgICBpZiAoX3RoaXMuZGF0ZUZpbGVzT2JqW3RpbWVdKSB7CiAgICAgICAgICAgIHJldHVybiAnZGF0YS1waWNrZXItdHJ1ZSc7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICByZXR1cm4gJ2RhdGEtcGlja2VyLWZhbHNlJzsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIHBsYXlTcGVlZFJhbmdlOiBbMSwgMiwgNCwgNiwgOCwgMTZdCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGJveFN0eWxlOiBmdW5jdGlvbiBib3hTdHlsZSgpIHsKICAgICAgaWYgKHRoaXMuc2hvd1NpZGViYXIpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgZGlzcGxheTogJ2dyaWQnLAogICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzIxMHB4IG1pbm1heCgwLCAxZnIpJwogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGRpc3BsYXk6ICdncmlkJywKICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcwIG1pbm1heCgwLCAxZnIpJwogICAgICAgIH07CiAgICAgIH0KICAgIH0sCiAgICBzaG93VGltZVZhbHVlOiBmdW5jdGlvbiBzaG93VGltZVZhbHVlKCkgewogICAgICByZXR1cm4gbW9tZW50KHRoaXMucGxheVRpbWUpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgLy8g5p+l6K+i5b2T5bm05pyJ6KeG6aKR55qE5pel5pyfCiAgICB0aGlzLmdldERhdGVJblllYXIoZnVuY3Rpb24gKCkgewogICAgICBpZiAoT2JqZWN0LnZhbHVlcyhfdGhpczIuZGF0ZUZpbGVzT2JqKS5sZW5ndGggPiAwKSB7CiAgICAgICAgX3RoaXMyLmNob29zZURhdGUgPSBPYmplY3QudmFsdWVzKF90aGlzMi5kYXRlRmlsZXNPYmopW09iamVjdC52YWx1ZXMoX3RoaXMyLmRhdGVGaWxlc09iaikubGVuZ3RoIC0gMV07CiAgICAgICAgX3RoaXMyLmRhdGVDaGFuZ2UoKTsKICAgICAgfQogICAgfSk7CiAgfSwKICBkZXN0cm95ZWQ6IGZ1bmN0aW9uIGRlc3Ryb3llZCgpIHsKICAgIHRoaXMuJGRlc3Ryb3koJ3JlY29yZFZpZGVvUGxheWVyJyk7CiAgICAvLyDmuIXnkIblrprml7blmagKICAgIGlmICh0aGlzLnNlZWtUaW1lcikgewogICAgICBjbGVhclRpbWVvdXQodGhpcy5zZWVrVGltZXIpOwogICAgICB0aGlzLnNlZWtUaW1lciA9IG51bGw7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBzaWRlYmFyQ29udHJvbDogZnVuY3Rpb24gc2lkZWJhckNvbnRyb2woKSB7CiAgICAgIHRoaXMuc2hvd1NpZGViYXIgPSAhdGhpcy5zaG93U2lkZWJhcjsKICAgIH0sCiAgICBzbmFwOiBmdW5jdGlvbiBzbmFwKCkgewogICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnNjcmVlbnNob3QoKTsKICAgIH0sCiAgICBwbGF5TGFzdDogZnVuY3Rpb24gcGxheUxhc3QoKSB7CiAgICAgIC8vIOaSreaUvuS4iuS4gOS4qgogICAgICBpZiAodGhpcy5jaG9vc2VGaWxlSW5kZXggPT09IDApIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5jaG9vc2VGaWxlKHRoaXMuY2hvb3NlRmlsZUluZGV4IC0gMSk7CiAgICB9LAogICAgcGxheU5leHQ6IGZ1bmN0aW9uIHBsYXlOZXh0KCkgewogICAgICAvLyDmkq3mlL7kuIvkuIDkuKoKICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ID09PSB0aGlzLmRldGFpbEZpbGVzLmxlbmd0aCAtIDEpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5jaG9vc2VGaWxlKHRoaXMuY2hvb3NlRmlsZUluZGV4ICsgMSk7CiAgICB9LAogICAgY2hhbmdlUGxheVNwZWVkOiBmdW5jdGlvbiBjaGFuZ2VQbGF5U3BlZWQoc3BlZWQpIHsKICAgICAgY29uc29sZS5sb2coc3BlZWQpOwogICAgICAvLyDlgI3pgJ/mkq3mlL4KICAgICAgdGhpcy5wbGF5U3BlZWQgPSBzcGVlZDsKICAgICAgaWYgKHRoaXMuc3RyZWFtSW5mbykgewogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdjbG91ZFJlY29yZC9zcGVlZCcsIHsKICAgICAgICAgIG1lZGlhU2VydmVySWQ6IHRoaXMuc3RyZWFtSW5mby5tZWRpYVNlcnZlcklkLAogICAgICAgICAgYXBwOiB0aGlzLnN0cmVhbUluZm8uYXBwLAogICAgICAgICAgc3RyZWFtOiB0aGlzLnN0cmVhbUluZm8uc3RyZWFtLAogICAgICAgICAgc3BlZWQ6IHRoaXMucGxheVNwZWVkLAogICAgICAgICAgc2NoZW1hOiAndHMnCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgdHJ5IHsKICAgICAgICBpZiAodGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllcikgewogICAgICAgICAgdGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5zZXRQbGF5YmFja1JhdGUodGhpcy5wbGF5U3BlZWQpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+iuvue9ruaSreaUvuWAjemAn+aXtuWHuueOsOmUmeivrzonLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCiAgICBzZWVrQmFja3dhcmQ6IGZ1bmN0aW9uIHNlZWtCYWNrd2FyZCgpIHsKICAgICAgLy8g5b+r6YCA5LqU56eSCiAgICAgIGNvbnNvbGUubG9nKCflv6vpgIDkupTnp5IgLSDlvZPliY1zZWVr5YC8OicsIHRoaXMucGxheVNlZWtWYWx1ZSk7CiAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSAtPSA1ICogMTAwMDsKCiAgICAgIC8vIOehruS/neS4jeS8mumAgOWIsOi0n+aVsAogICAgICBpZiAodGhpcy5wbGF5U2Vla1ZhbHVlIDwgMCkgewogICAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSA9IDA7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ+W/q+mAgOWQjnNlZWvlgLw6JywgdGhpcy5wbGF5U2Vla1ZhbHVlKTsKICAgICAgdGhpcy5zbWFydFNlZWtPclJlbG9hZCgpOwogICAgfSwKICAgIHNlZWtGb3J3YXJkOiBmdW5jdGlvbiBzZWVrRm9yd2FyZCgpIHsKICAgICAgLy8g5b+r6L+b5LqU56eSCiAgICAgIGNvbnNvbGUubG9nKCflv6vov5vkupTnp5IgLSDlvZPliY1zZWVr5YC8OicsIHRoaXMucGxheVNlZWtWYWx1ZSk7CiAgICAgIHRoaXMucGxheVNlZWtWYWx1ZSArPSA1ICogMTAwMDsKCiAgICAgIC8vIOiuoeeul+aAu+aXtumVv++8jOehruS/neS4jeS8mui2heWHuuiMg+WbtAogICAgICB2YXIgdG90YWxEdXJhdGlvbiA9IDA7CiAgICAgIGlmICh0aGlzLmRldGFpbEZpbGVzICYmIHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoID4gMCkgewogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgaWYgKHRoaXMuZGV0YWlsRmlsZXNbaV0gJiYgdGhpcy5kZXRhaWxGaWxlc1tpXS50aW1lTGVuICE9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgdG90YWxEdXJhdGlvbiArPSB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW47CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAvLyDnoa7kv53kuI3kvJrotoXlh7rmgLvml7bplb8KICAgICAgICBpZiAodGhpcy5wbGF5U2Vla1ZhbHVlID4gdG90YWxEdXJhdGlvbikgewogICAgICAgICAgdGhpcy5wbGF5U2Vla1ZhbHVlID0gdG90YWxEdXJhdGlvbjsKICAgICAgICB9CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ+W/q+i/m+WQjnNlZWvlgLw6JywgdGhpcy5wbGF5U2Vla1ZhbHVlLCAn5oC75pe26ZW/OicsIHRvdGFsRHVyYXRpb24pOwogICAgICB0aGlzLnNtYXJ0U2Vla09yUmVsb2FkKCk7CiAgICB9LAogICAgc21hcnRTZWVrT3JSZWxvYWQ6IGZ1bmN0aW9uIHNtYXJ0U2Vla09yUmVsb2FkKCkgewogICAgICAvLyDmmbrog73liKTmlq3mmK/kvb/nlKhzZWVr6L+Y5piv6YeN5paw5Yqg6L29CiAgICAgIGNvbnNvbGUubG9nKCfmmbrog71zZWVr5Yik5patIC0g5b2T5YmNc2Vla+WAvDonLCB0aGlzLnBsYXlTZWVrVmFsdWUpOwoKICAgICAgLy8g6aaW5YWI56Gu5a6a55uu5qCH5paH5Lu257Si5byVCiAgICAgIHZhciB0YXJnZXRGaWxlSW5kZXggPSAtMTsKICAgICAgdmFyIGJhc2VTZWVrVmFsdWUgPSAwOwogICAgICBpZiAodGhpcy5kZXRhaWxGaWxlcyAmJiB0aGlzLmRldGFpbEZpbGVzLmxlbmd0aCA+IDApIHsKICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHZhciBmaWxlID0gdGhpcy5kZXRhaWxGaWxlc1tpXTsKICAgICAgICAgIGlmICghZmlsZSB8fCBmaWxlLnRpbWVMZW4gPT09IHVuZGVmaW5lZCkgY29udGludWU7CiAgICAgICAgICBpZiAodGhpcy5wbGF5U2Vla1ZhbHVlID49IGJhc2VTZWVrVmFsdWUgJiYgdGhpcy5wbGF5U2Vla1ZhbHVlIDw9IGJhc2VTZWVrVmFsdWUgKyBmaWxlLnRpbWVMZW4pIHsKICAgICAgICAgICAgdGFyZ2V0RmlsZUluZGV4ID0gaTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IGZpbGUudGltZUxlbjsKICAgICAgICB9CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ+ebruagh+aWh+S7tue0ouW8lTonLCB0YXJnZXRGaWxlSW5kZXgsICflvZPliY3mlofku7bntKLlvJU6JywgdGhpcy5jaG9vc2VGaWxlSW5kZXgpOwoKICAgICAgLy8g5aaC5p6c6ZyA6KaB5YiH5o2i5paH5Lu277yM5b+F6aG76YeN5paw5Yqg6L29CiAgICAgIGlmICh0YXJnZXRGaWxlSW5kZXggIT09IHRoaXMuY2hvb3NlRmlsZUluZGV4KSB7CiAgICAgICAgY29uc29sZS5sb2coJ+mcgOimgeWIh+aNouaWh+S7tu+8jOmHjeaWsOWKoOi9veaSreaUvuWZqCcpOwogICAgICAgIHRoaXMuY2hvb3NlRmlsZUluZGV4ID0gdGFyZ2V0RmlsZUluZGV4OwogICAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KHRhcmdldEZpbGVJbmRleCk7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5zdHJlYW1JbmZvICYmIHRoaXMuc3RyZWFtSW5mby5hcHAgJiYgdGhpcy5zdHJlYW1JbmZvLnN0cmVhbSkgewogICAgICAgIC8vIOWcqOWQjOS4gOaWh+S7tuWGhe+8jOS4lOa1geS/oeaBr+WtmOWcqO+8jOebtOaOpXNlZWsKICAgICAgICBjb25zb2xlLmxvZygn5Zyo5ZCM5LiA5paH5Lu25YaF77yM5omn6KGMc2Vla+aTjeS9nCcpOwogICAgICAgIHRoaXMuc2Vla1JlY29yZCgpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOa1geS/oeaBr+S4jeWtmOWcqO+8jOmHjeaWsOWKoOi9vQogICAgICAgIGNvbnNvbGUubG9nKCfmtYHkv6Hmga/kuI3lrZjlnKjvvIzph43mlrDliqDovb3mkq3mlL7lmagnKTsKICAgICAgICBpZiAodGFyZ2V0RmlsZUluZGV4ID49IDApIHsKICAgICAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KHRhcmdldEZpbGVJbmRleCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucGxheVJlY29yZCgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIHN0b3BQTGF5OiBmdW5jdGlvbiBzdG9wUExheSgpIHsKICAgICAgLy8g5YGc5q2iCiAgICAgIHRyeSB7CiAgICAgICAgaWYgKHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIpIHsKICAgICAgICAgIHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIuZGVzdHJveSgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+WBnOatouaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcik7CiAgICAgIH0KICAgICAgLy8g6YeN572u5pKt5pS+54q25oCB5ZKM6KeG6aKRVVJM77yM56Gu5L+d5LiL5qyh5pKt5pS+5pe26YeN5paw5Yqg6L29CiAgICAgIHRoaXMucGxheWluZyA9IGZhbHNlOwogICAgICB0aGlzLnZpZGVvVXJsID0gbnVsbDsKICAgICAgdGhpcy5wbGF5TG9hZGluZyA9IGZhbHNlOwoKICAgICAgLy8g6YeN572u5Yiw56ys5LiA5Liq5paH5Lu25ZKM5Yid5aeL5pe26Ze0CiAgICAgIHRoaXMuY2hvb3NlRmlsZUluZGV4ID0gMDsKICAgICAgdGhpcy5wbGF5U2Vla1ZhbHVlID0gMDsKICAgICAgdGhpcy5wbGF5ZXJUaW1lID0gMDsKICAgICAgaWYgKHRoaXMuZGV0YWlsRmlsZXMgJiYgdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGggPiAwICYmIHRoaXMuZGV0YWlsRmlsZXNbMF0pIHsKICAgICAgICBpZiAodGhpcy5kZXRhaWxGaWxlc1swXS5zdGFydFRpbWUgIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgdGhpcy5wbGF5VGltZSA9IHRoaXMuZGV0YWlsRmlsZXNbMF0uc3RhcnRUaW1lOwogICAgICAgICAgLy8g5ZCM5q2l5pu05paw5pe26Ze06L205pi+56S65Yiw56ys5LiA5Liq5paH5Lu255qE5byA5aeL5pe26Ze0CiAgICAgICAgICBpZiAodGhpcy4kcmVmcy5UaW1lbGluZSkgewogICAgICAgICAgICB0aGlzLiRyZWZzLlRpbWVsaW5lLnNldFRpbWUodGhpcy5wbGF5VGltZSk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUud2Fybign56ys5LiA5Liq5paH5Lu255qEc3RhcnRUaW1l5pyq5a6a5LmJJyk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5Y+v55So55qE5b2V5YOP5paH5Lu2Jyk7CiAgICAgIH0KCiAgICAgIC8vIOWBnOatouaXtumHjee9ruWAjemAn+S4ujFYCiAgICAgIGlmICh0aGlzLnBsYXlTcGVlZCAhPT0gMSkgewogICAgICAgIHRoaXMuY2hhbmdlUGxheVNwZWVkKDEpOwogICAgICB9CiAgICB9LAogICAgcGF1c2VQbGF5OiBmdW5jdGlvbiBwYXVzZVBsYXkoKSB7CiAgICAgIC8vIOaaguWBnAogICAgICB0cnkgewogICAgICAgIGlmICh0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBhdXNlKCk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUud2Fybign5pqC5YGc5pKt5pS+5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKTsKICAgICAgfQogICAgfSwKICAgIHBsYXk6IGZ1bmN0aW9uIHBsYXkoKSB7CiAgICAgIC8vIOajgOafpeaSreaUvuWZqOaYr+WQpuWtmOWcqOS4lOW3suWKoOi9vQogICAgICBpZiAodGhpcy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllciAmJiB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLmxvYWRlZCAmJiAhdGhpcy5wbGF5TG9hZGluZykgewogICAgICAgIC8vIOWwneivleaBouWkjeaSreaUvgogICAgICAgIHRyeSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnVuUGF1c2UoKTsKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgLy8g5b+955WlQWJvcnRFcnJvcu+8jOi/memAmuW4uOaYr+eUseS6juW/q+mAn+eahHBsYXkvcGF1c2Xmk43kvZzlr7zoh7TnmoQKICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCfmgaLlpI3mkq3mlL7lpLHotKXvvIzph43mlrDliqDovb3op4bpopE6JywgZXJyb3IpOwogICAgICAgICAgICB0aGlzLnBsYXlSZWNvcmQoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5pKt5pS+5Zmo5pyq5Yqg6L295oiW5bey6KKr6ZSA5q+B77yM6YeN5paw5Yqg6L296KeG6aKRCiAgICAgICAgdGhpcy5wbGF5UmVjb3JkKCk7CiAgICAgIH0KICAgIH0sCiAgICBmdWxsU2NyZWVuOiBmdW5jdGlvbiBmdWxsU2NyZWVuKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgLy8g5YWo5bGPCiAgICAgIGlmICh0aGlzLmlzRnVsbFNjcmVlbikgewogICAgICAgIHNjcmVlbmZ1bGwuZXhpdCgpOwogICAgICAgIHRoaXMuaXNGdWxsU2NyZWVuID0gZmFsc2U7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciBwbGF5ZXJXaWR0aCA9IHRoaXMuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWVyV2lkdGg7CiAgICAgIHZhciBwbGF5ZXJIZWlnaHQgPSB0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBsYXllckhlaWdodDsKICAgICAgc2NyZWVuZnVsbC5yZXF1ZXN0KGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdwbGF5ZXJCb3gnKSk7CiAgICAgIHNjcmVlbmZ1bGwub24oJ2NoYW5nZScsIGZ1bmN0aW9uIChldmVudCkgewogICAgICAgIF90aGlzMy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5yZXNpemUocGxheWVyV2lkdGgsIHBsYXllckhlaWdodCk7CiAgICAgICAgX3RoaXMzLmlzRnVsbFNjcmVlbiA9IHNjcmVlbmZ1bGwuaXNGdWxsc2NyZWVuOwogICAgICB9KTsKICAgICAgdGhpcy5pc0Z1bGxTY3JlZW4gPSB0cnVlOwogICAgfSwKICAgIGRhdGVDaGFuZ2U6IGZ1bmN0aW9uIGRhdGVDaGFuZ2UoKSB7CiAgICAgIHRoaXMuZGV0YWlsRmlsZXMgPSBbXTsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHZhciBjaG9vc2VGdWxsRGF0ZSA9IG5ldyBEYXRlKHRoaXMuY2hvb3NlRGF0ZSArICcgJyArIHRoaXMudGltZUZvcm1hdCk7CiAgICAgIGlmIChjaG9vc2VGdWxsRGF0ZS5nZXRGdWxsWWVhcigpICE9PSB0aGlzLnF1ZXJ5RGF0ZS5nZXRGdWxsWWVhcigpIHx8IGNob29zZUZ1bGxEYXRlLmdldE1vbnRoKCkgIT09IHRoaXMucXVlcnlEYXRlLmdldE1vbnRoKCkpIHsKICAgICAgICB0aGlzLnF1ZXJ5RGF0ZSA9IGNob29zZUZ1bGxEYXRlOwogICAgICAgIHRoaXMuZ2V0RGF0ZUluWWVhcigpOwogICAgICB9CiAgICAgIHRoaXMucXVlcnlSZWNvcmREZXRhaWxzKCk7CiAgICB9LAogICAgaW5maW5pdGVTY3JvbGw6IGZ1bmN0aW9uIGluZmluaXRlU2Nyb2xsKCkgewogICAgICBpZiAodGhpcy50b3RhbCA+IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoKSB7CiAgICAgICAgdGhpcy5jdXJyZW50UGFnZSsrOwogICAgICAgIHRoaXMucXVlcnlSZWNvcmREZXRhaWxzKCk7CiAgICAgIH0KICAgIH0sCiAgICBxdWVyeVJlY29yZERldGFpbHM6IGZ1bmN0aW9uIHF1ZXJ5UmVjb3JkRGV0YWlscyhjYWxsYmFjaykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy50aW1lU2VnbWVudHMgPSBbXTsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2Nsb3VkUmVjb3JkL3F1ZXJ5TGlzdCcsIHsKICAgICAgICBhcHA6IHRoaXMuYXBwLAogICAgICAgIHN0cmVhbTogdGhpcy5zdHJlYW0sCiAgICAgICAgc3RhcnRUaW1lOiB0aGlzLmNob29zZURhdGUgKyAnIDAwOjAwOjAwJywKICAgICAgICBlbmRUaW1lOiB0aGlzLmNob29zZURhdGUgKyAnIDIzOjU5OjU5JywKICAgICAgICBwYWdlOiB0aGlzLmN1cnJlbnRQYWdlLAogICAgICAgIGNvdW50OiB0aGlzLmNvdW50LAogICAgICAgIG1lZGlhU2VydmVySWQ6IHRoaXMubWVkaWFTZXJ2ZXJJZCwKICAgICAgICBhc2NPcmRlcjogdHJ1ZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgICAgX3RoaXM0LnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICBfdGhpczQuZGV0YWlsRmlsZXMgPSBfdGhpczQuZGV0YWlsRmlsZXMuY29uY2F0KGRhdGEubGlzdCk7CiAgICAgICAgdmFyIHRlbXAgPSBuZXcgU2V0KCk7CgogICAgICAgIC8vIOajgOafpeaYr+WQpuacieaWh+S7tuaVsOaNrgogICAgICAgIGlmIChfdGhpczQuZGV0YWlsRmlsZXMgJiYgX3RoaXM0LmRldGFpbEZpbGVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIF90aGlzNC5pbml0VGltZSA9IE51bWJlci5wYXJzZUludChfdGhpczQuZGV0YWlsRmlsZXNbMF0uc3RhcnRUaW1lKTsKICAgICAgICAgIC8vIOWIneWni+WMluaSreaUvuaXtumXtOS4uuesrOS4gOS4quaWh+S7tueahOW8gOWni+aXtumXtAogICAgICAgICAgaWYgKF90aGlzNC5wbGF5VGltZSA9PT0gbnVsbCkgewogICAgICAgICAgICBfdGhpczQucGxheVRpbWUgPSBfdGhpczQuZGV0YWlsRmlsZXNbMF0uc3RhcnRUaW1lOwogICAgICAgICAgICBfdGhpczQucGxheWVyVGltZSA9IDA7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5om+5Yiw5b2V5YOP5paH5Lu2Jyk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgX3RoaXM0LmRldGFpbEZpbGVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICB0ZW1wLmFkZChfdGhpczQuZGV0YWlsRmlsZXNbaV0ubWVkaWFTZXJ2ZXJJZCk7CiAgICAgICAgICBfdGhpczQudGltZVNlZ21lbnRzLnB1c2goewogICAgICAgICAgICBiZWdpblRpbWU6IE51bWJlci5wYXJzZUludChfdGhpczQuZGV0YWlsRmlsZXNbaV0uc3RhcnRUaW1lKSwKICAgICAgICAgICAgZW5kVGltZTogTnVtYmVyLnBhcnNlSW50KF90aGlzNC5kZXRhaWxGaWxlc1tpXS5lbmRUaW1lKSwKICAgICAgICAgICAgY29sb3I6ICcjMDE5MDFkJywKICAgICAgICAgICAgc3RhcnRSYXRpbzogMC43LAogICAgICAgICAgICBlbmRSYXRpbzogMC44NSwKICAgICAgICAgICAgaW5kZXg6IGkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICBfdGhpczQubWVkaWFTZXJ2ZXJMaXN0ID0gQXJyYXkuZnJvbSh0ZW1wKTsKICAgICAgICBpZiAoX3RoaXM0Lm1lZGlhU2VydmVyTGlzdC5sZW5ndGggPT09IDEpIHsKICAgICAgICAgIF90aGlzNC5tZWRpYVNlcnZlcklkID0gX3RoaXM0Lm1lZGlhU2VydmVyTGlzdFswXTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGNvbnNvbGUubG9nKGVycm9yKTsKICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICBpZiAoY2FsbGJhY2spIGNhbGxiYWNrKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGNob29zZUZpbGU6IGZ1bmN0aW9uIGNob29zZUZpbGUoaW5kZXgpIHsKICAgICAgLy8g5qOA5p+l57Si5byV5piv5ZCm5pyJ5pWICiAgICAgIGlmICghdGhpcy5kZXRhaWxGaWxlcyB8fCBpbmRleCA8IDAgfHwgaW5kZXggPj0gdGhpcy5kZXRhaWxGaWxlcy5sZW5ndGgpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfml6DmlYjnmoTmlofku7bntKLlvJU6JywgaW5kZXgsICfvvIzmlofku7bliJfooajplb/luqY6JywgdGhpcy5kZXRhaWxGaWxlcyA/IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoIDogMCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuY2hvb3NlRmlsZUluZGV4ID0gaW5kZXg7CiAgICAgIC8vIOiuvue9ruaSreaUvuaXtumXtOS4uumAieS4reaWh+S7tueahOW8gOWni+aXtumXtAogICAgICB2YXIgc2VsZWN0ZWRGaWxlID0gdGhpcy5kZXRhaWxGaWxlc1tpbmRleF07CiAgICAgIGlmICghc2VsZWN0ZWRGaWxlIHx8IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgPT09IHVuZGVmaW5lZCkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mAieS4reeahOaWh+S7tuaVsOaNruaXoOaViDonLCBzZWxlY3RlZEZpbGUpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnBsYXlUaW1lID0gc2VsZWN0ZWRGaWxlLnN0YXJ0VGltZTsKICAgICAgdGhpcy5wbGF5ZXJUaW1lID0gMDsgLy8g6YeN572u5pKt5pS+5Zmo5pe26Ze0CgogICAgICAvLyDorqHnrpfliLDpgInkuK3mlofku7bnmoRzZWVr5YC877yI57Sv6K6h5YmN6Z2i5omA5pyJ5paH5Lu255qE5pe26ZW/77yJCiAgICAgIHZhciBzZWVrVmFsdWUgPSAwOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGluZGV4OyBpKyspIHsKICAgICAgICBpZiAodGhpcy5kZXRhaWxGaWxlc1tpXSAmJiB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW4gIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgc2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbaV0udGltZUxlbjsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5wbGF5U2Vla1ZhbHVlID0gc2Vla1ZhbHVlOwoKICAgICAgLy8g5ZCM5q2l5pu05paw5pe26Ze06L205pi+56S65Yiw5paH5Lu25byA5aeL5pe26Ze0CiAgICAgIGlmICh0aGlzLiRyZWZzLlRpbWVsaW5lKSB7CiAgICAgICAgdGhpcy4kcmVmcy5UaW1lbGluZS5zZXRUaW1lKHRoaXMucGxheVRpbWUpOwogICAgICB9CiAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KGluZGV4KTsKICAgIH0sCiAgICBwbGF5UmVjb3JkOiBmdW5jdGlvbiBwbGF5UmVjb3JkKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6YeN5paw5Yqg6L295pW05L2T5b2V5YOP5rWBJyk7CgogICAgICAgICAgICAgIC8vIOW8uuWItumUgOavgeW9k+WJjeaSreaUvuWZqO+8jOehruS/nea4heeQhuaXp+eahOa1gQogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczUuZGVzdHJveUN1cnJlbnRQbGF5ZXIoKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF90aGlzNS5wbGF5TG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX3RoaXM1LiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvbG9hZFJlY29yZCcsIHsKICAgICAgICAgICAgICAgIGFwcDogX3RoaXM1LmFwcCwKICAgICAgICAgICAgICAgIHN0cmVhbTogX3RoaXM1LnN0cmVhbSwKICAgICAgICAgICAgICAgIGRhdGU6IF90aGlzNS5jaG9vc2VEYXRlCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoZGF0YSkgewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+WKoOi9veaVtOS9k+W9leWDj+a1geaIkOWKnzonLCBkYXRhKTsKICAgICAgICAgICAgICAgIF90aGlzNS5zdHJlYW1JbmZvID0gZGF0YTsKICAgICAgICAgICAgICAgIGlmIChsb2NhdGlvbi5wcm90b2NvbCA9PT0gJ2h0dHBzOicpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM1LnZpZGVvVXJsID0gZGF0YVsnaHR0cHNfZm1wNCddICsgJyZ0aW1lPScgKyBuZXcgRGF0ZSgpLmdldFRpbWUoKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS52aWRlb1VybCA9IGRhdGFbJ2ZtcDQnXSArICcmdGltZT0nICsgbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g5aaC5p6c5pyJc2Vla+WAvO+8jOW7tui/n+aJp+ihjHNlZWsKICAgICAgICAgICAgICAgIGlmIChfdGhpczUucGxheVNlZWtWYWx1ZSA+IDApIHsKICAgICAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LnNlZWtSZWNvcmQoKTsKICAgICAgICAgICAgICAgICAgfSwgMTAwMCk7IC8vIOe7meaSreaUvuWZqOWKoOi9veaXtumXtAogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295pW05L2T5b2V5YOP5rWB5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAgICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzNS5wbGF5TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHBsYXlSZWNvcmRCeUZpbGVJbmRleDogZnVuY3Rpb24gcGxheVJlY29yZEJ5RmlsZUluZGV4KGZpbGVJbmRleCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3IoKS53KGZ1bmN0aW9uIChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5oyH5a6a5paH5Lu257Si5byVOicsIGZpbGVJbmRleCwgJ++8jHNlZWvlgLw6JywgX3RoaXM2LnBsYXlTZWVrVmFsdWUpOwoKICAgICAgICAgICAgICAvLyDpqozor4Hmlofku7bntKLlvJUKICAgICAgICAgICAgICBpZiAoISghX3RoaXM2LmRldGFpbEZpbGVzIHx8IGZpbGVJbmRleCA8IDAgfHwgZmlsZUluZGV4ID49IF90aGlzNi5kZXRhaWxGaWxlcy5sZW5ndGgpKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDE7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5peg5pWI55qE5paH5Lu257Si5byVOicsIGZpbGVJbmRleCk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5hKDIpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczYuZGVzdHJveUN1cnJlbnRQbGF5ZXIoKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF90aGlzNi5wbGF5TG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX3RoaXM2LiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvbG9hZFJlY29yZEJ5RmlsZUluZGV4JywgewogICAgICAgICAgICAgICAgYXBwOiBfdGhpczYuYXBwLAogICAgICAgICAgICAgICAgc3RyZWFtOiBfdGhpczYuc3RyZWFtLAogICAgICAgICAgICAgICAgZGF0ZTogX3RoaXM2LmNob29zZURhdGUsCiAgICAgICAgICAgICAgICBmaWxlSW5kZXg6IGZpbGVJbmRleAogICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKGRhdGEpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfliqDovb3mlofku7bmiJDlip86JywgZGF0YSk7CiAgICAgICAgICAgICAgICBfdGhpczYuc3RyZWFtSW5mbyA9IGRhdGE7CiAgICAgICAgICAgICAgICBpZiAobG9jYXRpb24ucHJvdG9jb2wgPT09ICdodHRwczonKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNi52aWRlb1VybCA9IGRhdGFbJ2h0dHBzX2ZtcDQnXSArICcmdGltZT0nICsgbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczYudmlkZW9VcmwgPSBkYXRhWydmbXA0J10gKyAnJnRpbWU9JyArIG5ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOabtOaWsOaSreaUvuaXtumXtOeKtuaAgQogICAgICAgICAgICAgICAgdmFyIHNlbGVjdGVkRmlsZSA9IF90aGlzNi5kZXRhaWxGaWxlc1tmaWxlSW5kZXhdOwogICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkRmlsZSAmJiBzZWxlY3RlZEZpbGUuc3RhcnRUaW1lICE9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgICAgICAgLy8g6K6h566X5paH5Lu25YaF55qE5YGP56e75pe26Ze0CiAgICAgICAgICAgICAgICAgIHZhciBiYXNlU2Vla1ZhbHVlID0gMDsKICAgICAgICAgICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBmaWxlSW5kZXg7IGkrKykgewogICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczYuZGV0YWlsRmlsZXNbaV0gJiYgX3RoaXM2LmRldGFpbEZpbGVzW2ldLnRpbWVMZW4gIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgICAgICAgICAgICAgYmFzZVNlZWtWYWx1ZSArPSBfdGhpczYuZGV0YWlsRmlsZXNbaV0udGltZUxlbjsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgdmFyIG9mZnNldEluRmlsZSA9IF90aGlzNi5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZTsKICAgICAgICAgICAgICAgICAgX3RoaXM2LnBsYXlUaW1lID0gc2VsZWN0ZWRGaWxlLnN0YXJ0VGltZSArIG9mZnNldEluRmlsZTsKICAgICAgICAgICAgICAgICAgX3RoaXM2LnBsYXllclRpbWUgPSBvZmZzZXRJbkZpbGU7CiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDmkq3mlL7ml7bpl7TnirbmgIE6JywgewogICAgICAgICAgICAgICAgICAgIGZpbGVJbmRleDogZmlsZUluZGV4LAogICAgICAgICAgICAgICAgICAgIGJhc2VTZWVrVmFsdWU6IGJhc2VTZWVrVmFsdWUsCiAgICAgICAgICAgICAgICAgICAgb2Zmc2V0SW5GaWxlOiBvZmZzZXRJbkZpbGUsCiAgICAgICAgICAgICAgICAgICAgcGxheVRpbWU6IF90aGlzNi5wbGF5VGltZSwKICAgICAgICAgICAgICAgICAgICBwbGF5ZXJUaW1lOiBfdGhpczYucGxheWVyVGltZQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmnIlzZWVr5YC877yM5bu26L+f5omn6KGMc2Vla+WumuS9jQogICAgICAgICAgICAgICAgaWYgKF90aGlzNi5wbGF5U2Vla1ZhbHVlID4gMCkgewogICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAvLyDorqHnrpflvZPliY3mlofku7blhoXnmoTnm7jlr7lzZWVr5YC8CiAgICAgICAgICAgICAgICAgICAgdmFyIGJhc2VTZWVrVmFsdWUgPSAwOwogICAgICAgICAgICAgICAgICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBmaWxlSW5kZXg7IF9pKyspIHsKICAgICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczYuZGV0YWlsRmlsZXNbX2ldICYmIF90aGlzNi5kZXRhaWxGaWxlc1tfaV0udGltZUxlbiAhPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGJhc2VTZWVrVmFsdWUgKz0gX3RoaXM2LmRldGFpbEZpbGVzW19pXS50aW1lTGVuOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB2YXIgZmlsZVNlZWtWYWx1ZSA9IF90aGlzNi5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZTsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5omn6KGMc2Vla+WumuS9jSAtIOWFqOWxgHNlZWvlgLw6JywgX3RoaXM2LnBsYXlTZWVrVmFsdWUsICfvvIzmlofku7blhoVzZWVr5YC8OicsIGZpbGVTZWVrVmFsdWUpOwoKICAgICAgICAgICAgICAgICAgICAvLyDmo4Dmn6VzZWVr5YC85piv5ZCm5Zyo5b2T5YmN5paH5Lu26IyD5Zu05YaFCiAgICAgICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkRmlsZSAmJiBzZWxlY3RlZEZpbGUudGltZUxlbiAhPT0gdW5kZWZpbmVkICYmIGZpbGVTZWVrVmFsdWUgPj0gMCAmJiBmaWxlU2Vla1ZhbHVlIDw9IHNlbGVjdGVkRmlsZS50aW1lTGVuKSB7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuc2Vla1JlY29yZCgpOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ3NlZWvlgLzotoXlh7rlvZPliY3mlofku7bojIPlm7TvvIzot7Pov4dzZWVr5pON5L2cIC0gZmlsZVNlZWtWYWx1ZTonLCBmaWxlU2Vla1ZhbHVlLCAn77yM5paH5Lu25pe26ZW/OicsIHNlbGVjdGVkRmlsZSA/IHNlbGVjdGVkRmlsZS50aW1lTGVuIDogJ3Vua25vd24nKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0sIDEwMDApOyAvLyDnu5nmkq3mlL7lmajliqDovb3ml7bpl7QKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaWh+S7tuWksei0pTonLCBlcnJvcik7CiAgICAgICAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBfdGhpczYucGxheUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGRlc3Ryb3lDdXJyZW50UGxheWVyOiBmdW5jdGlvbiBkZXN0cm95Q3VycmVudFBsYXllcigpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yKCkubShmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgX3Q7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQzLm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIC8vIOW8uuWItumUgOavgeW9k+WJjeaSreaUvuWZqO+8jOehruS/nea4heeQhuaXp+eahOa1gQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflvLrliLbplIDmr4HlvZPliY3mkq3mlL7lmagnKTsKICAgICAgICAgICAgICBfY29udGV4dDMucCA9IDE7CiAgICAgICAgICAgICAgaWYgKCFfdGhpczcuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyDlhYjmmoLlgZzmkq3mlL4KICAgICAgICAgICAgICBpZiAoX3RoaXM3LiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBhdXNlKSB7CiAgICAgICAgICAgICAgICBfdGhpczcuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGF1c2UoKTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOeri+WNs+mUgOavgeaSreaUvuWZqAogICAgICAgICAgICAgIF90aGlzNy4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5kZXN0cm95KCk7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqOmUgOavgeaIkOWKnycpOwoKICAgICAgICAgICAgICAvLyDnu5nplIDmr4Hmk43kvZzkuIDngrnml7bpl7TlrozmiJDvvIznoa7kv51ET03lrozlhajmuIXnkIYKICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gc2V0VGltZW91dChyZXNvbHZlLCAyMDApOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgLy8g6YeN572u54q25oCBCiAgICAgICAgICAgICAgX3RoaXM3LnBsYXlpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczcudmlkZW9VcmwgPSBudWxsOwogICAgICAgICAgICAgIF90aGlzNy5zdHJlYW1JbmZvID0gbnVsbDsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo54q25oCB5bey6YeN572uJyk7CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA0OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQzLnAgPSAzOwogICAgICAgICAgICAgIF90ID0gX2NvbnRleHQzLnY7CiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCflvLrliLbplIDmr4Hmkq3mlL7lmajml7blh7rnjrDplJnor686JywgX3QpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzLCBudWxsLCBbWzEsIDNdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHNlZWtSZWNvcmQ6IGZ1bmN0aW9uIHNlZWtSZWNvcmQoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICAvLyDmo4Dmn6Xmkq3mlL7lmajlkozmtYHkv6Hmga/mmK/lkKblh4blpIflpb0KICAgICAgaWYgKCF0aGlzLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyIHx8ICF0aGlzLnN0cmVhbUluZm8pIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+aSreaUvuWZqOaIlua1geS/oeaBr+acquWHhuWkh+Wlve+8jOi3s+i/h3NlZWvmk43kvZwnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOmYsuaKluWkhOeQhu+8mua4hemZpOS5i+WJjeeahOWumuaXtuWZqAogICAgICBpZiAodGhpcy5zZWVrVGltZXIpIHsKICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5zZWVrVGltZXIpOwogICAgICB9CgogICAgICAvLyDlu7bov5/miafooYxzZWVr77yM6YG/5YWN6aKR57mB5pON5L2cCiAgICAgIHRoaXMuc2Vla1RpbWVyID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM4LmRvU2Vla1JlY29yZCgpOwogICAgICB9LCAzMDApOyAvLyAzMDBtc+mYsuaKlgogICAgfSwKICAgIGRvU2Vla1JlY29yZDogZnVuY3Rpb24gZG9TZWVrUmVjb3JkKCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3IoKS5tKGZ1bmN0aW9uIF9jYWxsZWU0KCkgewogICAgICAgIHZhciBiYXNlU2Vla1ZhbHVlLCBpLCBmaWxlU2Vla1ZhbHVlLCBjdXJyZW50RmlsZSwgYWRqdXN0ZWRTZWVrVmFsdWUsIHdhc1BsYXlpbmcsIGZpbmFsRmlsZVNlZWtWYWx1ZSwgc2Vla1ZhbHVlVG9TZW5kLCBfdDI7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvcigpLncoZnVuY3Rpb24gKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGlmICghKCFfdGhpczkuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgfHwgIV90aGlzOS5zdHJlYW1JbmZvKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5oiW5rWB5L+h5oGv5pyq5YeG5aSH5aW977yM5Y+W5raIc2Vla+aTjeS9nCcpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIC8vIOiuoeeul+W9k+WJjeaWh+S7tuWGheeahOebuOWvuXNlZWvlgLwKICAgICAgICAgICAgICBiYXNlU2Vla1ZhbHVlID0gMDsKICAgICAgICAgICAgICBpZiAoX3RoaXM5LmNob29zZUZpbGVJbmRleCAhPT0gbnVsbCkgewogICAgICAgICAgICAgICAgZm9yIChpID0gMDsgaSA8IF90aGlzOS5jaG9vc2VGaWxlSW5kZXg7IGkrKykgewogICAgICAgICAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IF90aGlzOS5kZXRhaWxGaWxlc1tpXS50aW1lTGVuOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgLy8g5b2T5YmN5paH5Lu25YaF55qEc2Vla+WAvO+8iOavq+enku+8iQogICAgICAgICAgICAgIGZpbGVTZWVrVmFsdWUgPSBfdGhpczkucGxheVNlZWtWYWx1ZSAtIGJhc2VTZWVrVmFsdWU7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+aJp+ihjHNlZWvlrprkvY0gLSDlhajlsYBzZWVr5YC8OicsIF90aGlzOS5wbGF5U2Vla1ZhbHVlLCAnbXPvvIzmlofku7blhoVzZWVr5YC8OicsIGZpbGVTZWVrVmFsdWUsICdtcycpOwoKICAgICAgICAgICAgICAvLyDpqozor4FzZWVr5YC85piv5ZCm5Zyo5ZCI55CG6IyD5Zu05YaFCiAgICAgICAgICAgICAgaWYgKF90aGlzOS5jaG9vc2VGaWxlSW5kZXggIT09IG51bGwgJiYgX3RoaXM5LmRldGFpbEZpbGVzW190aGlzOS5jaG9vc2VGaWxlSW5kZXhdKSB7CiAgICAgICAgICAgICAgICBjdXJyZW50RmlsZSA9IF90aGlzOS5kZXRhaWxGaWxlc1tfdGhpczkuY2hvb3NlRmlsZUluZGV4XTsKICAgICAgICAgICAgICAgIGlmIChmaWxlU2Vla1ZhbHVlIDwgMCB8fCBmaWxlU2Vla1ZhbHVlID4gY3VycmVudEZpbGUudGltZUxlbikgewogICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ3NlZWvlgLzotoXlh7rlvZPliY3mlofku7bojIPlm7TvvIzosIPmlbTliLDmlofku7bovrnnlYwgLSBmaWxlU2Vla1ZhbHVlOicsIGZpbGVTZWVrVmFsdWUsICfvvIzmlofku7bml7bplb86JywgY3VycmVudEZpbGUudGltZUxlbik7CiAgICAgICAgICAgICAgICAgIC8vIOiwg+aVtOWIsOaWh+S7tui+ueeVjAogICAgICAgICAgICAgICAgICBhZGp1c3RlZFNlZWtWYWx1ZSA9IE1hdGgubWF4KDAsIE1hdGgubWluKGZpbGVTZWVrVmFsdWUsIGN1cnJlbnRGaWxlLnRpbWVMZW4pKTsKICAgICAgICAgICAgICAgICAgX3RoaXM5LnBsYXlTZWVrVmFsdWUgPSBiYXNlU2Vla1ZhbHVlICsgYWRqdXN0ZWRTZWVrVmFsdWU7CiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfosIPmlbTlkI7nmoRzZWVr5YC8OicsIF90aGlzOS5wbGF5U2Vla1ZhbHVlKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIC8vIOiusOW9leaSreaUvueKtuaAge+8jOeUqOS6jnNlZWvlkI7mgaLlpI0KICAgICAgICAgICAgICB3YXNQbGF5aW5nID0gX3RoaXM5LiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBsYXlpbmc7IC8vIOaaguWBnOaSreaUvuWZqO+8jOmBv+WFjXNlZWvml7bnmoTnirbmgIHlhrLnqoEKICAgICAgICAgICAgICBpZiAoISh3YXNQbGF5aW5nICYmIF90aGlzOS4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wYXVzZSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDI7CiAgICAgICAgICAgICAgX3RoaXM5LiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBhdXNlKCk7CiAgICAgICAgICAgICAgLy8g57uZ5pqC5YGc5pON5L2c5LiA54K55pe26Ze05a6M5oiQCiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSkgewogICAgICAgICAgICAgICAgcmV0dXJuIHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gNTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0NC5wID0gNDsKICAgICAgICAgICAgICBfdDIgPSBfY29udGV4dDQudjsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+aaguWBnOaSreaUvuWZqOaXtuWHuueOsOmUmeivrzonLCBfdDIpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgLy8g6YeN5paw6K6h566X5pyA57uI55qE5paH5Lu25YaFc2Vla+WAvO+8iOWPr+iDveW3sue7j+iiq+iwg+aVtOi/h++8iQogICAgICAgICAgICAgIGZpbmFsRmlsZVNlZWtWYWx1ZSA9IF90aGlzOS5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZTsgLy8g5YWz6ZSu5L+u5aSN77ya5a+55LqO5oyJ5paH5Lu257Si5byV5Yqg6L2955qE5rWB77yMWkxNZWRpYUtpdOacn+acm+eahOaYr+aWh+S7tuWGheeahOebuOWvueaXtumXtAogICAgICAgICAgICAgIC8vIOS9huaYr+WvueS6juesrOS4gOS4quaWh+S7tu+8iOe0ouW8lTDvvInvvIzlpoLmnpzmmK/pgJrov4dsb2FkUmVjb3Jk5Yqg6L2955qE77yM5Y+v6IO95pyf5pyb5YWo5bGA5pe26Ze0CiAgICAgICAgICAgICAgc2Vla1ZhbHVlVG9TZW5kID0gZmluYWxGaWxlU2Vla1ZhbHVlOyAvLyDmo4Dmn6XmtYHlkI3np7DmmK/lkKbljIXlkKvmlofku7bntKLlvJXvvIhfMCwgXzEsIF8y562J77yJCiAgICAgICAgICAgICAgaWYgKF90aGlzOS5zdHJlYW1JbmZvLnN0cmVhbSAmJiBfdGhpczkuc3RyZWFtSW5mby5zdHJlYW0uaW5jbHVkZXMoJ18nICsgX3RoaXM5LmNob29zZUZpbGVJbmRleCkpIHsKICAgICAgICAgICAgICAgIC8vIOi/meaYr+aMieaWh+S7tue0ouW8leWKoOi9veeahOa1ge+8jOS9v+eUqOaWh+S7tuWGheebuOWvueaXtumXtAogICAgICAgICAgICAgICAgc2Vla1ZhbHVlVG9TZW5kID0gZmluYWxGaWxlU2Vla1ZhbHVlOwogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+ajgOa1i+WIsOaMieaWh+S7tue0ouW8leWKoOi9veeahOa1ge+8jOS9v+eUqOaWh+S7tuWGhXNlZWvlgLw6Jywgc2Vla1ZhbHVlVG9TZW5kLCAnbXMnKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy8g6L+Z5Y+v6IO95piv5pW05L2T5b2V5YOP5rWB77yM5L2/55So5YWo5bGA5pe26Ze0CiAgICAgICAgICAgICAgICBzZWVrVmFsdWVUb1NlbmQgPSBfdGhpczkucGxheVNlZWtWYWx1ZTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDmlbTkvZPlvZXlg4/mtYHvvIzkvb/nlKjlhajlsYBzZWVr5YC8OicsIHNlZWtWYWx1ZVRvU2VuZCwgJ21zJyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzOS4kc3RvcmUuZGlzcGF0Y2goJ2Nsb3VkUmVjb3JkL3NlZWsnLCB7CiAgICAgICAgICAgICAgICBtZWRpYVNlcnZlcklkOiBfdGhpczkuc3RyZWFtSW5mby5tZWRpYVNlcnZlcklkLAogICAgICAgICAgICAgICAgYXBwOiBfdGhpczkuc3RyZWFtSW5mby5hcHAsCiAgICAgICAgICAgICAgICBzdHJlYW06IF90aGlzOS5zdHJlYW1JbmZvLnN0cmVhbSwKICAgICAgICAgICAgICAgIHNlZWs6IHNlZWtWYWx1ZVRvU2VuZCwKICAgICAgICAgICAgICAgIHNjaGVtYTogJ2ZtcDQnCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5ZCO56uvc2Vla+aTjeS9nOaIkOWKnyAtIOWPkemAgeeahHNlZWvlgLw6Jywgc2Vla1ZhbHVlVG9TZW5kLCAnbXMnKTsKCiAgICAgICAgICAgICAgICAvLyDlkI7nq69zZWVr5oiQ5Yqf5ZCO77yM5ZCM5q2l5YmN56uv5pKt5pS+5ZmoCiAgICAgICAgICAgICAgICBfdGhpczkuc3luY1BsYXllclNlZWsod2FzUGxheWluZyk7CiAgICAgICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgICAgICAgICAvLyDpnZnpu5jlpITnkIZzZWVr6ZSZ6K+v77yM5LiN5b2x5ZON55So5oi35L2T6aqMCiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ3NlZWvmk43kvZzlpLHotKU6JywgZXJyb3IpOwoKICAgICAgICAgICAgICAgIC8vIOWNs+S9v+WQjuerr3NlZWvlpLHotKXvvIzkuZ/lsJ3or5XliY3nq69zZWVrCiAgICAgICAgICAgICAgICBfdGhpczkuc3luY1BsYXllclNlZWsoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCwgbnVsbCwgW1syLCA0XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBzeW5jUGxheWVyU2VlazogZnVuY3Rpb24gc3luY1BsYXllclNlZWsoKSB7CiAgICAgIHZhciBfdGhpczAgPSB0aGlzOwogICAgICAvLyDorqHnrpfmkq3mlL7lmajpnIDopoFzZWVr5Yiw55qE5pe26Ze077yI56eS77yJCiAgICAgIC8vIHBsYXlTZWVrVmFsdWXmmK/ku47lvZXlg4/lvIDlp4vnmoTmr6vnp5LmlbDvvIzpnIDopoHovazmjaLkuLrlvZPliY3mlofku7blhoXnmoTnp5LmlbAKICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsICYmIHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdKSB7CiAgICAgICAgdmFyIGJhc2VTZWVrVmFsdWUgPSAwOwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5jaG9vc2VGaWxlSW5kZXg7IGkrKykgewogICAgICAgICAgYmFzZVNlZWtWYWx1ZSArPSB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW47CiAgICAgICAgfQoKICAgICAgICAvLyDorqHnrpflnKjlvZPliY3mlofku7blhoXnmoTlgY/np7vml7bpl7TvvIjmr6vnp5LvvIkKICAgICAgICB2YXIgb2Zmc2V0SW5GaWxlID0gdGhpcy5wbGF5U2Vla1ZhbHVlIC0gYmFzZVNlZWtWYWx1ZTsKICAgICAgICAvLyDovazmjaLkuLrnp5IKICAgICAgICB2YXIgc2Vla1RpbWVJblNlY29uZHMgPSBvZmZzZXRJbkZpbGUgLyAxMDAwOwogICAgICAgIGNvbnNvbGUubG9nKCfliY3nq6/mkq3mlL7lmahzZWVr5YiwOicsIHNlZWtUaW1lSW5TZWNvbmRzLCAn56eS77yI5paH5Lu25YaF5YGP56e777yJJyk7CgogICAgICAgIC8vIOeri+WNs+abtOaWsOaYvuekuuaXtumXtO+8jOS4jeetieW+heaSreaUvuWZqOWbnuiwgwogICAgICAgIHRoaXMudXBkYXRlRGlzcGxheVRpbWUoKTsKCiAgICAgICAgLy8g5bu26L+f5LiA54K55pe26Ze077yM56Gu5L+d5ZCO56uvc2Vla+aTjeS9nOWujOaIkAogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WJjeerr+aSreaUvuWZqHNlZWvmk43kvZwnKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCfmkq3mlL7lmajnirbmgIHmo4Dmn6U6JywgewogICAgICAgICAgICBwbGF5ZXJFeGlzdHM6ICEhX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLAogICAgICAgICAgICBzZWVrTWV0aG9kRXhpc3RzOiAhIShfdGhpczAuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIgJiYgX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnNlZWspLAogICAgICAgICAgICBwbGF5ZXJMb2FkZWQ6ICEhKF90aGlzMC4kcmVmcy5yZWNvcmRWaWRlb1BsYXllciAmJiBfdGhpczAuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIubG9hZGVkKSwKICAgICAgICAgICAgcGxheWluZzogISEoX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmIF90aGlzMC4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5wbGF5aW5nKQogICAgICAgICAgfSk7CiAgICAgICAgICBpZiAoX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyICYmIF90aGlzMC4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5zZWVrKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfosIPnlKjmkq3mlL7lmahzZWVr5pa55rOV77yM55uu5qCH5pe26Ze0OicsIHNlZWtUaW1lSW5TZWNvbmRzLCAn56eSJyk7CiAgICAgICAgICAgIHZhciBzZWVrU3VjY2VzcyA9IF90aGlzMC4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci5zZWVrKHNlZWtUaW1lSW5TZWNvbmRzKTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ+aSreaUvuWZqHNlZWvmlrnms5Xov5Tlm57lgLw6Jywgc2Vla1N1Y2Nlc3MpOwoKICAgICAgICAgICAgLy8gc2Vla+aIkOWKn+WQjuWGjeasoeabtOaWsOaYvuekuuaXtumXtAogICAgICAgICAgICBpZiAoc2Vla1N1Y2Nlc3MpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5YmN56uv5pKt5pS+5Zmoc2Vla+aIkOWKnycpOwogICAgICAgICAgICAgIC8vIOW7tui/n+S4gOeCueaXtumXtOWGjeabtOaWsO+8jOehruS/neaSreaUvuWZqOWGhemDqOeKtuaAgeW3suabtOaWsAogICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgX3RoaXMwLnVwZGF0ZURpc3BsYXlUaW1lKCk7CiAgICAgICAgICAgICAgfSwgMTAwKTsKCiAgICAgICAgICAgICAgLy8gc2Vla+aIkOWKn+WQjuiHquWKqOW8gOWni+aSreaUvu+8iOaXoOiuuuS5i+WJjeaYr+WQpuWcqOaSreaUvu+8iQogICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgaWYgKF90aGlzMC4kcmVmcy5yZWNvcmRWaWRlb1BsYXllcikgewogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnc2Vla+WujOaIkOWQjuiHquWKqOW8gOWni+aSreaUvu+8jOW9k+WJjeaSreaUvueKtuaAgTonLCBfdGhpczAuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWluZyk7CiAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgLy8g5peg6K665LmL5YmN5piv5ZCm5Zyo5pKt5pS+77yM6YO96YeN5paw5byA5aeL5pKt5pS+CiAgICAgICAgICAgICAgICAgICAgX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnVuUGF1c2UoKTsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnc3luY1BsYXllclNlZWvkuK3mkq3mlL7lmajlt7LlkK/liqgnKTsKICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgICAgICAvLyDlv73nlaVBYm9ydEVycm9y77yM6L+Z6YCa5bi45piv55Sx5LqO5b+r6YCf55qEcGxheS9wYXVzZeaTjeS9nOWvvOiHtOeahAogICAgICAgICAgICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsKICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybignc2Vla+WQjuiHquWKqOaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcik7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSwgNjAwKTsgLy8g56Gu5L+dc2Vla+aTjeS9nOWujOWFqOWujOaIkOWQjuWGjeW8gOWni+aSreaUvgogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign5YmN56uv5pKt5pS+5Zmoc2Vla+Wksei0pe+8jOWwneivlemHjeaWsOWKoOi9veaSreaUvuWZqCcpOwogICAgICAgICAgICAgIC8vIOWmguaenHNlZWvlpLHotKXvvIzlsJ3or5Xph43mlrDliqDovb3mkq3mlL7lmajliLDnm67moIfkvY3nva4KICAgICAgICAgICAgICBpZiAoX3RoaXMwLmNob29zZUZpbGVJbmRleCAhPT0gbnVsbCkgewogICAgICAgICAgICAgICAgX3RoaXMwLnBsYXlSZWNvcmRCeUZpbGVJbmRleChfdGhpczAuY2hvb3NlRmlsZUluZGV4KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pKt5pS+5Zmo5LiN5pSv5oyBc2Vla+aTjeS9nO+8jOWwneivlemHjeaWsOWKoOi9veaSreaUvuWZqCcpOwogICAgICAgICAgICAvLyDlpoLmnpzkuI3mlK/mjIFzZWVr77yM6YeN5paw5Yqg6L295pKt5pS+5Zmo5Yiw55uu5qCH5L2N572uCiAgICAgICAgICAgIGlmIChfdGhpczAuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgX3RoaXMwLnBsYXlSZWNvcmRCeUZpbGVJbmRleChfdGhpczAuY2hvb3NlRmlsZUluZGV4KTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g5aaC5p6c5LiN5pSv5oyBc2Vla++8jOiHquWKqOW8gOWni+aSreaUvu+8iOaXoOiuuuS5i+WJjeaYr+WQpuWcqOaSreaUvu+8iQogICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICBpZiAoX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmo5LiN5pSv5oyBc2Vla++8jOmHjeaWsOWKoOi9veWQjuiHquWKqOW8gOWni+aSreaUvu+8jOW9k+WJjeaSreaUvueKtuaAgTonLCBfdGhpczAuJHJlZnMucmVjb3JkVmlkZW9QbGF5ZXIucGxheWluZyk7CiAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAvLyDml6DorrrkuYvliY3mmK/lkKblnKjmkq3mlL7vvIzpg73ph43mlrDlvIDlp4vmkq3mlL4KICAgICAgICAgICAgICAgICAgX3RoaXMwLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnVuUGF1c2UoKTsKICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S4jeaUr+aMgXNlZWvml7bmkq3mlL7lmajlt7LlkK/liqgnKTsKICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgIC8vIOW/veeVpUFib3J0RXJyb3LvvIzov5npgJrluLjmmK/nlLHkuo7lv6vpgJ/nmoRwbGF5L3BhdXNl5pON5L2c5a+86Ie055qECiAgICAgICAgICAgICAgICAgIGlmIChlcnJvci5uYW1lICE9PSAnQWJvcnRFcnJvcicpIHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+mHjeaWsOWKoOi9veWQjuiHquWKqOaSreaUvuaXtuWHuueOsOmUmeivrzonLCBlcnJvcik7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIDEwMDApOyAvLyDnu5nph43mlrDliqDovb3mm7TlpJrml7bpl7QKICAgICAgICAgIH0KICAgICAgICB9LCA4MDApOyAvLyDlop7liqDlu7bov5/ml7bpl7TvvIznu5nlkI7nq69zZWVr5pON5L2c5pu05aSa5pe26Ze0CiAgICAgIH0KICAgIH0sCiAgICB1cGRhdGVEaXNwbGF5VGltZTogZnVuY3Rpb24gdXBkYXRlRGlzcGxheVRpbWUoKSB7CiAgICAgIC8vIOaJi+WKqOabtOaWsOaYvuekuuaXtumXtO+8jOehruS/nXNlZWvlkI7ml7bpl7TmmL7npLrmraPnoa4KICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsICYmIHRoaXMuZGV0YWlsRmlsZXMgJiYgdGhpcy5jaG9vc2VGaWxlSW5kZXggPj0gMCAmJiB0aGlzLmNob29zZUZpbGVJbmRleCA8IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoKSB7CiAgICAgICAgdmFyIHNlbGVjdGVkRmlsZSA9IHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdOwogICAgICAgIGlmICghc2VsZWN0ZWRGaWxlIHx8IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgY29uc29sZS53YXJuKCfpgInkuK3nmoTmlofku7bmlbDmja7ml6DmlYjvvIzml6Dms5Xmm7TmlrDmmL7npLrml7bpl7Q6Jywgc2VsZWN0ZWRGaWxlKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CgogICAgICAgIC8vIOiuoeeul+W9k+WJjeaWh+S7tueahOWfuuehgHNlZWvlgLwKICAgICAgICB2YXIgYmFzZVNlZWtWYWx1ZSA9IDA7CiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLmNob29zZUZpbGVJbmRleDsgaSsrKSB7CiAgICAgICAgICBpZiAodGhpcy5kZXRhaWxGaWxlc1tpXSAmJiB0aGlzLmRldGFpbEZpbGVzW2ldLnRpbWVMZW4gIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbaV0udGltZUxlbjsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOiuoeeul+WcqOW9k+WJjeaWh+S7tuWGheeahOWBj+enu+aXtumXtO+8iOavq+enku+8iQogICAgICAgIHZhciBvZmZzZXRJbkZpbGUgPSB0aGlzLnBsYXlTZWVrVmFsdWUgLSBiYXNlU2Vla1ZhbHVlOwoKICAgICAgICAvLyDmm7TmlrBwbGF5VGltZeS4uuebruagh+aXtumXtAogICAgICAgIHRoaXMucGxheVRpbWUgPSBzZWxlY3RlZEZpbGUuc3RhcnRUaW1lICsgb2Zmc2V0SW5GaWxlOwoKICAgICAgICAvLyDlkIzml7bmm7TmlrBwbGF5ZXJUaW1lCiAgICAgICAgdGhpcy5wbGF5ZXJUaW1lID0gb2Zmc2V0SW5GaWxlOwogICAgICAgIGNvbnNvbGUubG9nKCfmiYvliqjmm7TmlrDmmL7npLrml7bpl7Q6JywgewogICAgICAgICAgcGxheVRpbWU6IHRoaXMucGxheVRpbWUsCiAgICAgICAgICBwbGF5ZXJUaW1lOiB0aGlzLnBsYXllclRpbWUsCiAgICAgICAgICBvZmZzZXRJbkZpbGU6IG9mZnNldEluRmlsZSwKICAgICAgICAgIHNlbGVjdGVkRmlsZVN0YXJ0VGltZTogc2VsZWN0ZWRGaWxlLnN0YXJ0VGltZQogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIGNvbnNvbGUud2Fybign5peg5rOV5pu05paw5pi+56S65pe26Ze0IC0g5paH5Lu257Si5byV5peg5pWIOicsIHsKICAgICAgICAgIGNob29zZUZpbGVJbmRleDogdGhpcy5jaG9vc2VGaWxlSW5kZXgsCiAgICAgICAgICBkZXRhaWxGaWxlc0xlbmd0aDogdGhpcy5kZXRhaWxGaWxlcyA/IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoIDogMAogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgZG93bmxvYWRGaWxlOiBmdW5jdGlvbiBkb3dubG9hZEZpbGUoZmlsZSkgewogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvZ2V0UGxheVBhdGgnLCBmaWxlLmlkKS50aGVuKGZ1bmN0aW9uIChkYXRhKSB7CiAgICAgICAgdmFyIGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay50YXJnZXQgPSAnX2JsYW5rJzsKICAgICAgICBpZiAobG9jYXRpb24ucHJvdG9jb2wgPT09ICdodHRwczonKSB7CiAgICAgICAgICBsaW5rLmhyZWYgPSBkYXRhLmh0dHBzUGF0aCArICcmc2F2ZV9uYW1lPScgKyBmaWxlLmZpbGVOYW1lOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBsaW5rLmhyZWYgPSBkYXRhLmh0dHBQYXRoICsgJyZzYXZlX25hbWU9JyArIGZpbGUuZmlsZU5hbWU7CiAgICAgICAgfQogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5sb2coZXJyb3IpOwogICAgICB9KTsKICAgIH0sCiAgICBiYWNrVG9MaXN0OiBmdW5jdGlvbiBiYWNrVG9MaXN0KCkgewogICAgICB0aGlzLiRyb3V0ZXIuYmFjaygpOwogICAgfSwKICAgIGdldEZpbGVTaG93TmFtZTogZnVuY3Rpb24gZ2V0RmlsZVNob3dOYW1lKGl0ZW0pIHsKICAgICAgaWYgKCFpdGVtIHx8IGl0ZW0uc3RhcnRUaW1lID09PSB1bmRlZmluZWQgfHwgaXRlbS5lbmRUaW1lID09PSB1bmRlZmluZWQpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+aWh+S7tuaVsOaNruaXoOaViO+8jOaXoOazleagvOW8j+WMluaYvuekuuWQjeensDonLCBpdGVtKTsKICAgICAgICByZXR1cm4gJ+aXoOaViOaXtumXtCc7CiAgICAgIH0KICAgICAgcmV0dXJuIG1vbWVudChpdGVtLnN0YXJ0VGltZSkuZm9ybWF0KCdISDptbTpzcycpICsgJy0nICsgbW9tZW50KGl0ZW0uZW5kVGltZSkuZm9ybWF0KCdISDptbTpzcycpOwogICAgfSwKICAgIHNob3dQbGF5VGltZUNoYW5nZTogZnVuY3Rpb24gc2hvd1BsYXlUaW1lQ2hhbmdlKHZhbCkgewogICAgICAvLyB2YWzmmK/mkq3mlL7lmajnmoTlvZPliY3mkq3mlL7ml7bpl7TvvIjnp5LvvInvvIzpnIDopoHovazmjaLkuLrnu53lr7nml7bpl7TmiLMKICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSBudWxsICYmIHRoaXMuZGV0YWlsRmlsZXMgJiYgdGhpcy5jaG9vc2VGaWxlSW5kZXggPj0gMCAmJiB0aGlzLmNob29zZUZpbGVJbmRleCA8IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoKSB7CiAgICAgICAgdmFyIHNlbGVjdGVkRmlsZSA9IHRoaXMuZGV0YWlsRmlsZXNbdGhpcy5jaG9vc2VGaWxlSW5kZXhdOwogICAgICAgIGlmICghc2VsZWN0ZWRGaWxlIHx8IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgY29uc29sZS53YXJuKCfpgInkuK3nmoTmlofku7bmlbDmja7ml6DmlYjvvIzml6Dms5Xmm7TmlrDmkq3mlL7ml7bpl7Q6Jywgc2VsZWN0ZWRGaWxlKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CgogICAgICAgIC8vIOiuoeeul+W9k+WJjeaSreaUvueahOe7neWvueaXtumXtO+8muaWh+S7tuW8gOWni+aXtumXtCArIOaSreaUvuWZqOW9k+WJjeaXtumXtAogICAgICAgIHZhciBuZXdQbGF5VGltZSA9IHNlbGVjdGVkRmlsZS5zdGFydFRpbWUgKyB2YWwgKiAxMDAwOwogICAgICAgIHZhciBuZXdQbGF5ZXJUaW1lID0gdmFsICogMTAwMDsKCiAgICAgICAgLy8g5aaC5p6c5q2j5Zyo5ouW5Yqo5pe26Ze06L2077yM5b+955Wl5pKt5pS+5Zmo55qE5pe26Ze05Zue6LCD77yM6YG/5YWN5Yay56qBCiAgICAgICAgaWYgKHRoaXMudGltZWxpbmVDb250cm9sKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn5q2j5Zyo5ouW5Yqo5pe26Ze06L2077yM5b+955Wl5pKt5pS+5Zmo5pe26Ze05Zue6LCDJyk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQoKICAgICAgICAvLyDmm7TmlrDml7bpl7QKICAgICAgICB0aGlzLnBsYXlUaW1lID0gbmV3UGxheVRpbWU7CiAgICAgICAgdGhpcy5wbGF5ZXJUaW1lID0gbmV3UGxheWVyVGltZTsKCiAgICAgICAgLy8gY29uc29sZS5sb2coJ+aSreaUvuWZqOaXtumXtOabtOaWsDonLCB7CiAgICAgICAgLy8gICBwbGF5ZXJTZWNvbmRzOiB2YWwsCiAgICAgICAgLy8gICBwbGF5VGltZTogdGhpcy5wbGF5VGltZSwKICAgICAgICAvLyAgIHBsYXllclRpbWU6IHRoaXMucGxheWVyVGltZQogICAgICAgIC8vIH0pCiAgICAgIH0KICAgIH0sCiAgICBwbGF5aW5nQ2hhbmdlOiBmdW5jdGlvbiBwbGF5aW5nQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLnBsYXlpbmcgPSB2YWw7CiAgICB9LAogICAgcGxheVRpbWVDaGFuZ2U6IGZ1bmN0aW9uIHBsYXlUaW1lQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsID09PSB0aGlzLnBsYXlUaW1lKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMucGxheVRpbWUgPSB2YWw7CiAgICB9LAogICAgdGltZWxpbmVNb3VzZURvd246IGZ1bmN0aW9uIHRpbWVsaW5lTW91c2VEb3duKCkgewogICAgICB0aGlzLnRpbWVsaW5lQ29udHJvbCA9IHRydWU7CiAgICB9LAogICAgb25TZWVrRmluaXNoOiBmdW5jdGlvbiBvblNlZWtGaW5pc2goKSB7CiAgICAgIHZhciBfdGhpczEgPSB0aGlzOwogICAgICBjb25zb2xlLmxvZygn5pKt5pS+5Zmoc2Vla+WujOaIkOWbnuiwgycpOwogICAgICAvLyDmkq3mlL7lmahzZWVr5a6M5oiQ5ZCO77yM56Gu5L+d5pi+56S65pe26Ze05q2j56GuCiAgICAgIHRoaXMudXBkYXRlRGlzcGxheVRpbWUoKTsKCiAgICAgIC8vIHNlZWvlrozmiJDlkI7oh6rliqjlvIDlp4vmkq3mlL7vvIjnoa7kv53nlKjmiLfmi5bliqjml7bpl7TovbTlkI7og73nnIvliLDop4bpopHvvIkKICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgaWYgKF90aGlzMS4kcmVmcy5yZWNvcmRWaWRlb1BsYXllcikgewogICAgICAgICAgY29uc29sZS5sb2coJ3NlZWvlrozmiJDlm57osIPkuK3oh6rliqjlvIDlp4vmkq3mlL7vvIzlvZPliY3mkq3mlL7nirbmgIE6JywgX3RoaXMxLiRyZWZzLnJlY29yZFZpZGVvUGxheWVyLnBsYXlpbmcpOwogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgLy8g5peg6K665LmL5YmN5piv5ZCm5Zyo5pKt5pS+77yM6YO96YeN5paw5byA5aeL5pKt5pS+CiAgICAgICAgICAgIF90aGlzMS4kcmVmcy5yZWNvcmRWaWRlb1BsYXllci51blBhdXNlKCk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdzZWVr5a6M5oiQ5ZCO5pKt5pS+5Zmo5bey5ZCv5YqoJyk7CiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICBpZiAoZXJyb3IubmFtZSAhPT0gJ0Fib3J0RXJyb3InKSB7CiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdzZWVr5a6M5oiQ5Zue6LCD5Lit6Ieq5Yqo5pKt5pS+5pe25Ye6546w6ZSZ6K+vOicsIGVycm9yKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgMzAwKTsgLy8g57uZc2Vla+aTjeS9nOS4gOeCuemineWkluaXtumXtOWujOaIkAogICAgfSwKICAgIG1vdXNldXBUaW1lbGluZTogZnVuY3Rpb24gbW91c2V1cFRpbWVsaW5lKCkgewogICAgICBpZiAoIXRoaXMudGltZWxpbmVDb250cm9sKSB7CiAgICAgICAgdGhpcy50aW1lbGluZUNvbnRyb2wgPSBmYWxzZTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy50aW1lbGluZUNvbnRyb2wgPSBmYWxzZTsKICAgICAgY29uc29sZS5sb2coJ+aXtumXtOi9tOaLluWKqOe7k+adn++8jOW9k+WJjeaXtumXtDonLCB0aGlzLnBsYXlUaW1lLCAn77yM5paH5Lu25YiX6KGo6ZW/5bqmOicsIHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoKTsKCiAgICAgIC8vIOafpeaJvuaLluWKqOaXtumXtOeCueWvueW6lOeahOaWh+S7tgogICAgICB2YXIgdGFyZ2V0RmlsZUluZGV4ID0gLTE7CiAgICAgIHZhciB0aW1lT2Zmc2V0SW5GaWxlID0gMDsgLy8g5Zyo5paH5Lu25YaF55qE5pe26Ze05YGP56e777yI5q+r56eS77yJCgogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICB2YXIgaXRlbSA9IHRoaXMuZGV0YWlsRmlsZXNbaV07CiAgICAgICAgaWYgKCFpdGVtIHx8IGl0ZW0uc3RhcnRUaW1lID09PSB1bmRlZmluZWQgfHwgaXRlbS5lbmRUaW1lID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGNvbnNvbGUud2FybigiXHU2NTg3XHU0RUY2Ii5jb25jYXQoaSwgIlx1NjU3MFx1NjM2RVx1NjVFMFx1NjU0ODoiKSwgaXRlbSk7CiAgICAgICAgICBjb250aW51ZTsKICAgICAgICB9CiAgICAgICAgY29uc29sZS5sb2coIlx1NjhDMFx1NjdFNVx1NjU4N1x1NEVGNiIuY29uY2F0KGksICI6ICIpLmNvbmNhdChpdGVtLnN0YXJ0VGltZSwgIiAtICIpLmNvbmNhdChpdGVtLmVuZFRpbWUsICIsIFx1NUY1M1x1NTI0RFx1NjVGNlx1OTVGNDogIikuY29uY2F0KHRoaXMucGxheVRpbWUpKTsKICAgICAgICBpZiAodGhpcy5wbGF5VGltZSA+PSBpdGVtLnN0YXJ0VGltZSAmJiB0aGlzLnBsYXlUaW1lIDw9IGl0ZW0uZW5kVGltZSkgewogICAgICAgICAgdGFyZ2V0RmlsZUluZGV4ID0gaTsKICAgICAgICAgIHRpbWVPZmZzZXRJbkZpbGUgPSB0aGlzLnBsYXlUaW1lIC0gaXRlbS5zdGFydFRpbWU7CiAgICAgICAgICBjb25zb2xlLmxvZygiXHU2MjdFXHU1MjMwXHU3NkVFXHU2ODA3XHU2NTg3XHU0RUY2Ii5jb25jYXQoaSwgIlx1RkYwQ1x1NjU4N1x1NEVGNlx1NTE4NVx1NTA0Rlx1NzlGQlx1RkYxQSIpLmNvbmNhdCh0aW1lT2Zmc2V0SW5GaWxlLCAibXMiKSk7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHRhcmdldEZpbGVJbmRleCA9PT0gLTEpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+aLluWKqOaXtumXtOeCueS4jeWcqOS7u+S9leaWh+S7tuiMg+WbtOWGhe+8jOafpeaJvuacgOi/keeahOaWh+S7ticpOwogICAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOeyvuehruWMuemFje+8jOafpeaJvuacgOi/keeahOaWh+S7tgogICAgICAgIHZhciBtaW5EaXN0YW5jZSA9IEluZmluaXR5OwogICAgICAgIGZvciAodmFyIF9pMiA9IDA7IF9pMiA8IHRoaXMuZGV0YWlsRmlsZXMubGVuZ3RoOyBfaTIrKykgewogICAgICAgICAgdmFyIF9pdGVtID0gdGhpcy5kZXRhaWxGaWxlc1tfaTJdOwogICAgICAgICAgaWYgKCFfaXRlbSB8fCBfaXRlbS5zdGFydFRpbWUgPT09IHVuZGVmaW5lZCB8fCBfaXRlbS5lbmRUaW1lID09PSB1bmRlZmluZWQgfHwgX2l0ZW0udGltZUxlbiA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIGNvbnNvbGUud2FybigiXHU2NTg3XHU0RUY2Ii5jb25jYXQoX2kyLCAiXHU2NTcwXHU2MzZFXHU2NUUwXHU2NTQ4XHVGRjBDXHU4REYzXHU4RkM3OiIpLCBfaXRlbSk7CiAgICAgICAgICAgIGNvbnRpbnVlOwogICAgICAgICAgfQogICAgICAgICAgdmFyIGRpc3RhbmNlVG9TdGFydCA9IE1hdGguYWJzKHRoaXMucGxheVRpbWUgLSBfaXRlbS5zdGFydFRpbWUpOwogICAgICAgICAgdmFyIGRpc3RhbmNlVG9FbmQgPSBNYXRoLmFicyh0aGlzLnBsYXlUaW1lIC0gX2l0ZW0uZW5kVGltZSk7CiAgICAgICAgICB2YXIgbWluRmlsZURpc3RhbmNlID0gTWF0aC5taW4oZGlzdGFuY2VUb1N0YXJ0LCBkaXN0YW5jZVRvRW5kKTsKICAgICAgICAgIGlmIChtaW5GaWxlRGlzdGFuY2UgPCBtaW5EaXN0YW5jZSkgewogICAgICAgICAgICBtaW5EaXN0YW5jZSA9IG1pbkZpbGVEaXN0YW5jZTsKICAgICAgICAgICAgdGFyZ2V0RmlsZUluZGV4ID0gX2kyOwogICAgICAgICAgICAvLyDlpoLmnpzmm7TmjqXov5HlvIDlp4vml7bpl7TvvIzlgY/np7vkuLow77yb5aaC5p6c5pu05o6l6L+R57uT5p2f5pe26Ze077yM5YGP56e75Li65paH5Lu25pe26ZW/CiAgICAgICAgICAgIHRpbWVPZmZzZXRJbkZpbGUgPSBkaXN0YW5jZVRvU3RhcnQgPCBkaXN0YW5jZVRvRW5kID8gMCA6IF9pdGVtLnRpbWVMZW47CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGlmICh0YXJnZXRGaWxlSW5kZXggPT09IC0xKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfml6Dms5Xmib7liLDku7vkvZXlj6/mkq3mlL7nmoTmlofku7YnKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgY29uc29sZS5sb2coIlx1NEY3Rlx1NzUyOFx1NjcwMFx1OEZEMVx1NzY4NFx1NjU4N1x1NEVGNiIuY29uY2F0KHRhcmdldEZpbGVJbmRleCwgIlx1RkYwQ1x1NTA0Rlx1NzlGQlx1RkYxQSIpLmNvbmNhdCh0aW1lT2Zmc2V0SW5GaWxlLCAibXMiKSk7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coIlx1NjJENlx1NTJBOFx1NTIzMFx1NjU4N1x1NEVGNiIuY29uY2F0KHRhcmdldEZpbGVJbmRleCwgIlx1RkYwQ1x1NjVGNlx1OTVGNFx1NTA0Rlx1NzlGQlx1RkYxQSIpLmNvbmNhdCh0aW1lT2Zmc2V0SW5GaWxlLCAibXMiKSk7CgogICAgICAvLyDmo4Dmn6XmmK/lkKbpnIDopoHliIfmjaLmlofku7YKICAgICAgaWYgKHRoaXMuY2hvb3NlRmlsZUluZGV4ICE9PSB0YXJnZXRGaWxlSW5kZXgpIHsKICAgICAgICBjb25zb2xlLmxvZygiXHU1MjA3XHU2MzYyXHU2NTg3XHU0RUY2XHVGRjFBXHU0RUNFIi5jb25jYXQodGhpcy5jaG9vc2VGaWxlSW5kZXgsICJcdTUyMzAiKS5jb25jYXQodGFyZ2V0RmlsZUluZGV4KSk7CiAgICAgICAgLy8g5YiH5o2i5Yiw55uu5qCH5paH5Lu2CiAgICAgICAgdGhpcy5jaG9vc2VGaWxlSW5kZXggPSB0YXJnZXRGaWxlSW5kZXg7CgogICAgICAgIC8vIOiuoeeul+WIsOebruagh+aWh+S7tueahHNlZWvlgLzvvIjntK/orqHliY3pnaLmiYDmnInmlofku7bnmoTml7bplb/vvIkKICAgICAgICB2YXIgc2Vla1ZhbHVlID0gMDsKICAgICAgICBmb3IgKHZhciBfaTMgPSAwOyBfaTMgPCB0YXJnZXRGaWxlSW5kZXg7IF9pMysrKSB7CiAgICAgICAgICBpZiAodGhpcy5kZXRhaWxGaWxlc1tfaTNdICYmIHRoaXMuZGV0YWlsRmlsZXNbX2kzXS50aW1lTGVuICE9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgc2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbX2kzXS50aW1lTGVuOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICAvLyDliqDkuIrmlofku7blhoXnmoTlgY/np7vml7bpl7QKICAgICAgICBzZWVrVmFsdWUgKz0gdGltZU9mZnNldEluRmlsZTsKICAgICAgICB0aGlzLnBsYXlTZWVrVmFsdWUgPSBzZWVrVmFsdWU7CiAgICAgICAgY29uc29sZS5sb2coIlx1OEJBMVx1N0I5N1x1NzY4NHNlZWtcdTUwM0NcdUZGMUEiLmNvbmNhdChzZWVrVmFsdWUsICJtcyIpKTsKCiAgICAgICAgLy8g5Yqg6L2955uu5qCH5paH5Lu25bm2c2Vla+WIsOaMh+WumuS9jee9rgogICAgICAgIHRoaXMucGxheVJlY29yZEJ5RmlsZUluZGV4KHRhcmdldEZpbGVJbmRleCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coIlx1NTcyOFx1NUY1M1x1NTI0RFx1NjU4N1x1NEVGNiIuY29uY2F0KHRhcmdldEZpbGVJbmRleCwgIlx1NTE4NXNlZWtcdTUyMzBcdTUwNEZcdTc5RkJcdUZGMUEiKS5jb25jYXQodGltZU9mZnNldEluRmlsZSwgIm1zIikpOwogICAgICAgIC8vIOWcqOW9k+WJjeaWh+S7tuWGhXNlZWsKICAgICAgICAvLyDorqHnrpflvZPliY3mlofku7bnmoTln7rnoYBzZWVr5YC8CiAgICAgICAgdmFyIGJhc2VTZWVrVmFsdWUgPSAwOwogICAgICAgIGZvciAodmFyIF9pNCA9IDA7IF9pNCA8IHRhcmdldEZpbGVJbmRleDsgX2k0KyspIHsKICAgICAgICAgIGlmICh0aGlzLmRldGFpbEZpbGVzW19pNF0gJiYgdGhpcy5kZXRhaWxGaWxlc1tfaTRdLnRpbWVMZW4gIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgICBiYXNlU2Vla1ZhbHVlICs9IHRoaXMuZGV0YWlsRmlsZXNbX2k0XS50aW1lTGVuOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICAvLyDliqDkuIrmlofku7blhoXnmoTlgY/np7vml7bpl7QKICAgICAgICB0aGlzLnBsYXlTZWVrVmFsdWUgPSBiYXNlU2Vla1ZhbHVlICsgdGltZU9mZnNldEluRmlsZTsKICAgICAgICBjb25zb2xlLmxvZygiXHU2NTg3XHU0RUY2XHU1MTg1c2Vla1x1NTAzQ1x1RkYxQSIuY29uY2F0KHRoaXMucGxheVNlZWtWYWx1ZSwgIm1zIikpOwoKICAgICAgICAvLyDkvJjljJbvvJrlpoLmnpzmtYHkv6Hmga/lrZjlnKjvvIznm7TmjqVzZWVr77yb5ZCm5YiZ6YeN5paw5Yqg6L29CiAgICAgICAgaWYgKHRoaXMuc3RyZWFtSW5mbyAmJiB0aGlzLnN0cmVhbUluZm8uYXBwICYmIHRoaXMuc3RyZWFtSW5mby5zdHJlYW0pIHsKICAgICAgICAgIHRoaXMuc2Vla1JlY29yZCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn5rWB5L+h5oGv5LiN5a2Y5Zyo77yM6YeN5paw5Yqg6L295paH5Lu2Jyk7CiAgICAgICAgICB0aGlzLnBsYXlSZWNvcmRCeUZpbGVJbmRleCh0YXJnZXRGaWxlSW5kZXgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGdldFRpbWVGb3JGaWxlOiBmdW5jdGlvbiBnZXRUaW1lRm9yRmlsZShmaWxlKSB7CiAgICAgIGlmICghZmlsZSB8fCBmaWxlLnN0YXJ0VGltZSA9PT0gdW5kZWZpbmVkIHx8IGZpbGUuZW5kVGltZSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgY29uc29sZS53YXJuKCfmlofku7bmlbDmja7ml6DmlYjvvIzml6Dms5XorqHnrpfml7bpl7Q6JywgZmlsZSk7CiAgICAgICAgcmV0dXJuIFtuZXcgRGF0ZSgpLCBuZXcgRGF0ZSgpLCAwXTsKICAgICAgfQogICAgICB2YXIgc3RhclRpbWUgPSBuZXcgRGF0ZShmaWxlLnN0YXJ0VGltZSAqIDEwMDApOwogICAgICB2YXIgZW5kVGltZSA9IG5ldyBEYXRlKGZpbGUuZW5kVGltZSAqIDEwMDApOwogICAgICBpZiAodGhpcy5jaGVja0lzT3ZlcjI0aChzdGFyVGltZSwgZW5kVGltZSkpIHsKICAgICAgICBlbmRUaW1lID0gbmV3IERhdGUodGhpcy5jaG9vc2VEYXRlICsgJyAnICsgJzIzOjU5OjU5Jyk7CiAgICAgIH0KICAgICAgcmV0dXJuIFtzdGFyVGltZSwgZW5kVGltZSwgZW5kVGltZS5nZXRUaW1lKCkgLSBzdGFyVGltZS5nZXRUaW1lKCldOwogICAgfSwKICAgIGNoZWNrSXNPdmVyMjRoOiBmdW5jdGlvbiBjaGVja0lzT3ZlcjI0aChzdGFyVGltZSwgZW5kVGltZSkgewogICAgICByZXR1cm4gc3RhclRpbWUgPiBlbmRUaW1lOwogICAgfSwKICAgIHBsYXlUaW1lRm9ybWF0OiBmdW5jdGlvbiBwbGF5VGltZUZvcm1hdCh2YWwpIHsKICAgICAgdmFyIGggPSBwYXJzZUludCh2YWwgLyAzNjAwKTsKICAgICAgdmFyIG0gPSBwYXJzZUludCgodmFsIC0gaCAqIDM2MDApIC8gNjApOwogICAgICB2YXIgcyA9IHBhcnNlSW50KHZhbCAtIGggKiAzNjAwIC0gbSAqIDYwKTsKICAgICAgdmFyIGhTdHIgPSBoOwogICAgICB2YXIgbVN0ciA9IG07CiAgICAgIHZhciBzU3RyID0gczsKICAgICAgaWYgKGggPCAxMCkgewogICAgICAgIGhTdHIgPSAnMCcgKyBoU3RyOwogICAgICB9CiAgICAgIGlmIChtIDwgMTApIHsKICAgICAgICBtU3RyID0gJzAnICsgbVN0cjsKICAgICAgICBzOwogICAgICB9CiAgICAgIGlmIChzIDwgMTApIHsKICAgICAgICBzU3RyID0gJzAnICsgc1N0cjsKICAgICAgfQogICAgICByZXR1cm4gaFN0ciArICc6JyArIG1TdHIgKyAnOicgKyBzU3RyOwogICAgfSwKICAgIGdldERhdGVJblllYXI6IGZ1bmN0aW9uIGdldERhdGVJblllYXIoY2FsbGJhY2spIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICB0aGlzLmRhdGVGaWxlc09iaiA9IHt9OwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY2xvdWRSZWNvcmQvcXVlcnlMaXN0QnlEYXRhJywgewogICAgICAgIGFwcDogdGhpcy5hcHAsCiAgICAgICAgc3RyZWFtOiB0aGlzLnN0cmVhbSwKICAgICAgICB5ZWFyOiB0aGlzLnF1ZXJ5RGF0ZS5nZXRGdWxsWWVhcigpLAogICAgICAgIG1vbnRoOiB0aGlzLnF1ZXJ5RGF0ZS5nZXRNb250aCgpICsgMSwKICAgICAgICBtZWRpYVNlcnZlcklkOiB0aGlzLm1lZGlhU2VydmVySWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAoZGF0YSkgewogICAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgICAgICBfdGhpczEwLmRhdGVGaWxlc09ialtkYXRhW2ldXSA9IGRhdGFbaV07CiAgICAgICAgICB9CiAgICAgICAgICBjb25zb2xlLmxvZyhfdGhpczEwLmRhdGVGaWxlc09iaik7CiAgICAgICAgfQogICAgICAgIGlmIChjYWxsYmFjaykgY2FsbGJhY2soKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5sb2coZXJyb3IpOwogICAgICB9KTsKICAgIH0sCiAgICBnb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy9jbG91ZFJlY29yZCcpOwogICAgfQogIH0KfTs="}, null]}