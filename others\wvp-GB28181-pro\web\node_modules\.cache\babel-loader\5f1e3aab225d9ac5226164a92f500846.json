{"remainingRequest": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\src\\views\\cloudRecord\\detail.vue", "mtime": 1750431834581}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\babel.config.js", "mtime": 1749892839318}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1749893289834}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749893289789}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749893289776}, {"path": "C:\\Driver-E\\工作\\代码文件\\others\\wvp-GB28181-pro\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749893289053}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}