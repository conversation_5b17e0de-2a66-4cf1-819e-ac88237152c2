0 verbose cli C:\Driver-D\tools\nodejs\node.exe C:\Driver-D\tools\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.15.0
3 silly config load:file:C:\Driver-D\tools\nodejs\node_modules\npm\npmrc
4 silly config load:file:C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Driver-E\工作\代码文件\nodejs\node_global\etc\npmrc
7 verbose title npm install @types/react@ts5.8
8 verbose argv "install" "--ignore-scripts" "@types/react@ts5.8" "--save-dev" "--user-agent" "typesInstaller/5.8.3"
9 verbose logfile logs-max:10 dir:C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_57_15_423Z-
10 verbose logfile C:\Driver-E\工作\代码文件\nodejs\node_cache\_logs\2025-06-20T12_57_15_423Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly logfile done cleaning log files
14 silly packumentCache corgi:https://registry.npmmirror.com/@types%2freact cache-miss
15 http fetch GET 200 https://registry.npmmirror.com/@types%2freact 81ms (cache miss)
16 silly packumentCache corgi:https://registry.npmmirror.com/@types%2freact set size:166134 disposed:false
17 silly idealTree buildDeps
18 silly fetch manifest @types/react@19.1.8
19 silly packumentCache full:https://registry.npmmirror.com/@types%2freact cache-miss
20 http fetch GET 200 https://registry.npmmirror.com/@types%2freact 46ms (cache miss)
21 silly packumentCache full:https://registry.npmmirror.com/@types%2freact set size:308112 disposed:false
22 silly placeDep ROOT @types/react@19.1.8 REPLACE for:  want: 19.1.8
23 silly reify moves {}
24 silly audit bulk request {
24 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
24 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
24 silly audit   '@babel/parser': [ '7.27.5' ],
24 silly audit   '@babel/types': [ '7.27.6' ],
24 silly audit   '@parcel/watcher': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-android-arm64': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-darwin-arm64': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-darwin-x64': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-freebsd-x64': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-arm-glibc': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-arm-musl': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-arm64-glibc': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-arm64-musl': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-x64-glibc': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-linux-x64-musl': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-win32-arm64': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-win32-ia32': [ '2.5.1' ],
24 silly audit   '@parcel/watcher-win32-x64': [ '2.5.1' ],
24 silly audit   '@types/babel__generator': [ '7.27.0' ],
24 silly audit   '@types/babel__template': [ '7.4.4' ],
24 silly audit   '@types/babel__traverse': [ '7.20.7' ],
24 silly audit   '@types/body-parser': [ '1.19.5' ],
24 silly audit   '@types/caseless': [ '0.12.5' ],
24 silly audit   '@types/clean-css': [ '4.2.11' ],
24 silly audit   '@types/color-convert': [ '2.0.4' ],
24 silly audit   '@types/color-name': [ '1.1.5' ],
24 silly audit   '@types/connect': [ '3.4.38' ],
24 silly audit   '@types/core-js': [ '2.5.8' ],
24 silly audit   '@types/html-minifier': [ '4.0.5' ],
24 silly audit   '@types/html-webpack-plugin': [ '3.2.9' ],
24 silly audit   '@types/http-errors': [ '2.0.4' ],
24 silly audit   '@types/js-cookie': [ '3.0.6' ],
24 silly audit   '@types/mime': [ '1.3.5' ],
24 silly audit   '@types/mockjs': [ '1.0.10' ],
24 silly audit   '@types/node': [ '22.15.30' ],
24 silly audit   '@types/node-sass': [ '4.11.8' ],
24 silly audit   '@types/nprogress': [ '0.2.3' ],
24 silly audit   '@types/platform': [ '1.3.6' ],
24 silly audit   '@types/relateurl': [ '0.2.33' ],
24 silly audit   '@types/request': [ '2.48.12' ],
24 silly audit   '@types/sass-loader': [ '8.0.9' ],
24 silly audit   '@types/script-ext-html-webpack-plugin': [ '2.1.6' ],
24 silly audit   '@types/send': [ '0.17.4' ],
24 silly audit   '@types/serve-static': [ '1.15.8' ],
24 silly audit   '@types/source-list-map': [ '0.1.6' ],
24 silly audit   '@types/svg-sprite-loader': [ '3.9.9' ],
24 silly audit   '@types/tapable': [ '1.0.12' ],
24 silly audit   '@types/tough-cookie': [ '4.0.5' ],
24 silly audit   '@types/uglify-js': [ '3.17.5' ],
24 silly audit   '@types/uuid': [ '10.0.0' ],
24 silly audit   '@types/webpack': [ '4.41.40' ],
24 silly audit   '@types/webpack-sources': [ '3.2.3' ],
24 silly audit   'source-map': [ '0.7.4', '0.6.1' ],
24 silly audit   anymatch: [ '3.1.3' ],
24 silly audit   asynckit: [ '0.4.0' ],
24 silly audit   braces: [ '3.0.3' ],
24 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
24 silly audit   chokidar: [ '4.0.3' ],
24 silly audit   'combined-stream': [ '1.0.8' ],
24 silly audit   csstype: [ '3.1.3' ],
24 silly audit   'delayed-stream': [ '1.0.0' ],
24 silly audit   'detect-libc': [ '1.0.3' ],
24 silly audit   'dunder-proto': [ '1.0.1' ],
24 silly audit   'es-define-property': [ '1.0.1' ],
24 silly audit   'es-errors': [ '1.3.0' ],
24 silly audit   'es-object-atoms': [ '1.1.1' ],
24 silly audit   'es-set-tostringtag': [ '2.1.0' ],
24 silly audit   'fill-range': [ '7.1.1' ],
24 silly audit   'form-data': [ '2.5.3' ],
24 silly audit   'function-bind': [ '1.1.2' ],
24 silly audit   'get-intrinsic': [ '1.3.0' ],
24 silly audit   'get-proto': [ '1.0.1' ],
24 silly audit   gopd: [ '1.2.0' ],
24 silly audit   'has-symbols': [ '1.1.0' ],
24 silly audit   'has-tostringtag': [ '1.0.2' ],
24 silly audit   hasown: [ '2.0.2' ],
24 silly audit   immutable: [ '5.1.2' ],
24 silly audit   'is-extglob': [ '2.1.1' ],
24 silly audit   'is-glob': [ '4.0.3' ],
24 silly audit   'is-number': [ '7.0.0' ],
24 silly audit   'math-intrinsics': [ '1.1.0' ],
24 silly audit   micromatch: [ '4.0.8' ],
24 silly audit   'mime-db': [ '1.52.0' ],
24 silly audit   'mime-types': [ '2.1.35' ],
24 silly audit   'node-addon-api': [ '7.1.1' ],
24 silly audit   'normalize-path': [ '3.0.0' ],
24 silly audit   picomatch: [ '2.3.1' ],
24 silly audit   readdirp: [ '4.1.2' ],
24 silly audit   'safe-buffer': [ '5.2.1' ],
24 silly audit   sass: [ '1.88.0' ],
24 silly audit   'source-map-js': [ '1.2.1' ],
24 silly audit   'to-regex-range': [ '5.0.1' ],
24 silly audit   'types-registry': [ '0.1.724' ],
24 silly audit   'undici-types': [ '6.21.0' ],
24 silly audit   '@types/react': [ '19.1.8' ]
24 silly audit }
25 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-win32-ia32
26 silly reify mark deleted [
26 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-win32-ia32'
26 silly reify ]
27 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-win32-arm64
28 silly reify mark deleted [
28 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-win32-arm64'
28 silly reify ]
29 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-x64-musl
30 silly reify mark deleted [
30 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-x64-musl'
30 silly reify ]
31 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-x64-glibc
32 silly reify mark deleted [
32 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-x64-glibc'
32 silly reify ]
33 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm64-musl
34 silly reify mark deleted [
34 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm64-musl'
34 silly reify ]
35 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm64-glibc
36 silly reify mark deleted [
36 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm64-glibc'
36 silly reify ]
37 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm-musl
38 silly reify mark deleted [
38 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm-musl'
38 silly reify ]
39 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-linux-arm-glibc
40 silly reify mark deleted [
40 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-linux-arm-glibc'
40 silly reify ]
41 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-freebsd-x64
42 silly reify mark deleted [
42 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-freebsd-x64'
42 silly reify ]
43 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-darwin-x64
44 silly reify mark deleted [
44 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-darwin-x64'
44 silly reify ]
45 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-darwin-arm64
46 silly reify mark deleted [
46 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-darwin-arm64'
46 silly reify ]
47 verbose reify failed optional dependency C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8\node_modules\@parcel\watcher-android-arm64
48 silly reify mark deleted [
48 silly reify   'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\TypeScript\\5.8\\node_modules\\@parcel\\watcher-android-arm64'
48 silly reify ]
49 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 17ms
50 silly audit bulk request failed [object Object]
51 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 6ms
52 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
52 verbose audit error     at C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\npm-registry-fetch\lib\check-response.js:103:15
52 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
52 verbose audit error     at async [getReport] (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:336:21)
52 verbose audit error     at async AuditReport.run (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:106:19)
52 verbose audit error     at async Arborist.reify (C:\Driver-D\tools\nodejs\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\reify.js:268:24)
52 verbose audit error     at async Install.exec (C:\Driver-D\tools\nodejs\node_modules\npm\lib\commands\install.js:150:5)
52 verbose audit error     at async Npm.exec (C:\Driver-D\tools\nodejs\node_modules\npm\lib\npm.js:207:9)
52 verbose audit error     at async module.exports (C:\Driver-D\tools\nodejs\node_modules\npm\lib\cli\entry.js:74:5) {
52 verbose audit error   headers: [Object: null prototype] {
52 verbose audit error     server: [ 'Tengine' ],
52 verbose audit error     date: [ 'Fri, 20 Jun 2025 12:57:15 GMT' ],
52 verbose audit error     'content-type': [ 'application/json' ],
52 verbose audit error     'transfer-encoding': [ 'chunked' ],
52 verbose audit error     connection: [ 'keep-alive' ],
52 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
52 verbose audit error     via: [ 'kunlun3.cn8434[,404666]' ],
52 verbose audit error     'timing-allow-origin': [ '*' ],
52 verbose audit error     eagleid: [ '76b7f15b17504242359676984e' ],
52 verbose audit error     'x-fetch-attempts': [ '1' ]
52 verbose audit error   },
52 verbose audit error   statusCode: 404,
52 verbose audit error   code: 'E404',
52 verbose audit error   method: 'POST',
52 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
52 verbose audit error   body: {
52 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
52 verbose audit error   },
52 verbose audit error   pkgid: undefined
52 verbose audit error }
53 silly audit error [object Object]
54 silly audit report null
55 silly ADD
56 silly ADD
57 silly ADD
58 silly ADD
59 silly ADD
60 silly ADD
61 silly ADD
62 silly ADD
63 silly ADD
64 silly ADD
65 silly ADD
66 silly ADD
67 verbose cwd C:\Users\<USER>\AppData\Local\Microsoft\TypeScript\5.8
68 verbose os Windows_NT 10.0.26100
69 verbose node v22.15.0
70 verbose npm  v10.9.2
71 verbose exit 0
72 info ok
