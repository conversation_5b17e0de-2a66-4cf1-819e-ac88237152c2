(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46f68df1"],{"278c":function(n,e,t){var o=t("c135"),r=t("9b42"),i=t("6613"),l=t("c240");function a(n,e){return o(n)||r(n,e)||i(n,e)||l()}n.exports=a,n.exports.__esModule=!0,n.exports["default"]=n.exports},3156:function(n,e,t){t("a4d3"),t("4de4"),t("e439"),t("b64b"),t("d3b7"),t("0643"),t("2382"),t("4e3e"),t("159b");var o=t("9523");function r(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?Object(arguments[e]):{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&r.push.apply(r,Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach((function(e){o(n,e,t[e])}))}return n}n.exports=r,n.exports.__esModule=!0,n.exports["default"]=n.exports},7037:function(n,e,t){function o(e){return n.exports=o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},n.exports.__esModule=!0,n.exports["default"]=n.exports,o(e)}t("a4d3"),t("e01a"),t("d28b"),t("d3b7"),t("3ca3"),t("ddb0"),n.exports=o,n.exports.__esModule=!0,n.exports["default"]=n.exports},7671:function(n,e,t){"use strict";function o({onlyFirst:n=!1}={}){const e="(?:\\u0007|\\u001B\\u005C|\\u009C)",t=[`[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?${e})`,"(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(t,n?void 0:"g")}t.d(e,"a",(function(){return i}));const r=o();function i(n){if("string"!==typeof n)throw new TypeError(`Expected a \`string\`, got \`${typeof n}\``);return n.replace(r,"")}},"89c1":function(n,e,t){(function(n){var o,r,i;function l(n){return l="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"===typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},l(n)}(function(a,c){var s="VirtualList";"object"===l(e)&&"object"===l(n)?n.exports=c(s,t("2b0e")):(r=[t("2b0e")],o=c.bind(a,s),i="function"===typeof o?o.apply(e,r):o,void 0===i||(n.exports=i))})(this,(function(n,e){"object"===l(e)&&"function"===typeof e["default"]&&(e=e["default"]);var t=function(n,e,t){var o;return function(){var r=this,i=arguments,l=function(){o=null,t||n.apply(r,i)},a=t&&!o;clearTimeout(o),o=setTimeout(l,e),a&&n.apply(r,i)}};return e.component(n,{props:{size:{type:Number,required:!0},remain:{type:Number,required:!0},rtag:{type:String,default:"div"},wtag:{type:String,default:"div"},wclass:{type:String,default:""},wstyle:{type:Object,default:function(){return{}}},pagemode:{type:Boolean,default:!1},scrollelement:{type:"undefined"===typeof window?Object:HTMLElement,default:null},start:{type:Number,default:0},offset:{type:Number,default:0},variable:{type:[Function,Boolean],default:!1},bench:{type:Number,default:0},debounce:{type:Number,default:0},totop:{type:[Function,Boolean],default:!1},tobottom:{type:[Function,Boolean],default:!1},onscroll:{type:[Function,Boolean],default:!1},istable:{type:Boolean,default:!1},item:{type:[Function,Object],default:null},itemcount:{type:Number,default:0},itemprops:{type:Function,default:function(){}}},watch:{size:function(){this.changeProp="size"},remain:function(){this.changeProp="remain"},bench:function(){this.changeProp="bench",this.itemModeForceRender()},start:function(){this.changeProp="start",this.itemModeForceRender()},offset:function(){this.changeProp="offset",this.itemModeForceRender()},itemcount:function(){this.changeProp="itemcount",this.itemModeForceRender()},scrollelement:function(n,e){this.pagemode||(e&&this.removeScrollListener(e),n&&this.addScrollListener(n))}},created:function(){var n=this.start>=this.remain?this.start:0,e=this.remain+(this.bench||this.remain),t=Object.create(null);t.direction="",t.scrollTop=0,t.start=n,t.end=n+e-1,t.keeps=e,t.total=0,t.offsetAll=0,t.paddingTop=0,t.paddingBottom=0,t.varCache={},t.varAverSize=0,t.varLastCalcIndex=0,this.delta=t},mounted:function(){if(this.pagemode?this.addScrollListener(window):this.scrollelement&&this.addScrollListener(this.scrollelement),this.start){var n=this.getZone(this.start).start;this.setScrollTop(this.variable?this.getVarOffset(n):n*this.size)}else this.offset&&this.setScrollTop(this.offset)},beforeDestroy:function(){this.pagemode?this.removeScrollListener(window):this.scrollelement&&this.removeScrollListener(this.scrollelement)},beforeUpdate:function(){var n=this.delta;n.keeps=this.remain+(this.bench||this.remain);var e="start"===this.changeProp?this.start:n.start,t=this.getZone(e);if(this.changeProp&&["start","size","offset"].includes(this.changeProp)){var o="offset"===this.changeProp?this.offset:this.variable?this.getVarOffset(t.isLast?n.total:t.start):t.isLast&&n.total-e<=this.remain?n.total*this.size:e*this.size;this.$nextTick(this.setScrollTop.bind(this,o))}(this.changeProp||n.end!==t.end||e!==t.start)&&(this.changeProp="",n.end=t.end,n.start=t.start,this.forceRender())},methods:{addScrollListener:function(n){this.scrollHandler=this.debounce?t(this.onScroll.bind(this),this.debounce):this.onScroll,n.addEventListener("scroll",this.scrollHandler,!1)},removeScrollListener:function(n){n.removeEventListener("scroll",this.scrollHandler,!1)},onScroll:function(n){var e,t=this.delta,o=this.$refs.vsl;if(this.pagemode){var r=this.$el.getBoundingClientRect();e=-r.top}else if(this.scrollelement){var i=this.scrollelement.getBoundingClientRect(),l=this.$el.getBoundingClientRect();e=i.top-l.top}else e=o&&(o.$el||o).scrollTop||0;t.direction=e>t.scrollTop?"D":"U",t.scrollTop=e,t.total>t.keeps?this.updateZone(e):t.end=t.total-1;var a=t.offsetAll;if(this.onscroll){var c=Object.create(null);c.offset=e,c.offsetAll=a,c.start=t.start,c.end=t.end,this.onscroll(n,c)}!e&&t.total&&this.fireEvent("totop"),e>=a&&this.fireEvent("tobottom")},updateZone:function(n){var e=this.delta,t=this.variable?this.getVarOvers(n):Math.floor(n/this.size);"U"===e.direction&&(t=t-this.remain+1);var o=this.getZone(t),r=this.bench||this.remain,i=1===Math.abs(t-e.start-r);!i&&t-e.start<=r&&!o.isLast&&t>e.start||(i||o.start!==e.start||o.end!==e.end)&&(e.end=o.end,e.start=o.start,this.forceRender())},getZone:function(n){var e,t,o=this.delta;n=parseInt(n,10),n=Math.max(0,n);var r=o.total-o.keeps,i=n<=o.total&&n>=r||n>o.total;return e=i?Math.max(0,r):n,t=e+o.keeps-1,o.total&&t>o.total&&(t=o.total-1),{end:t,start:e,isLast:i}},forceRender:function(){var n=this;window.requestAnimationFrame((function(){n.$forceUpdate()}))},itemModeForceRender:function(){this.item&&this.forceRender()},getVarOvers:function(n){var e=0,t=0,o=0,r=this.delta,i=r.total;while(e<=i){if(t=e+Math.floor((i-e)/2),o=this.getVarOffset(t),r.varAverSize||(r.varAverSize=Math.floor(o/t)),o===n)return t;o<n?e=t+1:o>n&&(i=t-1)}return e>0?--e:0},getVarOffset:function(n,e){var t=this.delta,o=t.varCache[n];if(!e&&o)return o.offset;for(var r=0,i=0;i<n;i++){var l=this.getVarSize(i,e);t.varCache[i]={size:l,offset:r},r+=l}return t.varLastCalcIndex=Math.max(t.varLastCalcIndex,n-1),t.varLastCalcIndex=Math.min(t.varLastCalcIndex,t.total-1),r},getVarSize:function(n,e){var t=this.delta.varCache[n];if(!e&&t)return t.size;if("function"===typeof this.variable)return this.variable(n)||0;var o=this.item?this.$children[n]?this.$children[n].$vnode:null:this.$slots["default"][n],r=o&&o.data&&o.data.style;if(r&&r.height){var i=r.height.match(/^(.*)px$/);return i&&+i[1]||0}return 0},getVarPaddingTop:function(){return this.getVarOffset(this.delta.start)},getVarPaddingBottom:function(){var n=this.delta,e=n.total-1;return n.total-n.end<=n.keeps||n.varLastCalcIndex===e?this.getVarOffset(e)-this.getVarOffset(n.end):(n.total-n.end)*(n.varAverSize||this.size)},getVarAllHeight:function(){var n=this.delta;return n.total-n.end<=n.keeps||n.varLastCalcIndex===n.total-1?this.getVarOffset(n.total):this.getVarOffset(n.start)+(n.total-n.end)*(n.varAverSize||this.size)},updateVariable:function(n){this.getVarOffset(n,!0)},fireEvent:function(n){this[n]&&this[n]()},setScrollTop:function(n){if(this.pagemode)window.scrollTo(0,n);else if(this.scrollelement)this.scrollelement.scrollTo(0,n);else{var e=this.$refs.vsl;e&&((e.$el||e).scrollTop=n)}},filter:function(n){var e,t,o,r=this.delta,i=this.$slots["default"]||[];this.item||this.$scopedSlots.item?(r.total=this.itemcount,r.keeps>r.total&&(r.end=r.total-1)):(i.length||(r.start=0),r.total=i.length);var l=r.total>r.keeps;this.variable?(o=this.getVarAllHeight(),e=l?this.getVarPaddingTop():0,t=l?this.getVarPaddingBottom():0):(o=this.size*r.total,e=this.size*(l?r.start:0),t=this.size*(l?r.total-r.keeps:0)-e),t<this.size&&(t=0),r.paddingTop=e,r.paddingBottom=t,r.offsetAll=o-this.size*this.remain;for(var a=[],c=r.start;c<r.total&&c<=Math.ceil(r.end);c++){var s=null;s=this.$scopedSlots.item?this.$scopedSlots.item(c):this.item?n(this.item,this.itemprops(c)):i[c],a.push(s)}return a}},render:function(n){var e=this.debounce,o=this.filter(n),r=this.delta,i=r.paddingTop,l=r.paddingBottom,a=this.istable,c=a?"div":this.wtag,s=a?"div":this.rtag;a&&(o=[n("table",[n("tbody",o)])]);var A=n(c,{style:Object.assign({display:"block","padding-top":i+"px","padding-bottom":l+"px"},this.wstyle),class:this.wclass,attrs:{role:"group"}},o);return this.pagemode||this.scrollelement?A:n(s,{ref:"vsl",style:{display:"block","overflow-y":this.size>=this.remain?"auto":"initial",height:this.size*this.remain+"px"},on:{"&scroll":e?t(this.onScroll.bind(this),e):this.onScroll}},[A])}})}))}).call(this,t("62e4")(n))},9523:function(n,e,t){var o=t("a395");function r(n,e,t){return(e=o(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}n.exports=r,n.exports.__esModule=!0,n.exports["default"]=n.exports},"9b42":function(n,e,t){function o(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var o,r,i,l,a=[],c=!0,s=!1;try{if(i=(t=t.call(n)).next,0===e){if(Object(t)!==t)return;c=!1}else for(;!(c=(o=i.call(t)).done)&&(a.push(o.value),a.length!==e);c=!0);}catch(n){s=!0,r=n}finally{try{if(!c&&null!=t["return"]&&(l=t["return"](),Object(l)!==l))return}finally{if(s)throw r}}return a}}t("a4d3"),t("e01a"),t("d28b"),t("d3b7"),t("3ca3"),t("ddb0"),n.exports=o,n.exports.__esModule=!0,n.exports["default"]=n.exports},a395:function(n,e,t){var o=t("7037")["default"],r=t("e50d");function i(n){var e=r(n,"string");return"symbol"==o(e)?e:e+""}n.exports=i,n.exports.__esModule=!0,n.exports["default"]=n.exports},c135:function(n,e){function t(n){if(Array.isArray(n))return n}n.exports=t,n.exports.__esModule=!0,n.exports["default"]=n.exports},c240:function(n,e){function t(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n.exports=t,n.exports.__esModule=!0,n.exports["default"]=n.exports},e50d:function(n,e,t){t("8172"),t("efec"),t("a9e3");var o=t("7037")["default"];function r(n,e){if("object"!=o(n)||!n)return n;var t=n[Symbol.toPrimitive];if(void 0!==t){var r=t.call(n,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(n)}n.exports=r,n.exports.__esModule=!0,n.exports["default"]=n.exports},e897:function(n,e,t){"use strict";(function(n){var o=t("89c1"),r=t.n(o),i=t("278c"),l=t.n(i),a=t("3156"),c=t.n(a),s={name:"LineContent",props:{content:Array}};function A(n,e,t,o,r,i,l,a,c,s){"boolean"!==typeof l&&(c=a,a=l,l=!1);var A,d="function"===typeof t?t.options:t;if(n&&n.render&&(d.render=n.render,d.staticRenderFns=n.staticRenderFns,d._compiled=!0,r&&(d.functional=!0)),o&&(d._scopeId=o),i?(A=function(n){n=n||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,n||"undefined"===typeof __VUE_SSR_CONTEXT__||(n=__VUE_SSR_CONTEXT__),e&&e.call(this,c(n)),n&&n._registeredComponents&&n._registeredComponents.add(i)},d._ssrRegister=A):e&&(A=l?function(){e.call(this,s(this.$root.$options.shadowRoot))}:function(n){e.call(this,a(n))}),A)if(d.functional){var g=d.render;d.render=function(n,e){return A.call(e),g(n,e)}}else{var u=d.beforeCreate;d.beforeCreate=u?[].concat(u,A):[A]}return t}var d=A,g="undefined"!==typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function u(n){return function(n,e){return b(n,e)}}var f=document.head||document.getElementsByTagName("head")[0],h={};function b(n,e){var t=g?e.media||"default":n,o=h[t]||(h[t]={ids:new Set,styles:[]});if(!o.ids.has(n)){o.ids.add(n);var r=e.source;if(e.map&&(r+="\n/*# sourceURL="+e.map.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e.map))))+" */"),o.element||(o.element=document.createElement("style"),o.element.type="text/css",e.media&&o.element.setAttribute("media",e.media),f.appendChild(o.element)),"styleSheet"in o.element)o.styles.push(r),o.element.styleSheet.cssText=o.styles.filter(Boolean).join("\n");else{var i=o.ids.size-1,l=document.createTextNode(r),a=o.element.childNodes;a[i]&&o.element.removeChild(a[i]),a.length?o.element.insertBefore(l,a[i]):o.element.appendChild(l)}}}var p=u;const m=s;var C=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"line-content"},n._l(n.content,(function(e,o){return t("span",{key:o,class:[{"log-bold":e.bold,"log-underline":e.underline,"log-italic":e.italic},e.foreground?"log-fore-"+e.foreground:"",e.background?"log-back-"+e.background:""]},[n._v(n._s(e.text))])})),0)},v=[];C._withStripped=!0;const y=function(n){n&&n("data-v-0acb7131_0",{source:".line-content .log-bold {\n  font-weight: bold;\n}\n.line-content .log-underline {\n  text-decoration: underline;\n}\n.line-content .log-italic {\n  font-style: italic;\n}\n.line-content .log-fore-black {\n  color: #000000;\n}\n.line-content .log-fore-black.log-underline {\n  -webkit-text-decoration-color: #000000;\n          text-decoration-color: #000000;\n}\n.line-content .log-fore-bright-black {\n  color: #818383;\n}\n.line-content .log-fore-bright-black.log-underline {\n  -webkit-text-decoration-color: #818383;\n          text-decoration-color: #818383;\n}\n.line-content .log-back-black {\n  background: #000000;\n}\n.line-content .log-back-bright-black {\n  background: #818383;\n}\n.line-content .log-fore-red {\n  color: #c23621;\n}\n.line-content .log-fore-red.log-underline {\n  -webkit-text-decoration-color: #c23621;\n          text-decoration-color: #c23621;\n}\n.line-content .log-fore-bright-red {\n  color: #fc391f;\n}\n.line-content .log-fore-bright-red.log-underline {\n  -webkit-text-decoration-color: #fc391f;\n          text-decoration-color: #fc391f;\n}\n.line-content .log-back-red {\n  background: #c23621;\n}\n.line-content .log-back-bright-red {\n  background: #fc391f;\n}\n.line-content .log-fore-green {\n  color: #25bc24;\n}\n.line-content .log-fore-green.log-underline {\n  -webkit-text-decoration-color: #25bc24;\n          text-decoration-color: #25bc24;\n}\n.line-content .log-fore-bright-green {\n  color: #31e722;\n}\n.line-content .log-fore-bright-green.log-underline {\n  -webkit-text-decoration-color: #31e722;\n          text-decoration-color: #31e722;\n}\n.line-content .log-back-green {\n  background: #25bc24;\n}\n.line-content .log-back-bright-green {\n  background: #31e722;\n}\n.line-content .log-fore-yellow {\n  color: #adad27;\n}\n.line-content .log-fore-yellow.log-underline {\n  -webkit-text-decoration-color: #adad27;\n          text-decoration-color: #adad27;\n}\n.line-content .log-fore-bright-yellow {\n  color: #eaec23;\n}\n.line-content .log-fore-bright-yellow.log-underline {\n  -webkit-text-decoration-color: #eaec23;\n          text-decoration-color: #eaec23;\n}\n.line-content .log-back-yellow {\n  background: #adad27;\n}\n.line-content .log-back-bright-yellow {\n  background: #eaec23;\n}\n.line-content .log-fore-blue {\n  color: #492ee1;\n}\n.line-content .log-fore-blue.log-underline {\n  -webkit-text-decoration-color: #492ee1;\n          text-decoration-color: #492ee1;\n}\n.line-content .log-fore-bright-blue {\n  color: #5833ff;\n}\n.line-content .log-fore-bright-blue.log-underline {\n  -webkit-text-decoration-color: #5833ff;\n          text-decoration-color: #5833ff;\n}\n.line-content .log-back-blue {\n  background: #492ee1;\n}\n.line-content .log-back-bright-blue {\n  background: #5833ff;\n}\n.line-content .log-fore-magenta {\n  color: #d338d3;\n}\n.line-content .log-fore-magenta.log-underline {\n  -webkit-text-decoration-color: #d338d3;\n          text-decoration-color: #d338d3;\n}\n.line-content .log-fore-bright-magenta {\n  color: #b4009e;\n}\n.line-content .log-fore-bright-magenta.log-underline {\n  -webkit-text-decoration-color: #b4009e;\n          text-decoration-color: #b4009e;\n}\n.line-content .log-back-magenta {\n  background: #d338d3;\n}\n.line-content .log-back-bright-magenta {\n  background: #b4009e;\n}\n.line-content .log-fore-cyan {\n  color: #33bbc8;\n}\n.line-content .log-fore-cyan.log-underline {\n  -webkit-text-decoration-color: #33bbc8;\n          text-decoration-color: #33bbc8;\n}\n.line-content .log-fore-bright-cyan {\n  color: #61d6d6;\n}\n.line-content .log-fore-bright-cyan.log-underline {\n  -webkit-text-decoration-color: #61d6d6;\n          text-decoration-color: #61d6d6;\n}\n.line-content .log-back-cyan {\n  background: #33bbc8;\n}\n.line-content .log-back-bright-cyan {\n  background: #61d6d6;\n}\n.line-content .log-fore-white {\n  color: #cbcccd;\n}\n.line-content .log-fore-white.log-underline {\n  -webkit-text-decoration-color: #cbcccd;\n          text-decoration-color: #cbcccd;\n}\n.line-content .log-fore-bright-white {\n  color: #f2f2f2;\n}\n.line-content .log-fore-bright-white.log-underline {\n  -webkit-text-decoration-color: #f2f2f2;\n          text-decoration-color: #f2f2f2;\n}\n.line-content .log-back-white {\n  background: #cbcccd;\n}\n.line-content .log-back-bright-white {\n  background: #f2f2f2;\n}\n",map:{version:3,sources:["line-content.vue","/home/<USER>/build/FEMessage/log-viewer/src/components/line-content.vue"],names:[],mappings:"AAAA;EACE,iBAAiB;AACnB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,kBAAkB;AACpB;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,mBAAmB;AACrB;AACA;EACE,mBAAmB;AACrB;AACA;EACE,cAAc;AAChB;AACA;ECCA,sCAAA;UAAA,8BAAA;AACA;AACA;EACA,cAAA;AACA;AACA;EACA,sCAAA;UAAA,8BAAA;AACA;AACA;EACA,mBAAA;AACA;AACA;EACA,mBAAA;AACA;AACA;EACA,cAAA;AACA;AACA;EDCE,sCAA8B;UAA9B,8BAA8B;ACChC;AACA;EDCE,cAAc;ACChB;AACA;EACA,sCAAA;UAAA,8BAAA;AACA;ADCA;ECCA,mBAAA;AACA;ADCA;ECCA,mBAAA;AACA;AACA;EACA,cAAA;ADCA;ACCA;EACA,sCAAA;UAAA,8BAAA;AACA;ADCA;ECCA,cAAA;AACA;AACA;EACA,sCAAA;UAAA,8BAAA;ADCA;ACCA;EACA,mBAAA;AACA;AACA;EDCE,mBAAmB;ACCrB;AACA;EACA,cAAA;ADCA;ACCA;EACA,sCAAA;UAAA,8BAAA;AACA;AACA;EACA,cAAA;AACA;AACA;EACA,sCAAA;UAAA,8BAAA;AACA;AACA;EACA,mBAAA;AACA;ADCA;EACE,mBAAmB;AACrB;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,mBAAmB;AACrB;AACA;EACE,mBAAmB;AACrB;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,mBAAmB;AACrB;AACA;EACE,mBAAmB;AACrB;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,cAAc;AAChB;AACA;EACE,sCAA8B;UAA9B,8BAA8B;AAChC;AACA;EACE,mBAAmB;AACrB;AACA;EACE,mBAAmB;AACrB",file:"line-content.vue",sourcesContent:[".line-content .log-bold {\n  font-weight: bold;\n}\n.line-content .log-underline {\n  text-decoration: underline;\n}\n.line-content .log-italic {\n  font-style: italic;\n}\n.line-content .log-fore-black {\n  color: #000000;\n}\n.line-content .log-fore-black.log-underline {\n  text-decoration-color: #000000;\n}\n.line-content .log-fore-bright-black {\n  color: #818383;\n}\n.line-content .log-fore-bright-black.log-underline {\n  text-decoration-color: #818383;\n}\n.line-content .log-back-black {\n  background: #000000;\n}\n.line-content .log-back-bright-black {\n  background: #818383;\n}\n.line-content .log-fore-red {\n  color: #c23621;\n}\n.line-content .log-fore-red.log-underline {\n  text-decoration-color: #c23621;\n}\n.line-content .log-fore-bright-red {\n  color: #fc391f;\n}\n.line-content .log-fore-bright-red.log-underline {\n  text-decoration-color: #fc391f;\n}\n.line-content .log-back-red {\n  background: #c23621;\n}\n.line-content .log-back-bright-red {\n  background: #fc391f;\n}\n.line-content .log-fore-green {\n  color: #25bc24;\n}\n.line-content .log-fore-green.log-underline {\n  text-decoration-color: #25bc24;\n}\n.line-content .log-fore-bright-green {\n  color: #31e722;\n}\n.line-content .log-fore-bright-green.log-underline {\n  text-decoration-color: #31e722;\n}\n.line-content .log-back-green {\n  background: #25bc24;\n}\n.line-content .log-back-bright-green {\n  background: #31e722;\n}\n.line-content .log-fore-yellow {\n  color: #adad27;\n}\n.line-content .log-fore-yellow.log-underline {\n  text-decoration-color: #adad27;\n}\n.line-content .log-fore-bright-yellow {\n  color: #eaec23;\n}\n.line-content .log-fore-bright-yellow.log-underline {\n  text-decoration-color: #eaec23;\n}\n.line-content .log-back-yellow {\n  background: #adad27;\n}\n.line-content .log-back-bright-yellow {\n  background: #eaec23;\n}\n.line-content .log-fore-blue {\n  color: #492ee1;\n}\n.line-content .log-fore-blue.log-underline {\n  text-decoration-color: #492ee1;\n}\n.line-content .log-fore-bright-blue {\n  color: #5833ff;\n}\n.line-content .log-fore-bright-blue.log-underline {\n  text-decoration-color: #5833ff;\n}\n.line-content .log-back-blue {\n  background: #492ee1;\n}\n.line-content .log-back-bright-blue {\n  background: #5833ff;\n}\n.line-content .log-fore-magenta {\n  color: #d338d3;\n}\n.line-content .log-fore-magenta.log-underline {\n  text-decoration-color: #d338d3;\n}\n.line-content .log-fore-bright-magenta {\n  color: #b4009e;\n}\n.line-content .log-fore-bright-magenta.log-underline {\n  text-decoration-color: #b4009e;\n}\n.line-content .log-back-magenta {\n  background: #d338d3;\n}\n.line-content .log-back-bright-magenta {\n  background: #b4009e;\n}\n.line-content .log-fore-cyan {\n  color: #33bbc8;\n}\n.line-content .log-fore-cyan.log-underline {\n  text-decoration-color: #33bbc8;\n}\n.line-content .log-fore-bright-cyan {\n  color: #61d6d6;\n}\n.line-content .log-fore-bright-cyan.log-underline {\n  text-decoration-color: #61d6d6;\n}\n.line-content .log-back-cyan {\n  background: #33bbc8;\n}\n.line-content .log-back-bright-cyan {\n  background: #61d6d6;\n}\n.line-content .log-fore-white {\n  color: #cbcccd;\n}\n.line-content .log-fore-white.log-underline {\n  text-decoration-color: #cbcccd;\n}\n.line-content .log-fore-bright-white {\n  color: #f2f2f2;\n}\n.line-content .log-fore-bright-white.log-underline {\n  text-decoration-color: #f2f2f2;\n}\n.line-content .log-back-white {\n  background: #cbcccd;\n}\n.line-content .log-back-bright-white {\n  background: #f2f2f2;\n}\n","<template>\n  <div class=\"line-content\">\n    <span\n      v-for=\"(item, index) in content\"\n      :key=\"index\"\n      :class=\"[\n        {\n          'log-bold': item.bold,\n          'log-underline': item.underline,\n          'log-italic': item.italic\n        },\n        item.foreground ? 'log-fore-' + item.foreground : '',\n        item.background ? 'log-back-' + item.background : ''\n      ]\"\n      >{{ item.text }}</span\n    >\n  </div>\n</template>\n<script>\nexport default {\n  name: 'LineContent',\n  props: {\n    /**\n     * log text content\n     */\n    content: Array\n  }\n}\n<\/script>\n\n<style lang=\"less\">\n@black: rgb(0, 0, 0);\n@red: rgb(194, 54, 33);\n@green: rgb(37, 188, 36);\n@yellow: rgb(173, 173, 39);\n@blue: rgb(73, 46, 225);\n@magenta: rgb(211, 56, 211);\n@cyan: rgb(51, 187, 200);\n@white: rgb(203, 204, 205);\n@bright-black: rgb(129, 131, 131);\n@bright-red: rgb(252, 57, 31);\n@bright-green: rgb(49, 231, 34);\n@bright-yellow: rgb(234, 236, 35);\n@bright-blue: rgb(88, 51, 255);\n@bright-magenta: rgb(180, 0, 158);\n@bright-cyan: rgb(97, 214, 214);\n@bright-white: rgb(242, 242, 242);\n.log(@color) {\n  @bright: 'bright-@{color}';\n\n  .log-fore-@{color} {\n    color: @@color;\n\n    &.log-underline {\n      text-decoration-color: @@color;\n    }\n  }\n\n  .log-fore-bright-@{color} {\n    color: @@bright;\n\n    &.log-underline {\n      text-decoration-color: @@bright;\n    }\n  }\n\n  .log-back-@{color} {\n    background: @@color;\n  }\n\n  .log-back-bright-@{color} {\n    background: @@bright;\n  }\n}\n\n.line-content {\n  .log-bold {\n    font-weight: bold;\n  }\n\n  .log-underline {\n    text-decoration: underline;\n  }\n\n  .log-italic {\n    font-style: italic;\n  }\n  .log(black);\n  .log(red);\n  .log(green);\n  .log(yellow);\n  .log(blue);\n  .log(magenta);\n  .log(cyan);\n  .log(white);\n}\n</style>\n"]},media:void 0})},B=void 0,x=void 0,k=!1;var E=d({render:C,staticRenderFns:v},y,m,B,k,x,p,void 0),w={name:"LineNumber",props:{number:Number,className:[String,Array,Object]}};const S=w;var _=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"line-number",class:n.className},[n._v(n._s(n.number))])},L=[];_._withStripped=!0;const O=void 0,z=void 0,$=void 0,T=!1;var M=d({render:_,staticRenderFns:L},O,S,z,T,$,void 0,void 0),j={name:"LineWrapper",components:{LineContent:E,LineNumber:M},props:{data:{type:Array,default:function(){return[{text:""}]}},height:{type:[Number,String],default:20},comStyle:{type:Object,default:function(){return{}}},hasNumber:Boolean,numberData:Object},computed:{customStyle:function(){var n="number"===typeof this.height?this.height+"px":this.height;return Object.assign({lineHeight:n,height:n},this.comStyle)}}};const R=j;var U=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"line-wrapper",style:n.customStyle},[n.hasNumber?t("line-number",n._b({},"line-number",n.numberData,!1)):n._e(),n._v(" "),n._t("default",[t("line-content",{attrs:{content:n.data}})])],2)},V=[];U._withStripped=!0;const N=function(n){n&&n("data-v-831b9dda_0",{source:".line-wrapper {\n  display: flex;\n  color: #f1f1f1;\n  line-height: 20px;\n  height: 20px;\n  white-space: pre;\n  box-sizing: border-box;\n  padding-left: 16px;\n}\n.line-wrapper:hover {\n  background-color: #444;\n}\n.line-wrapper .line-number {\n  min-width: 40px;\n  text-align: right;\n  color: #666;\n  padding-right: 10px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n",map:{version:3,sources:["line-wrapper.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,cAAc;EACd,iBAAiB;EACjB,YAAY;EACZ,gBAAgB;EAChB,sBAAsB;EACtB,kBAAkB;AACpB;AACA;EACE,sBAAsB;AACxB;AACA;EACE,eAAe;EACf,iBAAiB;EACjB,WAAW;EACX,mBAAmB;EACnB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB;AACnB",file:"line-wrapper.vue",sourcesContent:[".line-wrapper {\n  display: flex;\n  color: #f1f1f1;\n  line-height: 20px;\n  height: 20px;\n  white-space: pre;\n  box-sizing: border-box;\n  padding-left: 16px;\n}\n.line-wrapper:hover {\n  background-color: #444;\n}\n.line-wrapper .line-number {\n  min-width: 40px;\n  text-align: right;\n  color: #666;\n  padding-right: 10px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n"]},media:void 0})},F=void 0,P=void 0,D=!1;var I=d({render:U,staticRenderFns:V},N,R,F,D,P,p,void 0),H={name:"LogLoading"};const q=H;var Z=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("span",{staticClass:"log-loading"},[n._t("default",[n._m(0)])],2)},W=[function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("span",{staticClass:"loading-dots"},[t("span",{staticClass:"dot"},[n._v("•")]),n._v(" "),t("span",{staticClass:"dot"},[n._v("•")]),n._v(" "),t("span",{staticClass:"dot"},[n._v("•")])])}];Z._withStripped=!0;const Y=function(n){n&&n("data-v-67e231d4_0",{source:".log-loading .loading-dots .dot {\n  -webkit-animation: blink 0.6s infinite both;\n          animation: blink 0.6s infinite both;\n  margin-left: -1px;\n}\n.log-loading .loading-dots .dot:first-child {\n  margin-left: 0;\n}\n.log-loading .loading-dots .dot:nth-child(2) {\n  -webkit-animation-delay: 0.2s;\n          animation-delay: 0.2s;\n}\n.log-loading .loading-dots .dot:nth-child(3) {\n  -webkit-animation-delay: 0.4s;\n          animation-delay: 0.4s;\n}\n@-webkit-keyframes blink {\n0% {\n    opacity: 0.2;\n}\n20% {\n    opacity: 1;\n}\n100% {\n    opacity: 0.2;\n}\n}\n@keyframes blink {\n0% {\n    opacity: 0.2;\n}\n20% {\n    opacity: 1;\n}\n100% {\n    opacity: 0.2;\n}\n}\n",map:{version:3,sources:["loading.vue","/home/<USER>/build/FEMessage/log-viewer/src/components/loading.vue"],names:[],mappings:"AAAA;EACE,2CAAmC;UAAnC,mCAAmC;EACnC,iBAAiB;AACnB;AACA;EACE,cAAc;AAChB;AACA;EACE,6BAAqB;UAArB,qBAAqB;AACvB;AACA;EACE,6BAAqB;UAArB,qBAAqB;AACvB;AACA;AACE;IACE,YAAY;AACd;AACA;ICCF,UAAA;AACA;AACA;IACA,YAAA;AACA;ADCA;AAVA;AACE;IACE,YAAY;AACd;AACA;ICCF,UAAA;AACA;AACA;IACA,YAAA;AACA;ADCA",file:"loading.vue",sourcesContent:[".log-loading .loading-dots .dot {\n  animation: blink 0.6s infinite both;\n  margin-left: -1px;\n}\n.log-loading .loading-dots .dot:first-child {\n  margin-left: 0;\n}\n.log-loading .loading-dots .dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n.log-loading .loading-dots .dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n@keyframes blink {\n  0% {\n    opacity: 0.2;\n  }\n  20% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.2;\n  }\n}\n",'<template>\n  <span class="log-loading">\n    <slot>\n      <span class="loading-dots">\n        <span class="dot">•</span>\n        <span class="dot">•</span>\n        <span class="dot">•</span>\n      </span>\n    </slot>\n  </span>\n</template>\n<script>\nexport default {\n  name: \'LogLoading\'\n}\n<\/script>\n\n<style lang="less">\n.log-loading {\n  .loading-dots {\n    .dot {\n      animation: blink 0.6s infinite both;\n      margin-left: -1px;\n\n      &:first-child {\n        margin-left: 0;\n      }\n\n      &:nth-child(2) {\n        animation-delay: 0.2s;\n      }\n\n      &:nth-child(3) {\n        animation-delay: 0.4s;\n      }\n    }\n\n    @keyframes blink {\n      0% {\n        opacity: 0.2;\n      }\n\n      20% {\n        opacity: 1;\n      }\n\n      100% {\n        opacity: 0.2;\n      }\n    }\n  }\n}\n</style>\n']},media:void 0})},J=void 0,X=void 0,G=!1;var K=d({render:Z,staticRenderFns:W},Y,q,J,G,X,p,void 0),Q={30:"black",31:"red",32:"green",33:"yellow",34:"blue",35:"magenta",36:"cyan",37:"white",90:"bright-black",91:"bright-red",92:"bright-green",93:"bright-yellow",94:"bright-blue",95:"bright-magenta",96:"bright-cyan",97:"bright-white"},nn={40:"black",41:"red",42:"green",43:"yellow",44:"blue",45:"magenta",46:"cyan",47:"white",100:"bright-black",101:"bright-red",102:"bright-green",103:"bright-yellow",104:"bright-blue",105:"bright-magenta",106:"bright-cyan",107:"bright-white"},en={1:"bold",3:"italic",4:"underline"},tn="m",on=["A","B","C","D","E","F","G","H","J","K","S","T","f","s","u","h","l"],rn=function(n,e){if(n.length)return[n.substr(0,n.length-1),e];if(e.length){var t=e.length-1,o=e[t].text,r=1===o.length?e.slice(0,e.length-1):e.map((function(n,e){return t===e?c()({},n,{text:o.substr(0,o.length-1)}):n}));return[n,r]}return[n,e]},ln=function(n){for(var e=null,t=null,o="",r=[],i=[],a={},c=0;c<n.length;c++)if(null===e)if(null===t)if(""===n[c])e=n[c];else if("\b"===n[c]){var s=rn(o,i),A=l()(s,2);o=A[0],i=A[1]}else o+=n[c];else if(";"===n[c])r.push(t),t="";else if(n[c]===tn){r.push(t),t=null,o="";for(var d=0;d<r.length;d++){var g=+r[d];Q[g]?a.foreground=Q[g]:nn[g]?a.background=nn[g]:39===g?delete a.foreground:49===g?delete a.background:en[g]?a[en[g]]=!0:22===g?a.bold=!1:23===g?a.italic=!1:24===g&&(a.underline=!1)}r=[]}else on.indexOf(n[c])>-1?(t="",r=[]):t+=n[c];else""===e&&"["===n[c]?(o&&(a.text=o,i.push(a),a={},o=""),e=null,t=""):(o+=e+n[c],e=null);return o&&(a.text=o+(e||""),i.push(a)),i},an=/\r{0,1}\n(?!\u0008)/,cn=function(n){return n.split(an)},sn=function(n){var e=cn(n),t=[];return e.forEach((function(n){n&&t.push(ln(n))})),t},An={name:"LogViewer",components:{VirtualList:r.a},props:{virtualAttrs:{type:Object,default:function(){}},rowHeight:{type:Number,default:20},height:{type:Number,default:0},log:String,loading:Boolean,autoScroll:{type:Boolean,default:!0},hasNumber:{type:Boolean,default:!0},scrollDuration:{type:Number,default:0}},data:function(){return{start:0,scrollStart:0,animate:null,LineWrapper:I}},computed:{remain:function(){return"number"===typeof+this.height?this.height>0?Math.floor(this.height/this.rowHeight):30:this.height.indexOf("px")>0?Math.floor(this.height.split("px")[0]/this.rowHeight):30},lines:function(){return sn(this.log)},linesCount:function(){return this.lines.length+(this.loading?1:0)}},watch:{lines:{immediate:!0,handler:function(n){this.$refs.virturalList&&this.$refs.virturalList.forceRender(),this.autoScroll&&this.setScrollTop(this.linesCount)}}},methods:{forceRender:function(){this.$refs.virturalList.forceRender()},getLineWrapperProps:function(n){var e=this,t=this.rowHeight,o={height:t,hasNumber:this.hasNumber,numberData:{number:n+1}};return this.lineWrapperStyle&&(o.comStyle=this.lineWrapperStyle(n+1)),this.loading&&n===this.linesCount-1?{props:o,scopedSlots:{default:function(){return e.$createElement(K)}}}:(o.data=this.lines[n],{props:o})},setScrollTop:function(n){var e=this;if(0!==this.scrollDuration){this.animate&&cancelAnimationFrame(this.animate);var t=this.scrollStart,o=0;o=Math.abs(n-this.scrollStart)/(60*this.scrollDuration/1e3),o=o<1?1:o;var r=function r(){e.animate=requestAnimationFrame((function(){t<n-o||t>n+o?(e.start=t,t=Math.floor(t<n-o?t+o:t-o),r()):(e.start=n,e.scrollStart=e.start)}))};r()}else this.$nextTick((function(){e.start=e.linesCount}))},onscroll:function(n,e){this.scrollStart=Math.ceil(e.offset/this.rowHeight)}}};const dn=An;var gn=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("virtual-list",n._b({ref:"virturalList",staticClass:"log-viewer",attrs:{size:n.rowHeight,remain:n.remain,bench:0,start:n.start,item:n.LineWrapper,itemcount:n.linesCount,itemprops:n.getLineWrapperProps,onscroll:n.onscroll}},"virtual-list",n.virtualAttrs,!1))},un=[];gn._withStripped=!0;const fn=function(n){n&&n("data-v-75033634_0",{source:".log-viewer {\n  font-size: 12px;\n  background-color: #222;\n  overflow-x: auto;\n  padding: 20px 0;\n}\n",map:{version:3,sources:["log-viewer.vue"],names:[],mappings:"AAAA;EACE,eAAe;EACf,sBAAsB;EACtB,gBAAgB;EAChB,eAAe;AACjB",file:"log-viewer.vue",sourcesContent:[".log-viewer {\n  font-size: 12px;\n  background-color: #222;\n  overflow-x: auto;\n  padding: 20px 0;\n}\n"]},media:void 0})},hn=void 0,bn=void 0,pn=!1;var mn=d({render:gn,staticRenderFns:un},fn,dn,hn,pn,bn,p,void 0);mn.install=function(n){n.component(mn.name,mn)};var Cn=null;"undefined"!==typeof window?Cn=window.Vue:"undefined"!==typeof n&&(Cn=n.Vue),Cn&&Cn.use(mn),e["a"]=mn}).call(this,t("c8ba"))}}]);