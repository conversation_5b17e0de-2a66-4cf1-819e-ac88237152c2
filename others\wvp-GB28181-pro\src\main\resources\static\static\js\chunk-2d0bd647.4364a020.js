(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0bd647"],{"2c99":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container",staticStyle:{height:"calc(100vh - 118px)",display:"grid","grid-template-columns":"400px auto"},attrs:{id:"region"}},[n("RegionTree",{ref:"regionTree",attrs:{"show-header":!0,edit:!0,"click-event":e.treeNodeClickEvent,"on-channel-change":e.onChannelChange,"enable-add-channel":!0,"add-channel-to-civil-code":e.addChannelToCivilCode}}),n("div",{staticStyle:{padding:"0 20px"}},[n("el-form",{attrs:{inline:!0,size:"mini"}},[n("el-form-item",[e.regionParents.length>0?n("el-breadcrumb",{staticStyle:{display:"ruby"},attrs:{separator:"/"}},e._l(e.regionParents,(function(t){return n("el-breadcrumb-item",{key:t},[e._v(e._s(t))])})),1):n("div",{staticStyle:{color:"#00c6ff"}},[e._v("未选择行政区划")])],1),n("div",{staticStyle:{float:"right"}},[n("el-form-item",{attrs:{label:"搜索"}},[n("el-input",{staticStyle:{width:"10rem","margin-right":"1rem"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),n("el-form-item",{attrs:{label:"在线状态"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[n("el-option",{attrs:{label:"全部",value:""}}),n("el-option",{attrs:{label:"在线",value:"true"}}),n("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),n("el-form-item",{attrs:{label:"类型"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getChannelList},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[n("el-option",{attrs:{label:"全部",value:""}}),e._l(Object.values(e.$channelTypeList),(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.add()}}},[e._v(" 添加通道 ")]),n("el-button",{attrs:{disabled:0===e.multipleSelection.length,type:"danger"},on:{click:function(t){return e.remove()}}},[e._v(" 移除通道 ")]),n("el-button",{attrs:{plain:"",type:"warning"},on:{click:function(t){return e.showUnusualChanel()}}},[e._v(" 异常挂载通道 ")])],1),n("el-form-item",[n("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.getChannelList()}}})],1)],1)],1),n("el-table",{ref:"channelListTable",staticStyle:{width:"100%"},attrs:{size:"medium",data:e.channelList,height:"calc(100vh - 190px)","header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.rowDblclick}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbDeviceId",label:"编号","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbManufacturer",label:"厂家","min-width":"100"}}),n("el-table-column",{attrs:{label:"类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[n("el-tag",{style:e.$channelTypeList[t.row.dataType].style,attrs:{size:"medium",effect:"plain",type:"success"}},[e._v(e._s(e.$channelTypeList[t.row.dataType].name))])],1)]}}])}),n("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.gbStatus?n("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.gbStatus?n("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}])})],1),n("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),n("GbChannelSelect",{ref:"gbChannelSelect",attrs:{"data-type":"civilCode"}}),n("UnusualRegionChannelSelect",{ref:"unusualRegionChannelSelect"})],1)},a=[],l=(n("b0c0"),n("d3b7"),n("94b9")),o=n("1322"),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.getChannelListLoading,expression:"getChannelListLoading"}],attrs:{id:"gbChannelSelect"}},[n("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"异常挂载通道",width:"60%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"append-to-body":""},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[n("el-form",{attrs:{inline:!0,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索"}},[n("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{size:"mini",placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getChannelList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),n("el-form-item",{attrs:{label:"在线状态"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.getChannelList},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[n("el-option",{attrs:{label:"全部",value:""}}),n("el-option",{attrs:{label:"在线",value:"true"}}),n("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),n("el-form-item",{attrs:{label:"类型"}},[n("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择","default-first-option":""},on:{change:e.getChannelList},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[n("el-option",{attrs:{label:"全部",value:""}}),e._l(Object.values(e.$channelTypeList),(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary",loading:e.getChannelListLoading,disabled:0===e.multipleSelection.length},on:{click:function(t){return e.clearUnusualRegion()}}},[e._v("清除")]),n("el-button",{attrs:{size:"mini",loading:e.getChannelListLoading},on:{click:function(t){return e.clearUnusualRegion(!0)}}},[e._v("全部清除")])],1),n("el-form-item",{staticStyle:{float:"right"}},[n("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",loading:e.getChannelListLoading},on:{click:function(t){return e.getChannelList()}}})],1)],1),n("el-table",{ref:"channelListTable",staticStyle:{width:"100%"},attrs:{size:"small",data:e.channelList,height:e.winHeight,"header-row-class-name":"table-header"},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{prop:"gbName",label:"名称","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbDeviceId",label:"编号","min-width":"180"}}),n("el-table-column",{attrs:{prop:"gbManufacturer",label:"厂家","min-width":"100"}}),n("el-table-column",{attrs:{prop:"gbCivilCode",label:"行政区划","min-width":"100"}}),n("el-table-column",{attrs:{label:"类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[n("el-tag",{style:e.$channelTypeList[t.row.dataType].style,attrs:{size:"medium",effect:"plain",type:"success"}},[e._v(e._s(e.$channelTypeList[t.row.dataType].name))])],1)]}}])}),n("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.gbStatus?n("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.gbStatus?n("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}])}),n("el-table-column",{attrs:{label:"操作","min-width":"140",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{size:"medium",type:"text",icon:"el-icon-plus",loading:t.row.addRegionLoading},on:{click:function(n){return e.addRegion(t.row)}}},[e._v(" 添加 ")])]}}])})],1),n("div",{staticStyle:{display:"grid","grid-template-columns":"1fr 1fr"}},[n("div",{staticStyle:{"text-align":"left","line-height":"32px"}},[n("i",{staticClass:"el-icon-info"}),e._v(" 清除后通道可正常添加到行政区划，添加可以自动添加对应的行政区划节点。 ")]),n("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[10,25,35,50,200,1e3,5e4],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1)],1)],1)},r=[],c=n("a888"),h={name:"UnusualRegionChannelSelect",directives:{elDragDialog:c["a"]},props:[],data:function(){return{showDialog:!1,channelList:[],searchSrt:"",online:null,channelType:"",winHeight:580,currentPage:1,count:10,total:0,getChannelListLoading:!1,multipleSelection:[]}},computed:{},methods:{initData:function(){this.getChannelList()},currentChange:function(e){this.currentPage=e,this.getChannelList()},handleSizeChange:function(e){this.count=e,this.getChannelList()},handleSelectionChange:function(e){this.multipleSelection=e},getChannelList:function(){var e=this;this.getChannelListLoading=!0,this.$store.dispatch("commonChanel/getUnusualCivilCodeList",{page:this.currentPage,count:this.count,channelType:this.channelType,query:this.searchSrt,online:this.online}).then((function(t){e.total=t.total;for(var n=0;n<t.list.length;n++)t.list[n]["addRegionLoading"]=!1;e.channelList=t.list})).catch((function(e){console.error(e)})).finally((function(){e.getChannelListLoading=!1}))},openDialog:function(){this.showDialog=!0,this.initData()},close:function(){this.showDialog=!1},clearUnusualRegion:function(e){var t=this,n=null;if(e||this.multipleSelection.length>0){n=[];for(var i=0;i<this.multipleSelection.length;i++)n.push(this.multipleSelection[i].gbId)}this.$store.dispatch("commonChanel/clearUnusualCivilCodeList",{all:e,channelIds:n}).then((function(e){t.$message.success({showClose:!0,message:"清除成功"}),t.getChannelList()})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))},addRegion:function(e){var t=this;e.addRegionLoading=!0,this.$store.dispatch("region/description",e.gbCivilCode).then((function(n){t.$confirm("确定添加： ".concat(n),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((function(){t.$store.dispatch("region/addByCivilCode",e.gbCivilCode).then((function(e){t.$message.success({showClose:!0,message:"添加成功"}),t.initData()}))})).catch((function(){}))})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){e.addRegionLoading=!1}))}}},u=h,g=n("2877"),d=Object(g["a"])(u,s,r,!1,null,null,null),m=d.exports,p={name:"Region",components:{GbChannelSelect:o["a"],RegionTree:l["a"],UnusualRegionChannelSelect:m},data:function(){return{channelList:[],searchSrt:"",channelType:"",online:"",currentPage:1,count:15,total:0,loading:!1,loadSnap:{},regionId:"",regionDeviceId:"",regionParents:[],multipleSelection:[]}},created:function(){this.initData()},destroyed:function(){},methods:{initData:function(){this.getChannelList()},currentChange:function(e){this.currentPage=e,this.initData()},handleSizeChange:function(e){this.count=e,this.getChannelList()},getChannelList:function(){var e=this;this.$store.dispatch("commonChanel/getCivilCodeList",{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType,civilCode:this.regionDeviceId}).then((function(t){e.total=t.total,e.channelList=t.list,e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))}))},handleSelectionChange:function(e){this.multipleSelection=e},rowDblclick:function(e,t){},add:function(e){var t=this;""!==this.regionDeviceId?this.$refs.gbChannelSelect.openDialog((function(e){console.log("选择的数据"),console.log(e),t.addChannelToCivilCode(t.regionDeviceId,e)})):this.$message.info({showClose:!0,message:"请选择左侧行政区划"})},addChannelToCivilCode:function(e,t){var n=this;if(0!==t.length){for(var i=[],a=0;a<t.length;a++)i.push(t[a].gbId);this.loading=!0,this.$store.dispatch("commonChanel/addToRegion",{civilCode:e,channelIds:i}).then((function(e){n.$message.success({showClose:!0,message:"保存成功"}),n.getChannelList()})).catch((function(e){n.$message.error({showClose:!0,message:e})})).finally((function(){n.loading=!1}))}},remove:function(e){for(var t=this,n=[],i=0;i<this.multipleSelection.length;i++)n.push(this.multipleSelection[i].gbId);0!==n.length?(this.loading=!0,this.$store.dispatch("commonChanel/deleteFromRegion",n).then((function(e){t.$message.success({showClose:!0,message:"保存成功"}),t.getChannelList(),t.$refs.regionTree.refresh(t.regionDeviceId)})).catch((function(e){t.$message.error({showClose:!0,message:e})})).finally((function(){t.loading=!1}))):this.$message.info({showClose:!0,message:"请选择通道"})},showUnusualChanel:function(){this.$refs.unusualRegionChannelSelect.openDialog()},search:function(){this.currentPage=1,this.total=0,this.initData()},refresh:function(){this.initData()},treeNodeClickEvent:function(e){var t=this;this.regionDeviceId=e.deviceId,""===e.deviceId&&(this.channelList=[],this.regionParents=[]),this.initData(),this.$store.dispatch("region/queryPath",this.regionDeviceId).then((function(e){for(var n=[],i=0;i<e.length;i++)n.push(e[i].name);t.regionParents=n}))},gbChannelSelectEnd:function(e){console.log(e)},onChannelChange:function(e){}}},f=p,b=Object(g["a"])(f,i,a,!1,null,null,null);t["default"]=b.exports}}]);