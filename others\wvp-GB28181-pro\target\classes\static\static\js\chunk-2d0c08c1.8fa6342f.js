(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c08c1"],{"41f4":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container",attrs:{id:"streamProxyList"}},[e.streamProxy?e._e():r("div",{staticStyle:{height:"calc(100vh - 124px)"}},[r("el-form",{attrs:{inline:!0,size:"mini"}},[r("el-form-item",{attrs:{label:"搜索"}},[r("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.getStreamProxyList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),r("el-form-item",{attrs:{label:"流媒体"}},[r("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getStreamProxyList},model:{value:e.mediaServerId,callback:function(t){e.mediaServerId=t},expression:"mediaServerId"}},[r("el-option",{attrs:{label:"全部",value:""}}),e._l(e.mediaServerList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),r("el-form-item",{attrs:{label:"拉流状态"}},[r("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.getStreamProxyList},model:{value:e.pulling,callback:function(t){e.pulling=t},expression:"pulling"}},[r("el-option",{attrs:{label:"全部",value:""}}),r("el-option",{attrs:{label:"正在拉流",value:"true"}}),r("el-option",{attrs:{label:"尚未拉流",value:"false"}})],1)],1),r("el-form-item",[r("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.addStreamProxy}},[e._v("添加代理")])],1),r("el-form-item",{staticStyle:{float:"right"}},[r("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.refresh()}}})],1)],1),r("devicePlayer",{ref:"devicePlayer"}),r("el-table",{staticStyle:{width:"100%"},attrs:{size:"small",data:e.streamProxyList,height:"calc(100% - 64px)"}},[r("el-table-column",{attrs:{prop:"app",label:"流应用名","min-width":"120","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"stream",label:"流ID","min-width":"120","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"流地址","min-width":"250","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[r("el-tag",{directives:[{name:"clipboard",rawName:"v-clipboard",value:t.row.srcUrl,expression:"scope.row.srcUrl"}],attrs:{size:"medium"},on:{success:function(t){return e.$message({type:"success",message:"成功拷贝到粘贴板"})}}},[r("i",{staticClass:"el-icon-document-copy",attrs:{title:"点击拷贝"}}),e._v(" "+e._s(t.row.srcUrl)+" ")])],1)]}}],null,!1,2404782382)}),r("el-table-column",{attrs:{prop:"mediaServerId",label:"流媒体","min-width":"180"}}),r("el-table-column",{attrs:{label:"代理方式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[e._v(" "+e._s("default"===t.row.type?"默认":"FFMPEG代理")+" ")])]}}],null,!1,3096416671)}),r("el-table-column",{attrs:{prop:"gbDeviceId",label:"国标编码","min-width":"180","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"拉流状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.pulling&&e.myServerId!==t.row.serverId?r("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("正在拉流")]):e._e(),t.row.pulling&&e.myServerId===t.row.serverId?r("el-tag",{attrs:{size:"medium"}},[e._v("正在拉流")]):e._e(),t.row.pulling?e._e():r("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("尚未拉流")])],1)]}}],null,!1,819116356)}),r("el-table-column",{attrs:{label:"启用","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.enable&&e.myServerId!==t.row.serverId?r("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("已启用")]):e._e(),t.row.enable&&e.myServerId===t.row.serverId?r("el-tag",{attrs:{size:"medium"}},[e._v("已启用")]):e._e(),t.row.enable?e._e():r("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("未启用")])],1)]}}],null,!1,3937613589)}),r("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"150","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"操作",width:"370",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{size:"medium",loading:t.row.playLoading,icon:"el-icon-video-play",type:"text"},on:{click:function(r){return e.play(t.row)}}},[e._v("播放")]),r("el-divider",{attrs:{direction:"vertical"}}),t.row.pulling?r("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-switch-button",type:"text"},on:{click:function(r){return e.stopPlay(t.row)}}},[e._v("停止")]):e._e(),t.row.pulling?r("el-divider",{attrs:{direction:"vertical"}}):e._e(),r("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(r){return e.edit(t.row)}}},[e._v(" 编辑 ")]),r("el-divider",{attrs:{direction:"vertical"}}),r("el-button",{attrs:{size:"medium",icon:"el-icon-cloudy",type:"text"},on:{click:function(r){return e.queryCloudRecords(t.row)}}},[e._v("云端录像")]),r("el-divider",{attrs:{direction:"vertical"}}),r("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",icon:"el-icon-delete",type:"text"},on:{click:function(r){return e.deleteStreamProxy(t.row)}}},[e._v("删除")])]}}],null,!1,317584972)})],1),r("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),e.streamProxy?r("StreamProxyEdit",{attrs:{"close-edit":e.closeEdit},model:{value:e.streamProxy,callback:function(t){e.streamProxy=t},expression:"streamProxy"}}):e._e()],1)},s=[],o=(r("99af"),r("d3b7"),r("0328")),l=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{width:"100%"},attrs:{id:"StreamProxyEdit"}},[r("div",{staticClass:"page-header"},[r("div",{staticClass:"page-title"},[r("el-page-header",{attrs:{content:"编辑拉流代理信息"},on:{back:e.close}})],1)]),r("el-tabs",{staticStyle:{"padding-top":"1rem"},attrs:{"tab-position":"top"}},[r("el-tab-pane",{staticStyle:{"padding-top":"1rem"},attrs:{label:"拉流代理信息"}},[r("el-form",{ref:"streamProxy",staticStyle:{width:"50%",margin:"0 auto"},attrs:{rules:e.rules,model:e.streamProxy,"label-width":"140px"}},[r("el-form-item",{attrs:{label:"类型",prop:"type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择代理类型"},model:{value:e.streamProxy.type,callback:function(t){e.$set(e.streamProxy,"type",t)},expression:"streamProxy.type"}},[r("el-option",{key:"默认",attrs:{label:"默认",value:"default"}}),r("el-option",{key:"FFmpeg",attrs:{label:"FFmpeg",value:"ffmpeg"}})],1)],1),r("el-form-item",{attrs:{label:"应用名",prop:"app"}},[r("el-input",{attrs:{clearable:""},model:{value:e.streamProxy.app,callback:function(t){e.$set(e.streamProxy,"app",t)},expression:"streamProxy.app"}})],1),r("el-form-item",{attrs:{label:"流ID",prop:"stream"}},[r("el-input",{attrs:{clearable:""},model:{value:e.streamProxy.stream,callback:function(t){e.$set(e.streamProxy,"stream",t)},expression:"streamProxy.stream"}})],1),r("el-form-item",{attrs:{label:"拉流地址",prop:"url"}},[r("el-input",{attrs:{clearable:""},model:{value:e.streamProxy.srcUrl,callback:function(t){e.$set(e.streamProxy,"srcUrl",t)},expression:"streamProxy.srcUrl"}})],1),r("el-form-item",{attrs:{label:"超时时间(秒)",prop:"timeoutMs"}},[r("el-input",{attrs:{clearable:""},model:{value:e.streamProxy.timeout,callback:function(t){e.$set(e.streamProxy,"timeout",t)},expression:"streamProxy.timeout"}})],1),r("el-form-item",{attrs:{label:"节点选择",prop:"rtpType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择拉流节点"},on:{change:e.mediaServerIdChange},model:{value:e.streamProxy.relatesMediaServerId,callback:function(t){e.$set(e.streamProxy,"relatesMediaServerId",t)},expression:"streamProxy.relatesMediaServerId"}},[r("el-option",{key:"auto",attrs:{label:"自动选择",value:""}}),e._l(e.mediaServerList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),"ffmpeg"==e.streamProxy.type?r("el-form-item",{attrs:{label:"FFmpeg命令模板",prop:"ffmpegCmdKey"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择FFmpeg命令模板"},model:{value:e.streamProxy.ffmpegCmdKey,callback:function(t){e.$set(e.streamProxy,"ffmpegCmdKey",t)},expression:"streamProxy.ffmpegCmdKey"}},e._l(Object.keys(e.ffmpegCmdList),(function(t){return r("el-option",{key:t,attrs:{label:e.ffmpegCmdList[t],value:t}})})),1)],1):e._e(),r("el-form-item",{attrs:{label:"拉流方式(RTSP)",prop:"rtspType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择拉流方式"},model:{value:e.streamProxy.rtspType,callback:function(t){e.$set(e.streamProxy,"rtspType",t)},expression:"streamProxy.rtspType"}},[r("el-option",{attrs:{label:"TCP",value:"0"}}),r("el-option",{attrs:{label:"UDP",value:"1"}}),r("el-option",{attrs:{label:"组播",value:"2"}})],1)],1),r("el-form-item",{attrs:{label:"无人观看",prop:"noneReader"}},[r("el-radio-group",{model:{value:e.streamProxy.noneReader,callback:function(t){e.$set(e.streamProxy,"noneReader",t)},expression:"streamProxy.noneReader"}},[r("el-radio",{attrs:{label:0}},[e._v("不做处理")]),r("el-radio",{attrs:{label:1}},[e._v("停用")]),r("el-radio",{attrs:{label:2}},[e._v("移除")])],1)],1),r("el-form-item",{attrs:{label:"其他选项"}},[r("div",{staticStyle:{float:"left"}},[r("el-checkbox",{attrs:{label:"启用"},model:{value:e.streamProxy.enable,callback:function(t){e.$set(e.streamProxy,"enable",t)},expression:"streamProxy.enable"}}),r("el-checkbox",{attrs:{label:"开启音频"},model:{value:e.streamProxy.enableAudio,callback:function(t){e.$set(e.streamProxy,"enableAudio",t)},expression:"streamProxy.enableAudio"}}),r("el-checkbox",{attrs:{label:"录制"},model:{value:e.streamProxy.enableMp4,callback:function(t){e.$set(e.streamProxy,"enableMp4",t)},expression:"streamProxy.enableMp4"}})],1)]),r("el-form-item",[r("div",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.onSubmit}},[e._v("保存")]),r("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1),e.streamProxy.id?r("el-tab-pane",{attrs:{label:"国标通道配置"}},[r("CommonChannelEdit",{ref:"commonChannelEdit",attrs:{"data-form":e.streamProxy,cancel:e.close}})],1):e._e()],1)],1)},i=[],n=(r("b64b"),r("7317")),c={name:"ChannelEdit",components:{CommonChannelEdit:n["a"]},props:["value","closeEdit"],data:function(){return{saveLoading:!1,streamProxy:this.value,mediaServerList:{},ffmpegCmdList:{},rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],app:[{required:!0,message:"请输入应用名",trigger:"blur"}],stream:[{required:!0,message:"请输入流ID",trigger:"blur"}],srcUrl:[{required:!0,message:"请输入要代理的流",trigger:"blur"}],timeout:[{required:!0,message:"请输入FFmpeg推流成功超时时间",trigger:"blur"}],ffmpegCmdKey:[{required:!1,message:"请输入FFmpeg命令参数模板（可选）",trigger:"blur"}]}}},watch:{value:function(e,t){this.streamProxy=e}},created:function(){var e=this;console.log(this.streamProxy),this.$store.dispatch("server/getOnlineMediaServerList").then((function(t){e.mediaServerList=t}))},methods:{onSubmit:function(){var e=this;this.saveLoading=!0,this.noneReaderHandler(),this.streamProxy.id?this.$store.dispatch("streamProxy/update",this.streamProxy).then((function(t){e.saveLoading=!1,e.$message.success({showClose:!0,message:"保存成功"}),e.streamProxy=t})).catch((function(t){e.$message.error({showClose:!0,message:t}),e.saveLoading=!1})).finally((function(){e.saveLoading=!1})):this.$store.dispatch("streamProxy/add",this.streamProxy).then((function(t){e.saveLoading=!1,e.$message.success({showClose:!0,message:"保存成功"}),e.streamProxy=t})).catch((function(t){e.$message.error({showClose:!0,message:t}),e.saveLoading=!1})).finally((function(){e.saveLoading=!1}))},close:function(){this.closeEdit()},mediaServerIdChange:function(){var e=this;"auto"!==this.streamProxy.relatesMediaServerId&&this.$store.dispatch("streamProxy/queryFfmpegCmdList",this.streamProxy.relatesMediaServerId).then((function(t){e.ffmpegCmdList=t,e.streamProxy.ffmpegCmdKey=Object.keys(t)[0]}))},noneReaderHandler:function(){console.log(this.streamProxy),this.streamProxy.noneReader&&0!==this.streamProxy.noneReader?1===this.streamProxy.noneReader?(this.streamProxy.enableDisableNoneReader=!0,this.streamProxy.enableRemoveNoneReader=!1):2===this.streamProxy.noneReader&&(this.streamProxy.enableDisableNoneReader=!1,this.streamProxy.enableRemoveNoneReader=!0):(this.streamProxy.enableDisableNoneReader=!1,this.streamProxy.enableRemoveNoneReader=!1)}}},m=c,d=r("2877"),u=Object(d["a"])(m,l,i,!1,null,null,null),p=u.exports,y=r("2b0e"),f={name:"Proxy",components:{devicePlayer:o["a"],StreamProxyEdit:p},data:function(){return{streamProxyList:[],currentPusher:{},updateLooper:0,currentDeviceChannelsLenth:0,currentPage:1,count:15,total:0,streamProxy:null,searchSrt:"",mediaServerId:"",pulling:"",mediaServerList:[]}},computed:{Vue:function(){return y["default"]},myServerId:function(){return this.$store.getters.serverId}},mounted:function(){this.initData(),this.startUpdateList()},destroyed:function(){this.$destroy("videojs"),clearTimeout(this.updateLooper)},methods:{initData:function(){var e=this;this.getStreamProxyList(),this.$store.dispatch("server/getOnlineMediaServerList").then((function(t){e.mediaServerList=t}))},startUpdateList:function(){var e=this;this.updateLooper=setInterval((function(){e.streamProxy||e.getStreamProxyList()}),1e3)},currentChange:function(e){this.currentPage=e,this.getStreamProxyList()},handleSizeChange:function(e){this.count=e,this.getStreamProxyList()},getStreamProxyList:function(){var e=this;this.$store.dispatch("streamProxy/queryList",{page:this.currentPage,count:this.count,query:this.searchSrt,pulling:this.pulling,mediaServerId:this.mediaServerId}).then((function(t){e.total=t.total;for(var r=0;r<t.list.length;r++)t.list[r]["playLoading"]=!1;e.streamProxyList=t.list}))},addStreamProxy:function(){this.streamProxy={type:"default",dataType:3,noneReader:1,enable:!0,enableAudio:!0,mediaServerId:"",timeout:10}},edit:function(e){e.enableDisableNoneReader?this.$set(e,"noneReader",1):e.enableRemoveNoneReader?this.$set(e,"noneReader",2):this.$set(e,"noneReader",0),this.streamProxy=e,this.$set(this.streamProxy,"rtspType",e.rtspType)},closeEdit:function(e){this.streamProxy=null},play:function(e){var t=this;e.playLoading=!0,this.$store.dispatch("streamProxy/play",e.id).then((function(e){t.$refs.devicePlayer.openDialog("streamPlay",null,null,{streamInfo:e,hasAudio:!0})})).catch((function(e){console.log(e)})).finally((function(){e.playLoading=!1}))},stopPlay:function(e){var t=this;this.$store.dispatch("streamProxy/stopPlay",e.id).then((function(e){t.$refs.devicePlayer.openDialog("streamPlay",null,null,{streamInfo:e,hasAudio:!0})})).catch((function(e){console.log(e)}))},queryCloudRecords:function(e){this.$router.push("/cloudRecord/detail/".concat(e.app,"/").concat(e.stream))},deleteStreamProxy:function(e){var t=this;this.$confirm("确定删除此代理吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$store.dispatch("streamProxy/remove",e.id).then((function(e){t.$message.success({showClose:!0,message:"删除成功"}),t.initData()}))})).catch((function(){}))},refresh:function(){this.initData()}}},h=f,v=Object(d["a"])(h,a,s,!1,null,null,null);t["default"]=v.exports}}]);