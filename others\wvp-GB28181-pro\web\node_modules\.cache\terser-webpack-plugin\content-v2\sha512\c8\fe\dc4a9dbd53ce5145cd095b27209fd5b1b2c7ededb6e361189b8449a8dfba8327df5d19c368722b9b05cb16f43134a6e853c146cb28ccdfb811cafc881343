{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-1833c21d\",\"chunk-b3c5ace6\",\"chunk-3b4a2238\"],{\"0328\":function(e,t,s){\"use strict\";var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.isLoging,expression:\"isLoging\"}],attrs:{id:\"devicePlayer\"}},[e.showVideoDialog?s(\"el-dialog\",{directives:[{name:\"el-drag-dialog\",rawName:\"v-el-drag-dialog\"}],attrs:{title:\"视频播放\",top:\"0\",\"close-on-click-modal\":!1,visible:e.showVideoDialog},on:{\"update:visible\":function(t){e.showVideoDialog=t},close:function(t){return e.close()}}},[s(\"div\",{staticStyle:{width:\"100%\",height:\"100%\"}},[Object.keys(this.player).length>1?s(\"el-tabs\",{attrs:{type:\"card\",stretch:!0},on:{\"tab-click\":e.changePlayer},model:{value:e.activePlayer,callback:function(t){e.activePlayer=t},expression:\"activePlayer\"}},[s(\"el-tab-pane\",{attrs:{label:\"Jessibuca\",name:\"jessibuca\"}},[\"jessibuca\"===e.activePlayer?s(\"jessibucaPlayer\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),s(\"el-tab-pane\",{attrs:{label:\"WebRTC\",name:\"webRTC\"}},[\"webRTC\"===e.activePlayer?s(\"rtc-player\",{ref:\"webRTC\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),s(\"el-tab-pane\",{attrs:{label:\"h265web\",name:\"h265web\"}},[\"h265web\"===e.activePlayer?s(\"h265web\",{ref:\"h265web\",attrs:{\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\",\"show-button\":!0}}):e._e()],1)],1):e._e(),1==Object.keys(this.player).length&&this.player.jessibuca?s(\"jessibucaPlayer\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.webRTC?s(\"rtc-player\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e(),1==Object.keys(this.player).length&&this.player.h265web?s(\"h265web\",{ref:\"jessibuca\",attrs:{visible:e.showVideoDialog,\"video-url\":e.videoUrl,error:e.videoError,message:e.videoError,height:\"100px\",\"has-audio\":e.hasAudio,fluent:\"\",autoplay:\"\",live:\"\"},on:{\"update:visible\":function(t){e.showVideoDialog=t}}}):e._e()],1),s(\"div\",{staticStyle:{\"text-align\":\"right\",\"margin-top\":\"1rem\"},attrs:{id:\"shared\"}},[s(\"el-tabs\",{on:{\"tab-click\":e.tabHandleClick},model:{value:e.tabActiveName,callback:function(t){e.tabActiveName=t},expression:\"tabActiveName\"}},[s(\"el-tab-pane\",{attrs:{label:\"实时视频\",name:\"media\"}},[s(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[s(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"播放地址：\")]),s(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedUrl,callback:function(t){e.$set(e.getPlayerShared,\"sharedUrl\",t)},expression:\"getPlayerShared.sharedUrl\"}},[s(\"template\",{slot:\"append\"},[s(\"i\",{staticClass:\"cpoy-btn el-icon-document-copy\",staticStyle:{cursor:\"pointer\"},attrs:{title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedUrl)}}})])],2)],1),s(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[s(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"iframe：\")]),s(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedIframe,callback:function(t){e.$set(e.getPlayerShared,\"sharedIframe\",t)},expression:\"getPlayerShared.sharedIframe\"}},[s(\"template\",{slot:\"append\"},[s(\"i\",{staticClass:\"cpoy-btn el-icon-document-copy\",staticStyle:{cursor:\"pointer\"},attrs:{title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}}})])],2)],1),s(\"div\",{staticStyle:{display:\"flex\",\"margin-bottom\":\"0.5rem\",height:\"2.5rem\"}},[s(\"span\",{staticStyle:{width:\"5rem\",\"line-height\":\"2.5rem\",\"text-align\":\"right\"}},[e._v(\"资源地址：\")]),s(\"el-input\",{attrs:{disabled:!0},model:{value:e.getPlayerShared.sharedRtmp,callback:function(t){e.$set(e.getPlayerShared,\"sharedRtmp\",t)},expression:\"getPlayerShared.sharedRtmp\"}},[s(\"el-button\",{staticStyle:{cursor:\"pointer\"},attrs:{slot:\"append\",icon:\"el-icon-document-copy\",title:\"点击拷贝\"},on:{click:function(t){return e.copyUrl(e.getPlayerShared.sharedIframe)}},slot:\"append\"}),e.streamInfo?s(\"el-dropdown\",{attrs:{slot:\"prepend\",trigger:\"click\"},on:{command:e.copyUrl},slot:\"prepend\"},[s(\"el-button\",[e._v(\" 更多地址\"),s(\"i\",{staticClass:\"el-icon-arrow-down el-icon--right\"})]),s(\"el-dropdown-menu\",[e.streamInfo.flv?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.flv}},[s(\"el-tag\",[e._v(\"FLV:\")]),s(\"span\",[e._v(e._s(e.streamInfo.flv))])],1):e._e(),e.streamInfo.https_flv?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_flv}},[s(\"el-tag\",[e._v(\"FLV(https):\")]),s(\"span\",[e._v(e._s(e.streamInfo.https_flv))])],1):e._e(),e.streamInfo.ws_flv?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_flv}},[s(\"el-tag\",[e._v(\"FLV(ws):\")]),s(\"span\",[e._v(e._s(e.streamInfo.ws_flv))])],1):e._e(),e.streamInfo.wss_flv?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_flv}},[s(\"el-tag\",[e._v(\"FLV(wss):\")]),s(\"span\",[e._v(e._s(e.streamInfo.wss_flv))])],1):e._e(),e.streamInfo.fmp4?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.fmp4}},[s(\"el-tag\",[e._v(\"FMP4:\")]),s(\"span\",[e._v(e._s(e.streamInfo.fmp4))])],1):e._e(),e.streamInfo.https_fmp4?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_fmp4}},[s(\"el-tag\",[e._v(\"FMP4(https):\")]),s(\"span\",[e._v(e._s(e.streamInfo.https_fmp4))])],1):e._e(),e.streamInfo.ws_fmp4?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_fmp4}},[s(\"el-tag\",[e._v(\"FMP4(ws):\")]),s(\"span\",[e._v(e._s(e.streamInfo.ws_fmp4))])],1):e._e(),e.streamInfo.wss_fmp4?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_fmp4}},[s(\"el-tag\",[e._v(\"FMP4(wss):\")]),s(\"span\",[e._v(e._s(e.streamInfo.wss_fmp4))])],1):e._e(),e.streamInfo.hls?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.hls}},[s(\"el-tag\",[e._v(\"HLS:\")]),s(\"span\",[e._v(e._s(e.streamInfo.hls))])],1):e._e(),e.streamInfo.https_hls?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_hls}},[s(\"el-tag\",[e._v(\"HLS(https):\")]),s(\"span\",[e._v(e._s(e.streamInfo.https_hls))])],1):e._e(),e.streamInfo.ws_hls?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_hls}},[s(\"el-tag\",[e._v(\"HLS(ws):\")]),s(\"span\",[e._v(e._s(e.streamInfo.ws_hls))])],1):e._e(),e.streamInfo.wss_hls?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_hls}},[s(\"el-tag\",[e._v(\"HLS(wss):\")]),s(\"span\",[e._v(e._s(e.streamInfo.wss_hls))])],1):e._e(),e.streamInfo.ts?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ts}},[s(\"el-tag\",[e._v(\"TS:\")]),s(\"span\",[e._v(e._s(e.streamInfo.ts))])],1):e._e(),e.streamInfo.https_ts?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.https_ts}},[s(\"el-tag\",[e._v(\"TS(https):\")]),s(\"span\",[e._v(e._s(e.streamInfo.https_ts))])],1):e._e(),e.streamInfo.ws_ts?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.ws_ts}},[s(\"el-tag\",[e._v(\"TS(ws):\")]),s(\"span\",[e._v(e._s(e.streamInfo.ws_ts))])],1):e._e(),e.streamInfo.wss_ts?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.wss_ts}},[s(\"el-tag\",[e._v(\"TS(wss):\")]),s(\"span\",[e._v(e._s(e.streamInfo.wss_ts))])],1):e._e(),e.streamInfo.rtc?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtc}},[s(\"el-tag\",[e._v(\"RTC:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtc))])],1):e._e(),e.streamInfo.rtcs?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtcs}},[s(\"el-tag\",[e._v(\"RTCS:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtcs))])],1):e._e(),e.streamInfo.rtmp?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtmp}},[s(\"el-tag\",[e._v(\"RTMP:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtmp))])],1):e._e(),e.streamInfo.rtmps?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtmps}},[s(\"el-tag\",[e._v(\"RTMPS:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtmps))])],1):e._e(),e.streamInfo.rtsp?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtsp}},[s(\"el-tag\",[e._v(\"RTSP:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtsp))])],1):e._e(),e.streamInfo.rtsps?s(\"el-dropdown-item\",{attrs:{command:e.streamInfo.rtsps}},[s(\"el-tag\",[e._v(\"RTSPS:\")]),s(\"span\",[e._v(e._s(e.streamInfo.rtsps))])],1):e._e()],1)],1):e._e()],1)],1)]),e.showPtz?s(\"el-tab-pane\",{attrs:{label:\"云台控制\",name:\"control\"}},[s(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"240px auto\",height:\"180px\",overflow:\"auto\"}},[s(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"6.25rem auto\"}},[s(\"div\",{staticClass:\"control-wrapper\"},[s(\"div\",{staticClass:\"control-btn control-top\",on:{mousedown:function(t){return e.ptzCamera(\"up\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-caret-top\"}),s(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),s(\"div\",{staticClass:\"control-btn control-left\",on:{mousedown:function(t){return e.ptzCamera(\"left\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-caret-left\"}),s(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),s(\"div\",{staticClass:\"control-btn control-bottom\",on:{mousedown:function(t){return e.ptzCamera(\"down\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-caret-bottom\"}),s(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),s(\"div\",{staticClass:\"control-btn control-right\",on:{mousedown:function(t){return e.ptzCamera(\"right\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-caret-right\"}),s(\"div\",{staticClass:\"control-inner-btn control-inner\"})]),s(\"div\",{staticClass:\"control-round\"},[s(\"div\",{staticClass:\"control-round-inner\"},[s(\"i\",{staticClass:\"fa fa-pause-circle\"})])]),s(\"div\",{staticClass:\"contro-speed\",staticStyle:{position:\"absolute\",left:\"4px\",top:\"7rem\",width:\"6.25rem\"}},[s(\"el-slider\",{attrs:{max:100},model:{value:e.controSpeed,callback:function(t){e.controSpeed=t},expression:\"controSpeed\"}})],1)]),s(\"div\",[s(\"div\",{staticClass:\"ptz-btn-box\"},[s(\"div\",{attrs:{title:\"变倍+\"},on:{mousedown:function(t){return e.ptzCamera(\"zoomin\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-zoom-in control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),s(\"div\",{attrs:{title:\"变倍-\"},on:{mousedown:function(t){return e.ptzCamera(\"zoomout\")},mouseup:function(t){return e.ptzCamera(\"stop\")}}},[s(\"i\",{staticClass:\"el-icon-zoom-out control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])]),s(\"div\",{staticClass:\"ptz-btn-box\"},[s(\"div\",{attrs:{title:\"聚焦+\"},on:{mousedown:function(t){return e.focusCamera(\"near\")},mouseup:function(t){return e.focusCamera(\"stop\")}}},[s(\"i\",{staticClass:\"iconfont icon-bianjiao-fangda control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),s(\"div\",{attrs:{title:\"聚焦-\"},on:{mousedown:function(t){return e.focusCamera(\"far\")},mouseup:function(t){return e.focusCamera(\"stop\")}}},[s(\"i\",{staticClass:\"iconfont icon-bianjiao-suoxiao control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])]),s(\"div\",{staticClass:\"ptz-btn-box\"},[s(\"div\",{attrs:{title:\"光圈+\"},on:{mousedown:function(t){return e.irisCamera(\"in\")},mouseup:function(t){return e.irisCamera(\"stop\")}}},[s(\"i\",{staticClass:\"iconfont icon-guangquan control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})]),s(\"div\",{attrs:{title:\"光圈-\"},on:{mousedown:function(t){return e.irisCamera(\"out\")},mouseup:function(t){return e.irisCamera(\"stop\")}}},[s(\"i\",{staticClass:\"iconfont icon-guangquan- control-zoom-btn\",staticStyle:{\"font-size\":\"1.5rem\"}})])])])]),\"control\"===e.tabActiveName?s(\"div\",{staticStyle:{\"text-align\":\"left\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{size:\"mini\",placeholder:\"请选择云台功能\"},model:{value:e.ptzMethod,callback:function(t){e.ptzMethod=t},expression:\"ptzMethod\"}},[s(\"el-option\",{attrs:{label:\"预置点\",value:\"preset\"}}),s(\"el-option\",{attrs:{label:\"巡航组\",value:\"cruise\"}}),s(\"el-option\",{attrs:{label:\"自动扫描\",value:\"scan\"}}),s(\"el-option\",{attrs:{label:\"雨刷\",value:\"wiper\"}}),s(\"el-option\",{attrs:{label:\"辅助开关\",value:\"switch\"}})],1),\"preset\"===e.ptzMethod?s(\"ptzPreset\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"cruise\"===e.ptzMethod?s(\"ptzCruising\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"scan\"===e.ptzMethod?s(\"ptzScan\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"wiper\"===e.ptzMethod?s(\"ptzWiper\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e(),\"switch\"===e.ptzMethod?s(\"ptzSwitch\",{staticStyle:{\"margin-top\":\"1rem\"},attrs:{\"channel-device-id\":e.channelId,\"device-id\":e.deviceId}}):e._e()],1):e._e()])]):e._e(),s(\"el-tab-pane\",{attrs:{label:\"编码信息\",name:\"codec\"}},[s(\"mediaInfo\",{ref:\"mediaInfo\",attrs:{app:e.app,stream:e.streamId,\"media-server-id\":e.mediaServerId}})],1),e.showBroadcast?s(\"el-tab-pane\",{attrs:{label:\"语音对讲\",name:\"broadcast\"}},[s(\"div\",{staticStyle:{padding:\"0 10px\"}},[s(\"el-radio-group\",{attrs:{disabled:-1!==e.broadcastStatus},model:{value:e.broadcastMode,callback:function(t){e.broadcastMode=t},expression:\"broadcastMode\"}},[s(\"el-radio\",{attrs:{label:!0}},[e._v(\"喊话(Broadcast)\")]),s(\"el-radio\",{attrs:{label:!1}},[e._v(\"对讲(Talk)\")])],1)],1),s(\"div\",{staticClass:\"trank\",staticStyle:{\"text-align\":\"center\"}},[s(\"el-button\",{staticStyle:{\"font-size\":\"32px\",padding:\"24px\",\"margin-top\":\"24px\"},attrs:{type:e.getBroadcastStatus(),disabled:-2===e.broadcastStatus,circle:\"\",icon:\"el-icon-microphone\"},on:{click:function(t){return e.broadcastStatusClick()}}}),s(\"p\",[-2===e.broadcastStatus?s(\"span\",[e._v(\"正在释放资源\")]):e._e(),-1===e.broadcastStatus?s(\"span\",[e._v(\"点击开始对讲\")]):e._e(),0===e.broadcastStatus?s(\"span\",[e._v(\"等待接通中...\")]):e._e(),1===e.broadcastStatus?s(\"span\",[e._v(\"请说话\")]):e._e()])],1)]):e._e()],1)],1)]):e._e()],1)},a=[],i=(s(\"caad\"),s(\"b0c0\"),s(\"e9c4\"),s(\"b64b\"),s(\"2532\"),s(\"a888\")),n=s(\"1c46\"),r=s.n(n),l=s(\"bbf2\"),c=s(\"2655\"),d=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{staticStyle:{width:\"100%\"},attrs:{id:\"ptzPreset\"}},[e._l(e.presetList,(function(t){return s(\"el-tag\",{key:t.presetId,staticStyle:{\"margin-right\":\"1rem\",cursor:\"pointer\",\"margin-bottom\":\"0.6rem\"},attrs:{closable:\"\",size:\"mini\"},on:{close:function(s){return e.delPreset(t)},click:function(s){return e.gotoPreset(t)}}},[e._v(\" \"+e._s(t.presetName?t.presetName:t.presetId)+\" \")])})),e.inputVisible?s(\"el-input\",{ref:\"saveTagInput\",staticStyle:{width:\"300px\",\"vertical-align\":\"bottom\"},attrs:{min:\"1\",max:\"255\",placeholder:\"预置位编号\",\"addon-before\":\"预置位编号\",\"addon-after\":\"(1-255)\",size:\"small\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[s(\"el-button\",{on:{click:function(t){return e.addPreset()}}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:function(t){return e.cancel()}}},[e._v(\"取消\")])]},proxy:!0}],null,!1,3889715118),model:{value:e.ptzPresetId,callback:function(t){e.ptzPresetId=t},expression:\"ptzPresetId\"}}):s(\"el-button\",{attrs:{size:\"small\"},on:{click:e.showInput}},[e._v(\"+ 添加\")])],2)},u=[],m=(s(\"d3b7\"),{name:\"PtzPreset\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{presetList:[],inputVisible:!1,ptzPresetId:\"\"}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch(\"frontEnd/queryPreset\",[this.deviceId,this.channelDeviceId]).then((function(t){e.presetList=t,e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))})).catch((function(e){console.log(e)}))},showInput:function(){var e=this;this.inputVisible=!0,this.$nextTick((function(t){e.$refs.saveTagInput.$refs.input.focus()}))},addPreset:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/addPreset\",[this.deviceId,this.channelDeviceId,this.ptzPresetId]).then((function(t){setTimeout((function(){e.inputVisible=!1,e.ptzPresetId=\"\",e.getPresetList()}),1e3)})).catch((function(s){t.close(),e.inputVisible=!1,e.ptzPresetId=\"\",e.$message({showClose:!0,message:s,type:\"error\"})})).finally((function(){t.close()}))},cancel:function(){this.inputVisible=!1,this.ptzPresetId=\"\"},gotoPreset:function(e){var t=this;console.log(e),this.$store.dispatch(\"frontEnd/callPreset\",[this.deviceId,this.channelDeviceId,e.presetId]).then((function(e){t.$message({showClose:!0,message:\"调用成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})}))},delPreset:function(e){var t=this;this.$confirm(\"确定删除此预置位\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){var s=t.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});t.$store.dispatch(\"frontEnd/deletePreset\",[t.deviceId,t.channelDeviceId,e.presetId]).then((function(e){setTimeout((function(){t.getPresetList()}),1e3)})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){s.close()}))})).catch((function(){}))}}}),p=m,f=s(\"2877\"),h=Object(f[\"a\"])(p,d,u,!1,null,null,null),b=h.exports,v=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"ptzCruising\"}},[s(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"80px auto\",\"line-height\":\"28px\"}},[s(\"span\",[e._v(\"巡航组号: \")]),s(\"el-input\",{attrs:{min:\"1\",max:\"255\",placeholder:\"巡航组号\",\"addon-before\":\"巡航组号\",\"addon-after\":\"(1-255)\",size:\"mini\"},model:{value:e.cruiseId,callback:function(t){e.cruiseId=t},expression:\"cruiseId\"}})],1),s(\"p\",e._l(e.presetList,(function(t,o){return s(\"el-tag\",{key:t.presetId,staticStyle:{\"margin-right\":\"1rem\",cursor:\"pointer\"},attrs:{closable:\"\"},on:{close:function(s){return e.delPreset(t,o)}}},[e._v(\" \"+e._s(t.presetName?t.presetName:t.presetId)+\" \")])})),1),e.selectPresetVisible?s(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[s(\"el-form-item\",[s(\"el-select\",{attrs:{placeholder:\"请选择预置点\"},model:{value:e.selectPreset,callback:function(t){e.selectPreset=t},expression:\"selectPreset\"}},e._l(e.allPresetList,(function(e){return s(\"el-option\",{key:e.presetId,attrs:{label:e.presetName,value:e}})})),1)],1),s(\"el-form-item\",[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.addCruisePoint}},[e._v(\"保存\")]),s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.cancelAddCruisePoint}},[e._v(\"取消\")])],1)],1):s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.selectPresetVisible=!0}}},[e._v(\"添加巡航点\")]),e.setSpeedVisible?s(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[s(\"el-form-item\",[e.setSpeedVisible?s(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航速度\",\"addon-before\":\"巡航速度\",\"addon-after\":\"(1-4095)\",size:\"mini\"},model:{value:e.cruiseSpeed,callback:function(t){e.cruiseSpeed=t},expression:\"cruiseSpeed\"}}):e._e()],1),s(\"el-form-item\",[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setCruiseSpeed}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:e.cancelSetCruiseSpeed}},[e._v(\"取消\")])],1)],1):s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v(\"设置巡航速度\")]),e.setTimeVisible?s(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[s(\"el-form-item\",[s(\"el-input\",{staticStyle:{width:\"100%\"},attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航停留时间(秒)\",\"addon-before\":\"巡航停留时间(秒)\",\"addon-after\":\"(1-4095)\"},model:{value:e.cruiseTime,callback:function(t){e.cruiseTime=t},expression:\"cruiseTime\"}})],1),s(\"el-form-item\",[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setCruiseTime}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:e.cancelSetCruiseTime}},[e._v(\"取消\")])],1)],1):s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setTimeVisible=!0}}},[e._v(\"设置巡航时间\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.startCruise}},[e._v(\"开始巡航\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.stopCruise}},[e._v(\"停止巡航\")]),s(\"el-button\",{attrs:{size:\"mini\",type:\"danger\"},on:{click:e.deleteCruise}},[e._v(\"删除巡航\")])],1)},g=[],y=(s(\"a434\"),{name:\"PtzCruising\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{cruiseId:1,presetList:[],allPresetList:[],selectPreset:\"\",inputVisible:!1,selectPresetVisible:!1,setSpeedVisible:!1,setTimeVisible:!1,cruiseSpeed:\"\",cruiseTime:\"\"}},created:function(){this.getPresetList()},methods:{getPresetList:function(){var e=this;this.$store.dispatch(\"frontEnd/queryPreset\",[this.deviceId,this.channelDeviceId]).then((function(t){e.allPresetList=t}))},addCruisePoint:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/addPointForCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.selectPreset.presetId]).then((function(t){e.presetList.push(e.selectPreset)})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.selectPreset=\"\",e.selectPresetVisible=!1,t.close()}))},cancelAddCruisePoint:function(){this.selectPreset=\"\",this.selectPresetVisible=!1},delPreset:function(e,t){var s=this,o=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/deletePointForCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId,e.presetId]).then((function(e){s.presetList.splice(t,1)})).catch((function(e){s.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){o.close()}))},deleteCruise:function(e,t){var s=this;this.$confirm(\"确定删除此巡航组\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){var e=s.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});s.$store.dispatch(\"frontEnd/deletePointForCruise\",[s.deviceId,s.channelDeviceId,s.cruiseId,0]).then((function(e){s.presetList=[]})).catch((function(e){s.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){e.close()}))}))},setCruiseSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setCruiseSpeed\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseSpeed]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.cruiseSpeed=\"\",e.setSpeedVisible=!1,t.close()}))},cancelSetCruiseSpeed:function(){this.cruiseSpeed=\"\",this.setSpeedVisible=!1},setCruiseTime:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setCruiseTime\",[this.deviceId,this.channelDeviceId,this.cruiseId,this.cruiseTime]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))},cancelSetCruiseTime:function(){this.setTimeVisible=!1,this.cruiseTime=\"\"},startCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/startCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))},stopCruise:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/stopCruise\",[this.deviceId,this.channelDeviceId,this.cruiseId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.setTimeVisible=!1,e.cruiseTime=\"\",t.close()}))}}}),S=y,_=(s(\"e8b4\"),Object(f[\"a\"])(S,v,g,!1,null,null,null)),w=_.exports,I=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"ptzScan\"}},[s(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"80px auto\",\"line-height\":\"28px\"}},[s(\"span\",[e._v(\"扫描组号: \")]),s(\"el-input\",{attrs:{min:\"1\",max:\"255\",placeholder:\"扫描组号\",\"addon-before\":\"扫描组号\",\"addon-after\":\"(1-255)\",size:\"mini\"},model:{value:e.scanId,callback:function(t){e.scanId=t},expression:\"scanId\"}})],1),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.setScanLeft}},[e._v(\"设置左边界\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.setScanRight}},[e._v(\"设置右边界\")]),e.setSpeedVisible?s(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[s(\"el-form-item\",[e.setSpeedVisible?s(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"巡航速度\",\"addon-before\":\"巡航速度\",\"addon-after\":\"(1-4095)\",size:\"mini\"},model:{value:e.speed,callback:function(t){e.speed=t},expression:\"speed\"}}):e._e()],1),s(\"el-form-item\",[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.setSpeed}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:e.cancelSetSpeed}},[e._v(\"取消\")])],1)],1):s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){e.setSpeedVisible=!0}}},[e._v(\"设置扫描速度\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.startScan}},[e._v(\"开始自动扫描\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:e.stopScan}},[e._v(\"停止自动扫描\")])],1)},C=[],x={name:\"PtzScan\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{scanId:1,setSpeedVisible:!1,speed:\"\"}},created:function(){},methods:{setSpeed:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setSpeedForScan\",[this.deviceId,this.channelDeviceId,this.scanId,this.speed]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},cancelSetSpeed:function(){this.speed=\"\",this.setSpeedVisible=!1},setScanLeft:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setLeftForScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},setScanRight:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/setRightForScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){e.speed=\"\",e.setSpeedVisible=!1,t.close()}))},startScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/startScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){t.close()}))},stopScan:function(){var e=this,t=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/stopScan\",[this.deviceId,this.channelDeviceId,this.scanId]).then((function(t){e.$message({showClose:!0,message:\"发送成功\",type:\"success\"})})).catch((function(t){e.$message({showClose:!0,message:t,type:\"error\"})})).finally((function(){t.close()}))}}},P=x,k=(s(\"fdc8\"),Object(f[\"a\"])(P,I,C,!1,null,null,null)),$=k.exports,T=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"ptzWiper\"}},[s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"on\")}}},[e._v(\"开启\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"off\")}}},[e._v(\"关闭\")])],1)},z=[],E={name:\"PtzWiper\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{}},created:function(){},methods:{open:function(e){var t=this,s=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/wiper\",[this.deviceId,this.channelDeviceId,e]).then((function(e){t.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){s.close()}))}}},R=E,D=(s(\"a135\"),Object(f[\"a\"])(R,T,z,!1,null,null,null)),L=D.exports,M=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"ptzScan\"}},[s(\"el-form\",{attrs:{size:\"mini\",inline:!0}},[s(\"el-form-item\",[s(\"el-input\",{attrs:{min:\"1\",max:\"4095\",placeholder:\"开关编号\",\"addon-before\":\"开关编号\",\"addon-after\":\"(2-255)\",size:\"mini\"},model:{value:e.switchId,callback:function(t){e.switchId=t},expression:\"switchId\"}})],1),s(\"el-form-item\",[s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"on\")}}},[e._v(\"开启\")]),s(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(t){return e.open(\"off\")}}},[e._v(\"关闭\")])],1)],1)],1)},F=[],N={name:\"PtzScan\",components:{},props:[\"channelDeviceId\",\"deviceId\"],data:function(){return{switchId:1}},created:function(){},methods:{open:function(e){var t=this,s=this.$loading({lock:!0,fullscreen:!0,text:\"正在发送指令\",spinner:\"el-icon-loading\",background:\"rgba(0, 0, 0, 0.7)\"});this.$store.dispatch(\"frontEnd/auxiliary\",[this.deviceId,this.channelDeviceId,e,this.switchId]).then((function(e){t.$message({showClose:!0,message:\"保存成功\",type:\"success\"})})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"})})).finally((function(){s.close()}))}}},U=N,A=(s(\"34c9\"),Object(f[\"a\"])(U,M,F,!1,null,null,null)),B=A.exports,O=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"mediaInfo\"}},[s(\"el-button\",{staticStyle:{position:\"absolute\",right:\"1rem\"},attrs:{icon:\"el-icon-refresh-right\",circle:\"\",size:\"mini\"},on:{click:e.getMediaInfo}}),s(\"el-descriptions\",{attrs:{size:\"mini\",column:3,title:\"概况\"}},[s(\"el-descriptions-item\",{attrs:{label:\"观看人数\"}},[e._v(e._s(e.info.readerCount))]),s(\"el-descriptions-item\",{attrs:{label:\"网络\"}},[e._v(e._s(e.formatByteSpeed()))]),s(\"el-descriptions-item\",{attrs:{label:\"持续时间\"}},[e._v(e._s(e.info.aliveSecond)+\"秒\")])],1),s(\"div\",{staticStyle:{display:\"grid\",\"grid-template-columns\":\"1fr 1fr\"}},[e.info.videoCodec?s(\"el-descriptions\",{attrs:{size:\"mini\",column:2,title:\"视频信息\"}},[s(\"el-descriptions-item\",{attrs:{label:\"编码\"}},[e._v(e._s(e.info.videoCodec))]),s(\"el-descriptions-item\",{attrs:{label:\"分辨率\"}},[e._v(e._s(e.info.width)+\"x\"+e._s(e.info.height)+\" \")]),s(\"el-descriptions-item\",{attrs:{label:\"FPS\"}},[e._v(e._s(e.info.fps))]),s(\"el-descriptions-item\",{attrs:{label:\"丢包率\"}},[e._v(e._s(e.info.loss))])],1):e._e(),e.info.audioCodec?s(\"el-descriptions\",{attrs:{size:\"mini\",column:2,title:\"音频信息\"}},[s(\"el-descriptions-item\",{attrs:{label:\"编码\"}},[e._v(\" \"+e._s(e.info.audioCodec)+\" \")]),s(\"el-descriptions-item\",{attrs:{label:\"采样率\"}},[e._v(e._s(e.info.audioSampleRate))])],1):e._e()],1)],1)},V=[],j=(s(\"99af\"),s(\"b680\"),{name:\"MediaInfo\",components:{},props:[\"app\",\"stream\",\"mediaServerId\"],data:function(){return{info:{},task:null}},created:function(){this.getMediaInfo()},methods:{getMediaInfo:function(){var e=this;this.$store.dispatch(\"server/getMediaInfo\",{app:this.app,stream:this.stream,mediaServerId:this.mediaServerId}).then((function(t){e.info=t}))},startTask:function(){this.task=setInterval(this.getMediaInfo,1e3)},stopTask:function(){this.task&&(window.clearInterval(this.task),this.task=null)},formatByteSpeed:function(){var e=this.info.bytesSpeed,t=1024;return e<t?e+\" B/S\":e<Math.pow(t,2)?(e/t).toFixed(2)+\" KB/S\":e<Math.pow(t,3)?(e/Math.pow(t,2)).toFixed(2)+\" MB/S\":e<Math.pow(t,4)?(e/Math.pow(t,3)).toFixed(2)+\" G/S\":(e/Math.pow(t,4)).toFixed(2)+\" T/S\"},formatAliveSecond:function(){var e=this.info.aliveSecond,t=parseInt(e.value/3600),s=parseInt(e.value/60%60),o=Math.ceil(e.value%60),a=t<10?\"0\"+t:t,i=o>59?59:o;return\"\".concat(a>0?\"\".concat(a,\"小时\"):\"\").concat(s<10?\"0\"+s:s,\"分\").concat(i<10?\"0\"+i:i,\"秒\")}}}),W=j,G=(s(\"5869\"),Object(f[\"a\"])(W,O,V,!1,null,null,null)),H=G.exports,q=s(\"4f91\"),J={name:\"DevicePlayer\",directives:{elDragDialog:i[\"a\"]},components:{H265web:q[\"a\"],PtzPreset:b,PtzCruising:w,ptzScan:$,ptzWiper:L,ptzSwitch:B,mediaInfo:H,jessibucaPlayer:c[\"default\"],rtcPlayer:l[\"default\"]},props:{},data:function(){return{video:\"http://lndxyj.iqilu.com/public/upload/2019/10/14/8c001ea0c09cdc59a57829dabc8010fa.mp4\",videoUrl:\"\",activePlayer:\"jessibuca\",player:{jessibuca:[\"ws_flv\",\"wss_flv\"],webRTC:[\"rtc\",\"rtcs\"],h265web:[\"ws_flv\",\"wss_flv\"]},showVideoDialog:!1,streamId:\"\",ptzMethod:\"preset\",ptzPresetId:\"\",app:\"\",mediaServerId:\"\",deviceId:\"\",channelId:\"\",tabActiveName:\"media\",hasAudio:!1,loadingRecords:!1,recordsLoading:!1,isLoging:!1,controSpeed:30,timeVal:0,timeMin:0,timeMax:1440,presetPos:1,cruisingSpeed:100,cruisingTime:5,cruisingGroup:0,scanSpeed:100,scanGroup:0,tracks:[],showPtz:!0,showBroadcast:!0,showRrecord:!0,sliderTime:0,seekTime:0,recordStartTime:0,showTimeText:\"00:00:00\",streamInfo:null,broadcastMode:!0,broadcastRtc:null,broadcastStatus:-1}},computed:{getPlayerShared:function(){return{sharedUrl:window.location.origin+\"/#/play/wasm/\"+encodeURIComponent(this.videoUrl),sharedIframe:'<iframe src=\"'+window.location.origin+\"/#/play/wasm/\"+encodeURIComponent(this.videoUrl)+'\"></iframe>',sharedRtmp:this.videoUrl}}},created:function(){console.log(\"created\"),console.log(this.player),this.broadcastStatus=-1,1===Object.keys(this.player).length&&(this.activePlayer=Object.keys(this.player)[0])},methods:{tabHandleClick:function(e,t){console.log(e),this.tracks=[],\"codec\"===e.name?this.$refs.mediaInfo.startTask():this.$refs.mediaInfo.stopTask()},changePlayer:function(e){console.log(this.player[e.name][0]),this.activePlayer=e.name,this.videoUrl=this.getUrlByStreamInfo(),console.log(this.videoUrl)},openDialog:function(e,t,s,o){if(!this.showVideoDialog)switch(this.tabActiveName=e,this.channelId=s,this.deviceId=t,this.streamId=\"\",this.mediaServerId=\"\",this.app=\"\",this.videoUrl=\"\",this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),e){case\"media\":this.play(o.streamInfo,o.hasAudio);break;case\"streamPlay\":this.tabActiveName=\"media\",this.showRrecord=!1,this.showPtz=!1,this.showBroadcast=!1,this.play(o.streamInfo,o.hasAudio);break;case\"control\":break}},play:function(e,t){this.streamInfo=e,this.hasAudio=t,this.isLoging=!1,this.videoUrl=this.getUrlByStreamInfo(),this.streamId=e.stream,this.app=e.app,this.mediaServerId=e.mediaServerId,this.playFromStreamInfo(!1,e)},getUrlByStreamInfo:function(){console.log(this.streamInfo);var e=this.streamInfo;return this.streamInfo.transcodeStream&&(e=this.streamInfo.transcodeStream),\"https:\"===location.protocol?this.videoUrl=e[this.player[this.activePlayer][1]]:this.videoUrl=e[this.player[this.activePlayer][0]],this.videoUrl},playFromStreamInfo:function(e,t){var s=this;this.showVideoDialog=!0,this.hasaudio=e&&this.hasaudio,this.$refs[this.activePlayer]?this.$refs[this.activePlayer].play(this.getUrlByStreamInfo(t)):this.$nextTick((function(){s.$refs[s.activePlayer].play(s.getUrlByStreamInfo(t))}))},close:function(){console.log(\"关闭视频\"),this.$refs[this.activePlayer]&&this.$refs[this.activePlayer].pause(),this.videoUrl=\"\",this.showVideoDialog=!1,this.stopBroadcast()},ptzCamera:function(e){console.log(\"云台控制：\"+e),this.$store.dispatch(\"frontEnd/ptz\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100),parseInt(255*this.controSpeed/100),parseInt(16*this.controSpeed/100)])},irisCamera:function(e){this.$store.dispatch(\"frontEnd/iris\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},focusCamera:function(e){this.$store.dispatch(\"frontEnd/focus\",[this.deviceId,this.channelId,e,parseInt(255*this.controSpeed/100)])},videoError:function(e){console.log(\"播放器错误：\"+JSON.stringify(e))},copyUrl:function(e){var t=this;console.log(e),this.$copyText(e).then((function(e){t.$message.success({showClose:!0,message:\"成功拷贝到粘贴板\"})}),(function(e){}))},getBroadcastStatus:function(){return-2==this.broadcastStatus||-1==this.broadcastStatus?\"primary\":0==this.broadcastStatus?\"warning\":1===this.broadcastStatus?\"danger\":void 0},broadcastStatusClick:function(){var e=this;-1===this.broadcastStatus?(this.broadcastStatus=0,this.$store.dispatch(\"play/broadcastStart\",[this.deviceId,this.channelId,this.broadcastMode]).then((function(t){var s=t.streamInfo;document.location.protocol.includes(\"https\")?e.startBroadcast(s.rtcs):e.startBroadcast(s.rtc)}))):1===this.broadcastStatus&&(this.broadcastStatus=-1,this.broadcastRtc.close())},startBroadcast:function(e){var t=this;this.$store.dispatch(\"user/getUserInfo\").then((function(s){if(null!=s){var o=s.pushKey;e+=\"&sign=\"+r.a.createHash(\"md5\").update(o,\"utf8\").digest(\"hex\"),console.log(\"开始语音喊话： \"+e),t.broadcastRtc=new ZLMRTCClient.Endpoint({debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!1,recvOnly:!1}),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT,(function(e){console.error(\"不支持webrtc\",e),t.$message({showClose:!0,message:\"不支持webrtc, 无法进行语音喊话\",type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error(\"ICE 协商出错\"),t.$message({showClose:!0,message:\"ICE 协商出错\",type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(e){console.error(\"offer anwser 交换失败\",e),t.$message({showClose:!0,message:\"offer anwser 交换失败\"+e,type:\"error\"}),t.broadcastStatus=-1})),t.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,(function(e){console.log(\"状态改变\",e),\"connecting\"===e?t.broadcastStatus=0:\"connected\"===e?t.broadcastStatus=1:\"disconnected\"===e&&(t.broadcastStatus=-1)})),t.broadcastRtc.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED,(function(e){console.log(\"捕获流失败\",e),t.$message({showClose:!0,message:\"捕获流失败\"+e,type:\"error\"}),t.broadcastStatus=-1}))}else t.broadcastStatus=-1})).catch((function(e){t.$message({showClose:!0,message:e,type:\"error\"}),t.broadcastStatus=-1}))},stopBroadcast:function(){this.broadcastRtc.close(),this.broadcastStatus=-1,this.$store.dispatch(\"play/broadcastStop\",[this.deviceId,this.channelId])}}},K=J,Z=(s(\"c5f0\"),Object(f[\"a\"])(K,o,a,!1,null,null,null));t[\"a\"]=Z.exports},\"0868\":function(e,t,s){},1148:function(e,t,s){\"use strict\";var o=s(\"a691\"),a=s(\"1d80\");e.exports=\"\".repeat||function(e){var t=String(a(this)),s=\"\",i=o(e);if(i<0||i==1/0)throw RangeError(\"Wrong number of repetitions\");for(;i>0;(i>>>=1)&&(t+=t))1&i&&(s+=t);return s}},\"257e\":function(e,t,s){\"use strict\";s(\"d017\")},2655:function(e,t,s){\"use strict\";s.r(t);var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{ref:\"container\",staticStyle:{width:\"100%\",height:\"100%\",\"background-color\":\"#000000\",margin:\"0 auto\",position:\"relative\"},on:{dblclick:e.fullscreenSwich}},[s(\"div\",{staticStyle:{width:\"100%\",\"padding-top\":\"56.25%\",position:\"relative\"}}),s(\"div\",{staticClass:\"buttons-box\",attrs:{id:\"buttonsBox\"}},[s(\"div\",{staticClass:\"buttons-box-left\"},[e.playing?e._e():s(\"i\",{staticClass:\"iconfont icon-play jessibuca-btn\",on:{click:e.playBtnClick}}),e.playing?s(\"i\",{staticClass:\"iconfont icon-pause jessibuca-btn\",on:{click:e.pause}}):e._e(),s(\"i\",{staticClass:\"iconfont icon-stop jessibuca-btn\",on:{click:e.destroy}}),e.isNotMute?s(\"i\",{staticClass:\"iconfont icon-audio-high jessibuca-btn\",on:{click:function(t){return e.mute()}}}):e._e(),e.isNotMute?e._e():s(\"i\",{staticClass:\"iconfont icon-audio-mute jessibuca-btn\",on:{click:function(t){return e.cancelMute()}}})]),s(\"div\",{staticClass:\"buttons-box-right\"},[s(\"span\",{staticClass:\"jessibuca-btn\"},[e._v(e._s(e.kBps)+\" kb/s\")]),s(\"i\",{staticClass:\"iconfont icon-camera1196054easyiconnet jessibuca-btn\",staticStyle:{\"font-size\":\"1rem !important\"},on:{click:e.screenshot}}),s(\"i\",{staticClass:\"iconfont icon-shuaxin11 jessibuca-btn\",on:{click:e.playBtnClick}}),e.fullscreen?e._e():s(\"i\",{staticClass:\"iconfont icon-weibiaoti10 jessibuca-btn\",on:{click:e.fullscreenSwich}}),e.fullscreen?s(\"i\",{staticClass:\"iconfont icon-weibiaoti11 jessibuca-btn\",on:{click:e.fullscreenSwich}}):e._e()])])])},a=[],i=s(\"5530\"),n={},r={name:\"Jessibuca\",props:[\"videoUrl\",\"error\",\"hasAudio\",\"height\"],data:function(){return{playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:\"\",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!1}},watch:{videoUrl:{handler:function(e,t){var s=this;this.$nextTick((function(){s.play(e)}))},immediate:!0}},created:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){e.updatePlayerDomSize(),window.onresize=e.updatePlayerDomSize,\"undefined\"===typeof e.videoUrl&&(e.videoUrl=t),e.btnDom=document.getElementById(\"buttonsBox\")}))},mounted:function(){this.updatePlayerDomSize()},destroyed:function(){n[this._uid]&&n[this._uid].destroy(),this.playing=!1,this.loaded=!1,this.performance=\"\"},methods:{updatePlayerDomSize:function(){var e=this,t=this.$refs.container;this.parentNodeResizeObserver||(this.parentNodeResizeObserver=new ResizeObserver((function(t){e.updatePlayerDomSize()})),this.parentNodeResizeObserver.observe(t.parentNode));var s=t.parentNode.clientWidth,o=t.parentNode.clientHeight,a=s,i=9/16*a;o>0&&s>o/9*16&&(i=o,a=o/9*16);var r=Math.min(document.body.clientHeight,document.documentElement.clientHeight);i>r&&(i=r,a=16/9*i),this.playerWidth=a,this.playerHeight=i,this.playing&&n[this._uid].resize(this.playerWidth,this.playerHeight)},create:function(){var e={container:this.$refs.container,autoWasm:!0,background:\"\",controlAutoHide:!1,debug:!1,decoder:\"static/js/jessibuca/decoder.js\",forceNoOffscreen:!1,hasAudio:\"undefined\"===typeof this.hasAudio||this.hasAudio,heartTimeout:5,heartTimeoutReplay:!0,heartTimeoutReplayTimes:3,hiddenAutoPause:!1,hotKey:!0,isFlv:!1,isFullResize:!1,isNotMute:this.isNotMute,isResize:!0,keepScreenOn:!0,loadingText:\"请稍等, 视频加载中......\",loadingTimeout:10,loadingTimeoutReplay:!0,loadingTimeoutReplayTimes:3,openWebglAlignment:!1,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},recordType:\"mp4\",rotate:0,showBandwidth:!1,supportDblclickFullscreen:!1,timeout:10,useMSE:!0,useWCS:!1,useWebFullScreen:!0,videoBuffer:.1,wasmDecodeErrorReplay:!0,wcsUseVideoRender:!0};console.log(\"Jessibuca -> options: \",e),n[this._uid]=new window.Jessibuca(Object(i[\"a\"])({},e));var t=n[this._uid],s=this;t.on(\"pause\",(function(){s.playing=!1})),t.on(\"play\",(function(){s.playing=!0})),t.on(\"fullscreen\",(function(e){s.fullscreen=e})),t.on(\"mute\",(function(e){s.isNotMute=!e})),t.on(\"performance\",(function(e){var t=\"卡顿\";2===e?t=\"非常流畅\":1===e&&(t=\"流畅\"),s.performance=t})),t.on(\"kBps\",(function(e){s.kBps=Math.round(e)})),t.on(\"videoInfo\",(function(e){console.log(\"Jessibuca -> videoInfo: \",e)})),t.on(\"audioInfo\",(function(e){console.log(\"Jessibuca -> audioInfo: \",e)})),t.on(\"error\",(function(e){console.log(\"Jessibuca -> error: \",e)})),t.on(\"timeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"loadingTimeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"delayTimeout\",(function(e){console.log(\"Jessibuca -> timeout: \",e)})),t.on(\"playToRenderTimes\",(function(e){console.log(\"Jessibuca -> playToRenderTimes: \",e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(\"Jessibuca -> url: \",e),n[this._uid]&&this.destroy(),this.create(),n[this._uid].on(\"play\",(function(){t.playing=!0,t.loaded=!0,t.quieting=jessibuca.quieting})),n[this._uid].hasLoaded()?n[this._uid].play(e):n[this._uid].on(\"load\",(function(){n[t._uid].play(e)}))},pause:function(){n[this._uid]&&n[this._uid].pause(),this.playing=!1,this.err=\"\",this.performance=\"\"},screenshot:function(){n[this._uid]&&n[this._uid].screenshot()},mute:function(){n[this._uid]&&n[this._uid].mute()},cancelMute:function(){n[this._uid]&&n[this._uid].cancelMute()},destroy:function(){n[this._uid]&&n[this._uid].destroy(),null==document.getElementById(\"buttonsBox\")&&this.$refs.container.appendChild(this.btnDom),n[this._uid]=null,this.playing=!1,this.err=\"\",this.performance=\"\"},fullscreenSwich:function(){var e=this.isFullscreen();n[this._uid].setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}}},l=r,c=(s(\"989a\"),s(\"2877\")),d=Object(c[\"a\"])(l,o,a,!1,null,null,null);t[\"default\"]=d.exports},\"28b5\":function(e,t,s){},\"2b7e\":function(e,t,s){},\"34c9\":function(e,t,s){\"use strict\";s(\"2b7e\")},\"408a\":function(e,t,s){var o=s(\"c6b6\");e.exports=function(e){if(\"number\"!=typeof e&&\"Number\"!=o(e))throw TypeError(\"Incorrect invocation\");return+e}},\"41f4\":function(e,t,s){\"use strict\";s.r(t);var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{staticClass:\"app-container\",attrs:{id:\"streamProxyList\"}},[e.streamProxy?e._e():s(\"div\",{staticStyle:{height:\"calc(100vh - 124px)\"}},[s(\"el-form\",{attrs:{inline:!0,size:\"mini\"}},[s(\"el-form-item\",{attrs:{label:\"搜索\"}},[s(\"el-input\",{staticStyle:{\"margin-right\":\"1rem\",width:\"auto\"},attrs:{placeholder:\"关键字\",\"prefix-icon\":\"el-icon-search\",clearable:\"\"},on:{input:e.getStreamProxyList},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:\"searchSrt\"}})],1),s(\"el-form-item\",{attrs:{label:\"流媒体\"}},[s(\"el-select\",{staticStyle:{\"margin-right\":\"1rem\"},attrs:{placeholder:\"请选择\",\"default-first-option\":\"\"},on:{change:e.getStreamProxyList},model:{value:e.mediaServerId,callback:function(t){e.mediaServerId=t},expression:\"mediaServerId\"}},[s(\"el-option\",{attrs:{label:\"全部\",value:\"\"}}),e._l(e.mediaServerList,(function(e){return s(\"el-option\",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),s(\"el-form-item\",{attrs:{label:\"拉流状态\"}},[s(\"el-select\",{staticStyle:{\"margin-right\":\"1rem\"},attrs:{placeholder:\"请选择\",\"default-first-option\":\"\"},on:{change:e.getStreamProxyList},model:{value:e.pulling,callback:function(t){e.pulling=t},expression:\"pulling\"}},[s(\"el-option\",{attrs:{label:\"全部\",value:\"\"}}),s(\"el-option\",{attrs:{label:\"正在拉流\",value:\"true\"}}),s(\"el-option\",{attrs:{label:\"尚未拉流\",value:\"false\"}})],1)],1),s(\"el-form-item\",[s(\"el-button\",{staticStyle:{\"margin-right\":\"1rem\"},attrs:{icon:\"el-icon-plus\",size:\"mini\",type:\"primary\"},on:{click:e.addStreamProxy}},[e._v(\"添加代理\")])],1),s(\"el-form-item\",{staticStyle:{float:\"right\"}},[s(\"el-button\",{attrs:{icon:\"el-icon-refresh-right\",circle:\"\"},on:{click:function(t){return e.refresh()}}})],1)],1),s(\"devicePlayer\",{ref:\"devicePlayer\"}),s(\"el-table\",{staticStyle:{width:\"100%\"},attrs:{size:\"small\",data:e.streamProxyList,height:\"calc(100% - 64px)\"}},[s(\"el-table-column\",{attrs:{prop:\"app\",label:\"流应用名\",\"min-width\":\"120\",\"show-overflow-tooltip\":\"\"}}),s(\"el-table-column\",{attrs:{prop:\"stream\",label:\"流ID\",\"min-width\":\"120\",\"show-overflow-tooltip\":\"\"}}),s(\"el-table-column\",{attrs:{label:\"流地址\",\"min-width\":\"250\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[s(\"div\",{staticClass:\"name-wrapper\",attrs:{slot:\"reference\"},slot:\"reference\"},[s(\"el-tag\",{directives:[{name:\"clipboard\",rawName:\"v-clipboard\",value:t.row.srcUrl,expression:\"scope.row.srcUrl\"}],attrs:{size:\"medium\"},on:{success:function(t){return e.$message({type:\"success\",message:\"成功拷贝到粘贴板\"})}}},[s(\"i\",{staticClass:\"el-icon-document-copy\",attrs:{title:\"点击拷贝\"}}),e._v(\" \"+e._s(t.row.srcUrl)+\" \")])],1)]}}],null,!1,2404782382)}),s(\"el-table-column\",{attrs:{prop:\"mediaServerId\",label:\"流媒体\",\"min-width\":\"180\"}}),s(\"el-table-column\",{attrs:{label:\"代理方式\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[s(\"div\",{staticClass:\"name-wrapper\",attrs:{slot:\"reference\"},slot:\"reference\"},[e._v(\" \"+e._s(\"default\"===t.row.type?\"默认\":\"FFMPEG代理\")+\" \")])]}}],null,!1,3096416671)}),s(\"el-table-column\",{attrs:{prop:\"gbDeviceId\",label:\"国标编码\",\"min-width\":\"180\",\"show-overflow-tooltip\":\"\"}}),s(\"el-table-column\",{attrs:{label:\"拉流状态\",\"min-width\":\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[s(\"div\",{staticClass:\"name-wrapper\",attrs:{slot:\"reference\"},slot:\"reference\"},[t.row.pulling&&e.myServerId!==t.row.serverId?s(\"el-tag\",{staticStyle:{\"border-color\":\"#ecf1af\"},attrs:{size:\"medium\"}},[e._v(\"正在拉流\")]):e._e(),t.row.pulling&&e.myServerId===t.row.serverId?s(\"el-tag\",{attrs:{size:\"medium\"}},[e._v(\"正在拉流\")]):e._e(),t.row.pulling?e._e():s(\"el-tag\",{attrs:{size:\"medium\",type:\"info\"}},[e._v(\"尚未拉流\")])],1)]}}],null,!1,819116356)}),s(\"el-table-column\",{attrs:{label:\"启用\",\"min-width\":\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[s(\"div\",{staticClass:\"name-wrapper\",attrs:{slot:\"reference\"},slot:\"reference\"},[t.row.enable&&e.myServerId!==t.row.serverId?s(\"el-tag\",{staticStyle:{\"border-color\":\"#ecf1af\"},attrs:{size:\"medium\"}},[e._v(\"已启用\")]):e._e(),t.row.enable&&e.myServerId===t.row.serverId?s(\"el-tag\",{attrs:{size:\"medium\"}},[e._v(\"已启用\")]):e._e(),t.row.enable?e._e():s(\"el-tag\",{attrs:{size:\"medium\",type:\"info\"}},[e._v(\"未启用\")])],1)]}}],null,!1,3937613589)}),s(\"el-table-column\",{attrs:{prop:\"createTime\",label:\"创建时间\",\"min-width\":\"150\",\"show-overflow-tooltip\":\"\"}}),s(\"el-table-column\",{attrs:{label:\"操作\",width:\"370\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[s(\"el-button\",{attrs:{size:\"medium\",loading:t.row.playLoading,icon:\"el-icon-video-play\",type:\"text\"},on:{click:function(s){return e.play(t.row)}}},[e._v(\"播放\")]),s(\"el-divider\",{attrs:{direction:\"vertical\"}}),t.row.pulling?s(\"el-button\",{staticStyle:{color:\"#f56c6c\"},attrs:{size:\"medium\",icon:\"el-icon-switch-button\",type:\"text\"},on:{click:function(s){return e.stopPlay(t.row)}}},[e._v(\"停止\")]):e._e(),t.row.pulling?s(\"el-divider\",{attrs:{direction:\"vertical\"}}):e._e(),s(\"el-button\",{attrs:{size:\"medium\",icon:\"el-icon-edit\",type:\"text\"},on:{click:function(s){return e.edit(t.row)}}},[e._v(\" 编辑 \")]),s(\"el-divider\",{attrs:{direction:\"vertical\"}}),s(\"el-button\",{attrs:{size:\"medium\",icon:\"el-icon-cloudy\",type:\"text\"},on:{click:function(s){return e.queryCloudRecords(t.row)}}},[e._v(\"云端录像\")]),s(\"el-divider\",{attrs:{direction:\"vertical\"}}),s(\"el-button\",{staticStyle:{color:\"#f56c6c\"},attrs:{size:\"medium\",icon:\"el-icon-delete\",type:\"text\"},on:{click:function(s){return e.deleteStreamProxy(t.row)}}},[e._v(\"删除\")])]}}],null,!1,317584972)})],1),s(\"el-pagination\",{staticStyle:{\"text-align\":\"right\"},attrs:{\"current-page\":e.currentPage,\"page-size\":e.count,\"page-sizes\":[15,25,35,50],layout:\"total, sizes, prev, pager, next\",total:e.total},on:{\"size-change\":e.handleSizeChange,\"current-change\":e.currentChange}})],1),e.streamProxy?s(\"StreamProxyEdit\",{attrs:{\"close-edit\":e.closeEdit},model:{value:e.streamProxy,callback:function(t){e.streamProxy=t},expression:\"streamProxy\"}}):e._e()],1)},a=[],i=(s(\"99af\"),s(\"d3b7\"),s(\"0328\")),n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{staticStyle:{width:\"100%\"},attrs:{id:\"StreamProxyEdit\"}},[s(\"div\",{staticClass:\"page-header\"},[s(\"div\",{staticClass:\"page-title\"},[s(\"el-page-header\",{attrs:{content:\"编辑拉流代理信息\"},on:{back:e.close}})],1)]),s(\"el-tabs\",{staticStyle:{\"padding-top\":\"1rem\"},attrs:{\"tab-position\":\"top\"}},[s(\"el-tab-pane\",{staticStyle:{\"padding-top\":\"1rem\"},attrs:{label:\"拉流代理信息\"}},[s(\"el-form\",{ref:\"streamProxy\",staticStyle:{width:\"50%\",margin:\"0 auto\"},attrs:{rules:e.rules,model:e.streamProxy,\"label-width\":\"140px\"}},[s(\"el-form-item\",{attrs:{label:\"类型\",prop:\"type\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择代理类型\"},model:{value:e.streamProxy.type,callback:function(t){e.$set(e.streamProxy,\"type\",t)},expression:\"streamProxy.type\"}},[s(\"el-option\",{key:\"默认\",attrs:{label:\"默认\",value:\"default\"}}),s(\"el-option\",{key:\"FFmpeg\",attrs:{label:\"FFmpeg\",value:\"ffmpeg\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"应用名\",prop:\"app\"}},[s(\"el-input\",{attrs:{clearable:\"\"},model:{value:e.streamProxy.app,callback:function(t){e.$set(e.streamProxy,\"app\",t)},expression:\"streamProxy.app\"}})],1),s(\"el-form-item\",{attrs:{label:\"流ID\",prop:\"stream\"}},[s(\"el-input\",{attrs:{clearable:\"\"},model:{value:e.streamProxy.stream,callback:function(t){e.$set(e.streamProxy,\"stream\",t)},expression:\"streamProxy.stream\"}})],1),s(\"el-form-item\",{attrs:{label:\"拉流地址\",prop:\"url\"}},[s(\"el-input\",{attrs:{clearable:\"\"},model:{value:e.streamProxy.srcUrl,callback:function(t){e.$set(e.streamProxy,\"srcUrl\",t)},expression:\"streamProxy.srcUrl\"}})],1),s(\"el-form-item\",{attrs:{label:\"超时时间(秒)\",prop:\"timeoutMs\"}},[s(\"el-input\",{attrs:{clearable:\"\"},model:{value:e.streamProxy.timeout,callback:function(t){e.$set(e.streamProxy,\"timeout\",t)},expression:\"streamProxy.timeout\"}})],1),s(\"el-form-item\",{attrs:{label:\"节点选择\",prop:\"rtpType\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择拉流节点\"},on:{change:e.mediaServerIdChange},model:{value:e.streamProxy.relatesMediaServerId,callback:function(t){e.$set(e.streamProxy,\"relatesMediaServerId\",t)},expression:\"streamProxy.relatesMediaServerId\"}},[s(\"el-option\",{key:\"auto\",attrs:{label:\"自动选择\",value:\"\"}}),e._l(e.mediaServerList,(function(e){return s(\"el-option\",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),\"ffmpeg\"==e.streamProxy.type?s(\"el-form-item\",{attrs:{label:\"FFmpeg命令模板\",prop:\"ffmpegCmdKey\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择FFmpeg命令模板\"},model:{value:e.streamProxy.ffmpegCmdKey,callback:function(t){e.$set(e.streamProxy,\"ffmpegCmdKey\",t)},expression:\"streamProxy.ffmpegCmdKey\"}},e._l(Object.keys(e.ffmpegCmdList),(function(t){return s(\"el-option\",{key:t,attrs:{label:e.ffmpegCmdList[t],value:t}})})),1)],1):e._e(),s(\"el-form-item\",{attrs:{label:\"拉流方式(RTSP)\",prop:\"rtspType\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择拉流方式\"},model:{value:e.streamProxy.rtspType,callback:function(t){e.$set(e.streamProxy,\"rtspType\",t)},expression:\"streamProxy.rtspType\"}},[s(\"el-option\",{attrs:{label:\"TCP\",value:\"0\"}}),s(\"el-option\",{attrs:{label:\"UDP\",value:\"1\"}}),s(\"el-option\",{attrs:{label:\"组播\",value:\"2\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"无人观看\",prop:\"noneReader\"}},[s(\"el-radio-group\",{model:{value:e.streamProxy.noneReader,callback:function(t){e.$set(e.streamProxy,\"noneReader\",t)},expression:\"streamProxy.noneReader\"}},[s(\"el-radio\",{attrs:{label:0}},[e._v(\"不做处理\")]),s(\"el-radio\",{attrs:{label:1}},[e._v(\"停用\")]),s(\"el-radio\",{attrs:{label:2}},[e._v(\"移除\")])],1)],1),s(\"el-form-item\",{attrs:{label:\"其他选项\"}},[s(\"div\",{staticStyle:{float:\"left\"}},[s(\"el-checkbox\",{attrs:{label:\"启用\"},model:{value:e.streamProxy.enable,callback:function(t){e.$set(e.streamProxy,\"enable\",t)},expression:\"streamProxy.enable\"}}),s(\"el-checkbox\",{attrs:{label:\"开启音频\"},model:{value:e.streamProxy.enableAudio,callback:function(t){e.$set(e.streamProxy,\"enableAudio\",t)},expression:\"streamProxy.enableAudio\"}}),s(\"el-checkbox\",{attrs:{label:\"录制\"},model:{value:e.streamProxy.enableMp4,callback:function(t){e.$set(e.streamProxy,\"enableMp4\",t)},expression:\"streamProxy.enableMp4\"}})],1)]),s(\"el-form-item\",[s(\"div\",{staticStyle:{float:\"right\"}},[s(\"el-button\",{attrs:{type:\"primary\",loading:e.saveLoading},on:{click:e.onSubmit}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:e.close}},[e._v(\"取消\")])],1)])],1)],1),e.streamProxy.id?s(\"el-tab-pane\",{attrs:{label:\"国标通道配置\"}},[s(\"CommonChannelEdit\",{ref:\"commonChannelEdit\",attrs:{\"data-form\":e.streamProxy,cancel:e.close}})],1):e._e()],1)],1)},r=[],l=(s(\"b64b\"),s(\"7317\")),c={name:\"ChannelEdit\",components:{CommonChannelEdit:l[\"a\"]},props:[\"value\",\"closeEdit\"],data:function(){return{saveLoading:!1,streamProxy:this.value,mediaServerList:{},ffmpegCmdList:{},rules:{name:[{required:!0,message:\"请输入名称\",trigger:\"blur\"}],app:[{required:!0,message:\"请输入应用名\",trigger:\"blur\"}],stream:[{required:!0,message:\"请输入流ID\",trigger:\"blur\"}],srcUrl:[{required:!0,message:\"请输入要代理的流\",trigger:\"blur\"}],timeout:[{required:!0,message:\"请输入FFmpeg推流成功超时时间\",trigger:\"blur\"}],ffmpegCmdKey:[{required:!1,message:\"请输入FFmpeg命令参数模板（可选）\",trigger:\"blur\"}]}}},watch:{value:function(e,t){this.streamProxy=e}},created:function(){var e=this;console.log(this.streamProxy),this.$store.dispatch(\"server/getOnlineMediaServerList\").then((function(t){e.mediaServerList=t}))},methods:{onSubmit:function(){var e=this;this.saveLoading=!0,this.noneReaderHandler(),this.streamProxy.id?this.$store.dispatch(\"streamProxy/update\",this.streamProxy).then((function(t){e.saveLoading=!1,e.$message.success({showClose:!0,message:\"保存成功\"}),e.streamProxy=t})).catch((function(t){e.$message.error({showClose:!0,message:t}),e.saveLoading=!1})).finally((function(){e.saveLoading=!1})):this.$store.dispatch(\"streamProxy/add\",this.streamProxy).then((function(t){e.saveLoading=!1,e.$message.success({showClose:!0,message:\"保存成功\"}),e.streamProxy=t})).catch((function(t){e.$message.error({showClose:!0,message:t}),e.saveLoading=!1})).finally((function(){e.saveLoading=!1}))},close:function(){this.closeEdit()},mediaServerIdChange:function(){var e=this;\"auto\"!==this.streamProxy.relatesMediaServerId&&this.$store.dispatch(\"streamProxy/queryFfmpegCmdList\",this.streamProxy.relatesMediaServerId).then((function(t){e.ffmpegCmdList=t,e.streamProxy.ffmpegCmdKey=Object.keys(t)[0]}))},noneReaderHandler:function(){console.log(this.streamProxy),this.streamProxy.noneReader&&0!==this.streamProxy.noneReader?1===this.streamProxy.noneReader?(this.streamProxy.enableDisableNoneReader=!0,this.streamProxy.enableRemoveNoneReader=!1):2===this.streamProxy.noneReader&&(this.streamProxy.enableDisableNoneReader=!1,this.streamProxy.enableRemoveNoneReader=!0):(this.streamProxy.enableDisableNoneReader=!1,this.streamProxy.enableRemoveNoneReader=!1)}}},d=c,u=s(\"2877\"),m=Object(u[\"a\"])(d,n,r,!1,null,null,null),p=m.exports,f=s(\"2b0e\"),h={name:\"Proxy\",components:{devicePlayer:i[\"a\"],StreamProxyEdit:p},data:function(){return{streamProxyList:[],currentPusher:{},updateLooper:0,currentDeviceChannelsLenth:0,currentPage:1,count:15,total:0,streamProxy:null,searchSrt:\"\",mediaServerId:\"\",pulling:\"\",mediaServerList:[]}},computed:{Vue:function(){return f[\"default\"]},myServerId:function(){return this.$store.getters.serverId}},mounted:function(){this.initData(),this.startUpdateList()},destroyed:function(){this.$destroy(\"videojs\"),clearTimeout(this.updateLooper)},methods:{initData:function(){var e=this;this.getStreamProxyList(),this.$store.dispatch(\"server/getOnlineMediaServerList\").then((function(t){e.mediaServerList=t}))},startUpdateList:function(){var e=this;this.updateLooper=setInterval((function(){e.streamProxy||e.getStreamProxyList()}),1e3)},currentChange:function(e){this.currentPage=e,this.getStreamProxyList()},handleSizeChange:function(e){this.count=e,this.getStreamProxyList()},getStreamProxyList:function(){var e=this;this.$store.dispatch(\"streamProxy/queryList\",{page:this.currentPage,count:this.count,query:this.searchSrt,pulling:this.pulling,mediaServerId:this.mediaServerId}).then((function(t){e.total=t.total;for(var s=0;s<t.list.length;s++)t.list[s][\"playLoading\"]=!1;e.streamProxyList=t.list}))},addStreamProxy:function(){this.streamProxy={type:\"default\",dataType:3,noneReader:1,enable:!0,enableAudio:!0,mediaServerId:\"\",timeout:10}},edit:function(e){e.enableDisableNoneReader?this.$set(e,\"noneReader\",1):e.enableRemoveNoneReader?this.$set(e,\"noneReader\",2):this.$set(e,\"noneReader\",0),this.streamProxy=e,this.$set(this.streamProxy,\"rtspType\",e.rtspType)},closeEdit:function(e){this.streamProxy=null},play:function(e){var t=this;e.playLoading=!0,this.$store.dispatch(\"streamProxy/play\",e.id).then((function(e){t.$refs.devicePlayer.openDialog(\"streamPlay\",null,null,{streamInfo:e,hasAudio:!0})})).catch((function(e){console.log(e)})).finally((function(){e.playLoading=!1}))},stopPlay:function(e){var t=this;this.$store.dispatch(\"streamProxy/stopPlay\",e.id).then((function(e){t.$refs.devicePlayer.openDialog(\"streamPlay\",null,null,{streamInfo:e,hasAudio:!0})})).catch((function(e){console.log(e)}))},queryCloudRecords:function(e){this.$router.push(\"/cloudRecord/detail/\".concat(e.app,\"/\").concat(e.stream))},deleteStreamProxy:function(e){var t=this;this.$confirm(\"确定删除此代理吗？\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){t.$store.dispatch(\"streamProxy/remove\",e.id).then((function(e){t.$message.success({showClose:!0,message:\"删除成功\"}),t.initData()}))})).catch((function(){}))},refresh:function(){this.initData()}}},b=h,v=Object(u[\"a\"])(b,o,a,!1,null,null,null);t[\"default\"]=v.exports},5869:function(e,t,s){\"use strict\";s(\"e0ad\")},\"6ebe\":function(e,t,s){\"use strict\";s(\"28b5\")},7295:function(e,t,s){},7317:function(e,t,s){\"use strict\";var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticStyle:{width:\"100%\"},attrs:{id:\"CommonChannelEdit\"}},[s(\"el-form\",{ref:\"passwordForm\",staticClass:\"channel-form\",attrs:{\"status-icon\":\"\",\"label-width\":\"160px\"}},[s(\"div\",{staticClass:\"form-box\"},[s(\"el-form-item\",{attrs:{label:\"名称\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入通道名称\"},model:{value:e.form.gbName,callback:function(t){e.$set(e.form,\"gbName\",t)},expression:\"form.gbName\"}})],1),s(\"el-form-item\",{attrs:{label:\"编码\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入通道编码\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[s(\"el-button\",{on:{click:function(t){return e.buildDeviceIdCode(e.form.gbDeviceId)}}},[e._v(\"生成\")])]},proxy:!0}]),model:{value:e.form.gbDeviceId,callback:function(t){e.$set(e.form,\"gbDeviceId\",t)},expression:\"form.gbDeviceId\"}})],1),s(\"el-form-item\",{attrs:{label:\"设备厂商\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入设备厂商\"},model:{value:e.form.gbManufacturer,callback:function(t){e.$set(e.form,\"gbManufacturer\",t)},expression:\"form.gbManufacturer\"}})],1),s(\"el-form-item\",{attrs:{label:\"设备型号\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入设备型号\"},model:{value:e.form.gbModel,callback:function(t){e.$set(e.form,\"gbModel\",t)},expression:\"form.gbModel\"}})],1),s(\"el-form-item\",{attrs:{label:\"行政区域\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入行政区域\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[s(\"el-button\",{on:{click:function(t){return e.chooseCivilCode()}}},[e._v(\"选择\")])]},proxy:!0}]),model:{value:e.form.gbCivilCode,callback:function(t){e.$set(e.form,\"gbCivilCode\",t)},expression:\"form.gbCivilCode\"}})],1),s(\"el-form-item\",{attrs:{label:\"安装地址\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入安装地址\"},model:{value:e.form.gbAddress,callback:function(t){e.$set(e.form,\"gbAddress\",t)},expression:\"form.gbAddress\"}})],1),s(\"el-form-item\",{attrs:{label:\"子设备\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择是否有子设备\"},model:{value:e.form.gbParental,callback:function(t){e.$set(e.form,\"gbParental\",t)},expression:\"form.gbParental\"}},[s(\"el-option\",{attrs:{label:\"有\",value:1}}),s(\"el-option\",{attrs:{label:\"无\",value:0}})],1)],1),s(\"el-form-item\",{attrs:{label:\"父节点编码\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入父节点编码或选择所属虚拟组织\"},scopedSlots:e._u([{key:\"append\",fn:function(){return[s(\"el-button\",{on:{click:function(t){return e.chooseGroup()}}},[e._v(\"选择\")])]},proxy:!0}]),model:{value:e.form.gbParentId,callback:function(t){e.$set(e.form,\"gbParentId\",t)},expression:\"form.gbParentId\"}})],1),s(\"el-form-item\",{attrs:{label:\"设备状态\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择设备状态\"},model:{value:e.form.gbStatus,callback:function(t){e.$set(e.form,\"gbStatus\",t)},expression:\"form.gbStatus\"}},[s(\"el-option\",{attrs:{label:\"在线\",value:\"ON\"}}),s(\"el-option\",{attrs:{label:\"离线\",value:\"OFF\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"经度\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入经度\"},model:{value:e.form.gbLongitude,callback:function(t){e.$set(e.form,\"gbLongitude\",t)},expression:\"form.gbLongitude\"}})],1),s(\"el-form-item\",{attrs:{label:\"纬度\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入纬度\"},model:{value:e.form.gbLatitude,callback:function(t){e.$set(e.form,\"gbLatitude\",t)},expression:\"form.gbLatitude\"}})],1),s(\"el-form-item\",{attrs:{label:\"云台类型\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择云台类型\"},model:{value:e.form.gbPtzType,callback:function(t){e.$set(e.form,\"gbPtzType\",t)},expression:\"form.gbPtzType\"}},[s(\"el-option\",{attrs:{label:\"球机\",value:1}}),s(\"el-option\",{attrs:{label:\"半球\",value:2}}),s(\"el-option\",{attrs:{label:\"固定枪机\",value:3}}),s(\"el-option\",{attrs:{label:\"遥控枪机\",value:4}}),s(\"el-option\",{attrs:{label:\"遥控半球\",value:5}}),s(\"el-option\",{attrs:{label:\"多目设备的全景/拼接通道\",value:6}}),s(\"el-option\",{attrs:{label:\"多目设备的分割通道\",value:7}})],1)],1)],1),s(\"div\",[s(\"el-form-item\",{attrs:{label:\"警区\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入警区\"},model:{value:e.form.gbBlock,callback:function(t){e.$set(e.form,\"gbBlock\",t)},expression:\"form.gbBlock\"}})],1),s(\"el-form-item\",{attrs:{label:\"设备归属\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入设备归属\"},model:{value:e.form.gbOwner,callback:function(t){e.$set(e.form,\"gbOwner\",t)},expression:\"form.gbOwner\"}})],1),s(\"el-form-item\",{attrs:{label:\"信令安全模式\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择信令安全模式\"},model:{value:e.form.gbSafetyWay,callback:function(t){e.$set(e.form,\"gbSafetyWay\",t)},expression:\"form.gbSafetyWay\"}},[s(\"el-option\",{attrs:{label:\"不采用\",value:0}}),s(\"el-option\",{attrs:{label:\"S/MIME签名\",value:2}}),s(\"el-option\",{attrs:{label:\"S/MIME加密签名同时采用\",value:3}}),s(\"el-option\",{attrs:{label:\"数字摘要\",value:4}})],1)],1),s(\"el-form-item\",{attrs:{label:\"注册方式\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择注册方式\"},model:{value:e.form.gbRegisterWay,callback:function(t){e.$set(e.form,\"gbRegisterWay\",t)},expression:\"form.gbRegisterWay\"}},[s(\"el-option\",{attrs:{label:\"IETFRFC3261标准\",value:1}}),s(\"el-option\",{attrs:{label:\"基于口令的双向认证\",value:2}}),s(\"el-option\",{attrs:{label:\"基于数字证书的双向认证注册\",value:3}})],1)],1),s(\"el-form-item\",{attrs:{label:\"证书序列号\"}},[s(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入证书序列号\"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,\"gbCertNum\",t)},expression:\"form.gbCertNum\"}})],1),s(\"el-form-item\",{attrs:{label:\"证书有效标识\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择证书有效标识\"},model:{value:e.form.gbCertifiable,callback:function(t){e.$set(e.form,\"gbCertifiable\",t)},expression:\"form.gbCertifiable\"}},[s(\"el-option\",{attrs:{label:\"有效\",value:1}}),s(\"el-option\",{attrs:{label:\"无效\",value:0}})],1)],1),s(\"el-form-item\",{attrs:{label:\"无效原因码\"}},[s(\"el-input\",{attrs:{type:\"errCode\",placeholder:\"请输入无效原因码\"},model:{value:e.form.gbCertNum,callback:function(t){e.$set(e.form,\"gbCertNum\",t)},expression:\"form.gbCertNum\"}})],1),s(\"el-form-item\",{attrs:{label:\"证书终止有效期\"}},[s(\"el-date-picker\",{staticStyle:{width:\"100%\"},attrs:{type:\"datetime\",placeholder:\"选择日期时间\"},model:{value:e.form.gbEndTime,callback:function(t){e.$set(e.form,\"gbEndTime\",t)},expression:\"form.gbEndTime\"}})],1),s(\"el-form-item\",{attrs:{label:\"保密属性\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择保密属性\"},model:{value:e.form.gbSecrecy,callback:function(t){e.$set(e.form,\"gbSecrecy\",t)},expression:\"form.gbSecrecy\"}},[s(\"el-option\",{attrs:{label:\"不涉密\",value:0}}),s(\"el-option\",{attrs:{label:\"涉密\",value:1}})],1)],1),s(\"el-form-item\",{attrs:{label:\"IP地址\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入IP地址\"},model:{value:e.form.gbIpAddress,callback:function(t){e.$set(e.form,\"gbIpAddress\",t)},expression:\"form.gbIpAddress\"}})],1),s(\"el-form-item\",{attrs:{label:\"端口\"}},[s(\"el-input\",{attrs:{type:\"number\",placeholder:\"请输入端口\"},model:{value:e.form.gbPort,callback:function(t){e.$set(e.form,\"gbPort\",t)},expression:\"form.gbPort\"}})],1),s(\"el-form-item\",{attrs:{label:\"设备口令\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入设备口令\"},model:{value:e.form.gbPassword,callback:function(t){e.$set(e.form,\"gbPassword\",t)},expression:\"form.gbPassword\"}})],1)],1),s(\"div\",[s(\"el-form-item\",{attrs:{label:\"业务分组编号\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入业务分组编号\"},model:{value:e.form.gbBusinessGroupId,callback:function(t){e.$set(e.form,\"gbBusinessGroupId\",t)},expression:\"form.gbBusinessGroupId\"}})],1),s(\"el-form-item\",{attrs:{label:\"位置类型\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbPositionType,callback:function(t){e.$set(e.form,\"gbPositionType\",t)},expression:\"form.gbPositionType\"}},[s(\"el-option\",{attrs:{label:\"省际检查站\",value:1}}),s(\"el-option\",{attrs:{label:\"党政机关\",value:2}}),s(\"el-option\",{attrs:{label:\"车站码头\",value:3}}),s(\"el-option\",{attrs:{label:\"中心广场\",value:4}}),s(\"el-option\",{attrs:{label:\"体育场馆\",value:5}}),s(\"el-option\",{attrs:{label:\"商业中心\",value:6}}),s(\"el-option\",{attrs:{label:\"宗教场所\",value:7}}),s(\"el-option\",{attrs:{label:\"校园周边\",value:8}}),s(\"el-option\",{attrs:{label:\"治安复杂区域\",value:9}}),s(\"el-option\",{attrs:{label:\"交通干线\",value:10}})],1)],1),s(\"el-form-item\",{attrs:{label:\"室外/室内\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbRoomType,callback:function(t){e.$set(e.form,\"gbRoomType\",t)},expression:\"form.gbRoomType\"}},[s(\"el-option\",{attrs:{label:\"室外\",value:1}}),s(\"el-option\",{attrs:{label:\"室内\",value:2}})],1)],1),s(\"el-form-item\",{attrs:{label:\"用途\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbUseType,callback:function(t){e.$set(e.form,\"gbUseType\",t)},expression:\"form.gbUseType\"}},[s(\"el-option\",{attrs:{label:\"治安\",value:1}}),s(\"el-option\",{attrs:{label:\"交通\",value:2}}),s(\"el-option\",{attrs:{label:\"重点\",value:3}})],1)],1),s(\"el-form-item\",{attrs:{label:\"补光\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbSupplyLightType,callback:function(t){e.$set(e.form,\"gbSupplyLightType\",t)},expression:\"form.gbSupplyLightType\"}},[s(\"el-option\",{attrs:{label:\"无补光\",value:1}}),s(\"el-option\",{attrs:{label:\"红外补光\",value:2}}),s(\"el-option\",{attrs:{label:\"白光补光\",value:3}}),s(\"el-option\",{attrs:{label:\"激光补光\",value:4}}),s(\"el-option\",{attrs:{label:\"其他\",value:9}})],1)],1),s(\"el-form-item\",{attrs:{label:\"监视方位\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择位置类型\"},model:{value:e.form.gbDirectionType,callback:function(t){e.$set(e.form,\"gbDirectionType\",t)},expression:\"form.gbDirectionType\"}},[s(\"el-option\",{attrs:{label:\"东(西向东)\",value:1}}),s(\"el-option\",{attrs:{label:\"西(东向西)\",value:2}}),s(\"el-option\",{attrs:{label:\"南(北向南)\",value:3}}),s(\"el-option\",{attrs:{label:\"北(南向北)\",value:4}}),s(\"el-option\",{attrs:{label:\"东南(西北到东南)\",value:5}}),s(\"el-option\",{attrs:{label:\"东北(西南到东北)\",value:6}}),s(\"el-option\",{attrs:{label:\"西南(东北到西南)\",value:7}}),s(\"el-option\",{attrs:{label:\"西北(东南到西北)\",value:8}})],1)],1),s(\"el-form-item\",{attrs:{label:\"分辨率\"}},[s(\"el-input\",{attrs:{placeholder:\"请输入分辨率\"},model:{value:e.form.gbResolution,callback:function(t){e.$set(e.form,\"gbResolution\",t)},expression:\"form.gbResolution\"}})],1),s(\"el-form-item\",{attrs:{label:\"下载倍速\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{multiple:\"\",placeholder:\"请选择位置类型\"},model:{value:e.form.gbDownloadSpeedArray,callback:function(t){e.$set(e.form,\"gbDownloadSpeedArray\",t)},expression:\"form.gbDownloadSpeedArray\"}},[s(\"el-option\",{attrs:{label:\"1倍速\",value:\"1\"}}),s(\"el-option\",{attrs:{label:\"2倍速\",value:\"2\"}}),s(\"el-option\",{attrs:{label:\"4倍速\",value:\"4\"}}),s(\"el-option\",{attrs:{label:\"8倍速\",value:\"8\"}}),s(\"el-option\",{attrs:{label:\"16倍速\",value:\"16\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"空域编码能力\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择空域编码能力\"},model:{value:e.form.gbSvcSpaceSupportMod,callback:function(t){e.$set(e.form,\"gbSvcSpaceSupportMod\",t)},expression:\"form.gbSvcSpaceSupportMod\"}},[s(\"el-option\",{attrs:{label:\"1级增强\",value:\"1\"}}),s(\"el-option\",{attrs:{label:\"2级增强\",value:\"2\"}}),s(\"el-option\",{attrs:{label:\"3级增强\",value:\"3\"}})],1)],1),s(\"el-form-item\",{attrs:{label:\"时域编码能力\"}},[s(\"el-select\",{staticStyle:{width:\"100%\"},attrs:{placeholder:\"请选择空域编码能力\"},model:{value:e.form.gbSvcTimeSupportMode,callback:function(t){e.$set(e.form,\"gbSvcTimeSupportMode\",t)},expression:\"form.gbSvcTimeSupportMode\"}},[s(\"el-option\",{attrs:{label:\"1级增强\",value:\"1\"}}),s(\"el-option\",{attrs:{label:\"2级增强\",value:\"2\"}}),s(\"el-option\",{attrs:{label:\"3级增强\",value:\"3\"}})],1)],1),s(\"div\",{staticStyle:{float:\"right\"}},[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.onSubmit}},[e._v(\"保存\")]),e.cancel?s(\"el-button\",{on:{click:e.cancelSubmit}},[e._v(\"取消\")]):e._e(),1===e.form.dataType?s(\"el-button\",{on:{click:e.reset}},[e._v(\"重置\")]):e._e()],1)],1)]),s(\"channelCode\",{ref:\"channelCode\"}),s(\"chooseCivilCode\",{ref:\"chooseCivilCode\"}),s(\"chooseGroup\",{ref:\"chooseGroup\"})],1)},a=[],i=(s(\"a15b\"),s(\"d3b7\"),s(\"165c\")),n=s(\"363b\"),r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"chooseGroup\"}},[s(\"el-dialog\",{directives:[{name:\"el-drag-dialog\",rawName:\"v-el-drag-dialog\"}],attrs:{title:\"选择虚拟组织\",width:\"30%\",top:\"5rem\",\"append-to-body\":!0,\"close-on-click-modal\":!1,visible:e.showDialog,\"destroy-on-close\":!0},on:{\"update:visible\":function(t){e.showDialog=t},close:function(t){return e.close()}}},[s(\"GroupTree\",{ref:\"regionTree\",attrs:{\"show-header\":!0,edit:!0,\"enable-add-channel\":!1,\"click-event\":e.treeNodeClickEvent,\"on-channel-change\":e.onChannelChange,\"tree-height\":\"45vh\"}}),s(\"el-form\",[s(\"el-form-item\",[s(\"div\",{staticStyle:{\"text-align\":\"right\"}},[s(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.onSubmit}},[e._v(\"保存\")]),s(\"el-button\",{on:{click:e.close}},[e._v(\"取消\")])],1)])],1)],1)],1)},l=[],c=s(\"a888\"),d=s(\"c2c8\"),u={name:\"ChooseCivilCode\",directives:{elDragDialog:c[\"a\"]},components:{GroupTree:d[\"a\"]},props:{},data:function(){return{showDialog:!1,endCallback:!1,groupDeviceId:\"\",businessGroup:\"\"}},computed:{},created:function(){},methods:{openDialog:function(e){this.showDialog=!0,this.endCallback=e},onSubmit:function(){this.endCallback&&this.endCallback(this.groupDeviceId,this.businessGroup),this.close()},close:function(){this.showDialog=!1},treeNodeClickEvent:function(e){\"\"!==e.deviceId&&e.deviceId!==e.businessGroup&&(this.groupDeviceId=e.deviceId,this.businessGroup=e.businessGroup)},onChannelChange:function(e){}}},m=u,p=s(\"2877\"),f=Object(p[\"a\"])(m,r,l,!1,null,null,null),h=f.exports,b={name:\"CommonChannelEdit\",components:{ChooseCivilCode:n[\"a\"],ChooseGroup:h,channelCode:i[\"a\"]},props:[\"id\",\"dataForm\",\"saveSuccess\",\"cancel\"],data:function(){return{loading:!1,form:{}}},created:function(){this.id?this.getCommonChannel():(this.dataForm.gbDeviceId||(this.dataForm.gbDeviceId=\"\"),console.log(this.dataForm),this.form=this.dataForm)},methods:{onSubmit:function(){var e=this;this.loading=!0,this.form.gbDownloadSpeedArray&&(this.form.gbDownloadSpeed=this.form.gbDownloadSpeedArray.join(\"/\")),this.form.gbId?this.$store.dispatch(\"commonChanel/update\",this.form).then((function(t){e.$message.success({showClose:!0,message:\"保存成功\"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]})):this.$store.dispatch(\"commonChanel/add\",this.form).then((function(t){e.$message.success({showClose:!0,message:\"保存成功\"}),e.saveSuccess&&e.saveSuccess()})).finally((function(){return[e.loading=!1]}))},reset:function(){var e=this;this.$confirm(\"确定重置为默认内容?\",\"提示\",{dangerouslyUseHTMLString:!0,confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){e.loading=!0,e.$axios({method:\"post\",url:\"/api/common/channel/reset\",params:{id:e.form.gbId}}).then((function(t){0===t.data.code&&(e.$message.success({showClose:!0,message:\"重置成功 已保存\"}),e.getCommonChannel())})).catch((function(e){console.error(e)})).finally((function(){return[e.loading=!1]}))})).catch((function(){}))},getCommonChannel:function(){var e=this;this.loading=!0,this.$store.dispatch(\"commonChanel/queryOne\",this.id).then((function(t){t.gbDownloadSpeed&&(t.gbDownloadSpeedArray=t.gbDownloadSpeed.split(\"/\")),e.form=t})).finally((function(){e.loading=!1}))},buildDeviceIdCode:function(e){var t=this;this.$refs.channelCode.openDialog((function(e){console.log(t.form),console.log(\"code===> \"+e),t.form.gbDeviceId=e,console.log(\"code22===> \"+e)}),e)},chooseCivilCode:function(){var e=this;this.$refs.chooseCivilCode.openDialog((function(t){e.form.gbCivilCode=t}))},chooseGroup:function(){var e=this;this.$refs.chooseGroup.openDialog((function(t,s){e.form.gbBusinessGroupId=s,e.form.gbParentId=t}))},cancelSubmit:function(){this.cancel&&this.cancel()}}},v=b,g=(s(\"257e\"),Object(p[\"a\"])(v,o,a,!1,null,null,null));t[\"a\"]=g.exports},\"953f\":function(e,t,s){},\"989a\":function(e,t,s){\"use strict\";s(\"953f\")},a135:function(e,t,s){\"use strict\";s(\"b21a\")},b21a:function(e,t,s){},b680:function(e,t,s){\"use strict\";var o=s(\"23e7\"),a=s(\"a691\"),i=s(\"408a\"),n=s(\"1148\"),r=s(\"d039\"),l=1..toFixed,c=Math.floor,d=function(e,t,s){return 0===t?s:t%2===1?d(e,t-1,s*e):d(e*e,t/2,s)},u=function(e){var t=0,s=e;while(s>=4096)t+=12,s/=4096;while(s>=2)t+=1,s/=2;return t},m=l&&(\"0.000\"!==8e-5.toFixed(3)||\"1\"!==.9.toFixed(0)||\"1.25\"!==1.255.toFixed(2)||\"1000000000000000128\"!==(0xde0b6b3a7640080).toFixed(0))||!r((function(){l.call({})}));o({target:\"Number\",proto:!0,forced:m},{toFixed:function(e){var t,s,o,r,l=i(this),m=a(e),p=[0,0,0,0,0,0],f=\"\",h=\"0\",b=function(e,t){var s=-1,o=t;while(++s<6)o+=e*p[s],p[s]=o%1e7,o=c(o/1e7)},v=function(e){var t=6,s=0;while(--t>=0)s+=p[t],p[t]=c(s/e),s=s%e*1e7},g=function(){var e=6,t=\"\";while(--e>=0)if(\"\"!==t||0===e||0!==p[e]){var s=String(p[e]);t=\"\"===t?s:t+n.call(\"0\",7-s.length)+s}return t};if(m<0||m>20)throw RangeError(\"Incorrect fraction digits\");if(l!=l)return\"NaN\";if(l<=-1e21||l>=1e21)return String(l);if(l<0&&(f=\"-\",l=-l),l>1e-21)if(t=u(l*d(2,69,1))-69,s=t<0?l*d(2,-t,1):l/d(2,t,1),s*=4503599627370496,t=52-t,t>0){b(0,s),o=m;while(o>=7)b(1e7,0),o-=7;b(d(10,o,1),0),o=t-1;while(o>=23)v(1<<23),o-=23;v(1<<o),b(1,1),v(2),h=g()}else b(0,s),b(1<<-t,0),h=g()+n.call(\"0\",m);return m>0?(r=h.length,h=f+(r<=m?\"0.\"+n.call(\"0\",m-r)+h:h.slice(0,r-m)+\".\"+h.slice(r-m))):h=f+h,h}})},bbbb:function(e,t,s){},bbf2:function(e,t,s){\"use strict\";s.r(t);var o=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},a=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s(\"div\",{attrs:{id:\"rtcPlayer\"}},[s(\"video\",{staticStyle:{\"text-align\":\"left\"},attrs:{id:\"webRtcPlayerBox\",controls:\"\",autoplay:\"\"}},[e._v(\" Your browser is too old which doesn't support HTML5 video. \")])])}],i=null,n={name:\"RtcPlayer\",props:[\"videoUrl\",\"error\",\"hasaudio\"],data:function(){return{timer:null}},watch:{videoUrl:function(e,t){this.pause(),this.play(e)},immediate:!0},mounted:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){\"undefined\"===typeof e.videoUrl&&(e.videoUrl=t),console.log(\"初始化时的地址为: \"+e.videoUrl),e.play(e.videoUrl)}))},destroyed:function(){clearTimeout(this.timer)},methods:{play:function(e){var t=this;i=new ZLMRTCClient.Endpoint({element:document.getElementById(\"webRtcPlayerBox\"),debug:!0,zlmsdpUrl:e,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!0,recvOnly:!0,usedatachannel:!1}),i.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(e){console.error(\"ICE 协商出错\"),t.eventcallbacK(\"ICE ERROR\",\"ICE 协商出错\")})),i.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS,(function(e){console.log(\"播放成功\",e.streams),t.eventcallbacK(\"playing\",\"播放成功\")})),i.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(s){console.error(\"offer anwser 交换失败\",s),t.eventcallbacK(\"OFFER ANSWER ERROR \",\"offer anwser 交换失败\"),-400==s.code&&\"流不存在\"==s.msg&&(console.log(\"流不存在\"),t.timer=setTimeout((function(){t.webrtcPlayer.close(),t.play(e)}),100))})),i.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM,(function(e){t.eventcallbacK(\"LOCAL STREAM\",\"获取到了本地流\")}))},pause:function(){null!=i&&(i.close(),i=null)},eventcallbacK:function(e,t){console.log(\"player 事件回调\"),console.log(e),console.log(t)}}},r=n,l=(s(\"6ebe\"),s(\"2877\")),c=Object(l[\"a\"])(r,o,a,!1,null,null,null);t[\"default\"]=c.exports},c5f0:function(e,t,s){\"use strict\";s(\"bbbb\")},d017:function(e,t,s){},e0ad:function(e,t,s){},e8b4:function(e,t,s){\"use strict\";s(\"0868\")},e9c4:function(e,t,s){var o=s(\"23e7\"),a=s(\"d066\"),i=s(\"d039\"),n=a(\"JSON\",\"stringify\"),r=/[\\uD800-\\uDFFF]/g,l=/^[\\uD800-\\uDBFF]$/,c=/^[\\uDC00-\\uDFFF]$/,d=function(e,t,s){var o=s.charAt(t-1),a=s.charAt(t+1);return l.test(e)&&!c.test(a)||c.test(e)&&!l.test(o)?\"\\\\u\"+e.charCodeAt(0).toString(16):e},u=i((function(){return'\"\\\\udf06\\\\ud834\"'!==n(\"\\udf06\\ud834\")||'\"\\\\udead\"'!==n(\"\\udead\")}));n&&o({target:\"JSON\",stat:!0,forced:u},{stringify:function(e,t,s){var o=n.apply(null,arguments);return\"string\"==typeof o?o.replace(r,d):o}})},fdc8:function(e,t,s){\"use strict\";s(\"7295\")}}]);", "extractedComments": []}