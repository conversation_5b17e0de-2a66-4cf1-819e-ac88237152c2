#Generated by Git-Commit-Id-Plugin
#Fri Jun 20 21:29:16 CST 2025
git.branch=master
git.build.host=DESKTOP-KF3KBEH
git.build.time=20250620
git.build.user.email=<EMAIL>
git.build.user.name=ziguicxd
git.build.version=2.7.4
git.closest.tag.commit.count=
git.closest.tag.name=
git.commit.id=764a92a3a2218f449e50938ab9edccbba02efa5e
git.commit.id.abbrev=764a92a
git.commit.id.describe=764a92a-dirty
git.commit.id.describe-short=764a92a-dirty
git.commit.message.full=\u4F18\u5316\u767B\u5F55\u9875\u80CC\u666F\u56FE\u52A0\u8F7D\u901F\u5EA6
git.commit.message.short=\u4F18\u5316\u767B\u5F55\u9875\u80CC\u666F\u56FE\u52A0\u8F7D\u901F\u5EA6
git.commit.time=20250620
git.commit.user.email=<EMAIL>
git.commit.user.name=ziguicxd
git.dirty=true
git.local.branch.ahead=0
git.local.branch.behind=0
git.remote.origin.url=https\://github.com/ziguicxd/wvp-GB28181-pro.git
git.tags=
git.total.commit.count=3507
