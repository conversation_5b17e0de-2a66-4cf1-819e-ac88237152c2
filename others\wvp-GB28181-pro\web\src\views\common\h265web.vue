<template>
  <div id="h265Player" ref="container" style="background-color: #000000; " @dblclick="fullscreenSwich">
    <div id="glplayer" ref="playerBox" style="width: 100%; height: 100%; margin: 0 auto;" />
    <div v-if="playerLoading" class="player-loading">
      <i class="el-icon-loading" />
      <span>视频加载中</span>
    </div>
    <div v-if="showButton" id="buttonsBox" class="buttons-box">
      <div class="buttons-box-left">
        <i v-if="!playing" class="iconfont icon-play h265web-btn" @click="unPause" />
        <i v-if="playing" class="iconfont icon-pause h265web-btn" @click="pause" />
        <i class="iconfont icon-stop h265web-btn" @click="destroy" />
        <i v-if="isNotMute" class="iconfont icon-audio-high h265web-btn" @click="mute()" />
        <i v-if="!isNotMute" class="iconfont icon-audio-mute h265web-btn" @click="cancelMute()" />
      </div>
      <div class="buttons-box-right">
        <!--          <i class="iconfont icon-file-record1 h265web-btn"></i>-->
        <!--          <i class="iconfont icon-xiangqing2 h265web-btn" ></i>-->
        <i
          class="iconfont icon-camera1196054easyiconnet h265web-btn"
          style="font-size: 1rem !important"
          @click="screenshot"
        />
        <i class="iconfont icon-shuaxin11 h265web-btn" @click="playBtnClick" />
        <i v-if="!fullscreen" class="iconfont icon-weibiaoti10 h265web-btn" @click="fullscreenSwich" />
        <i v-if="fullscreen" class="iconfont icon-weibiaoti11 h265web-btn" @click="fullscreenSwich" />
      </div>
    </div>
  </div>
</template>

<script>
const h265webPlayer = {}
/**
 * 从github上复制的
 * @see https://github.com/numberwolf/h265web.js/blob/master/example_normal/index.js
 */
const token = 'base64:********************************************************************************************************************************************************************************************************************************************************************************'
export default {
  name: 'H265web',
  props: ['videoUrl', 'error', 'hasAudio', 'height', 'showButton'],
  data() {
    return {
      playing: false,
      isNotMute: false,
      quieting: false,
      fullscreen: false,
      loaded: false, // mute
      speed: 0,
      kBps: 0,
      btnDom: null,
      videoInfo: null,
      volume: 1,
      rotate: 0,
      vod: true, // 点播
      forceNoOffscreen: false,
      playerWidth: 0,
      playerHeight: 0,
      inited: false,
      playerLoading: false,
      mediaInfo: null
    }
  },
  watch: {
    videoUrl(newData, oldData) {
      this.play(newData)
    },
    playing(newData, oldData) {
      this.$emit('playStatusChange', newData)
    },
    immediate: true
  },
  mounted() {
    const paramUrl = decodeURIComponent(this.$route.params.url)
    window.onresize = () => {
      this.updatePlayerDomSize()
    }
    this.btnDom = document.getElementById('buttonsBox')

    // 全局拦截器已在main.js中启动，这里只需要确保拦截器正常工作
    this.ensureGlobalInterceptorActive()

    console.log('初始化时的地址为: ' + paramUrl)
    if (paramUrl) {
      this.play(this.videoUrl)
    }
  },
  destroyed() {
    if (h265webPlayer[this._uid]) {
      h265webPlayer[this._uid].destroy()
    }
    this.playing = false
    this.loaded = false
    this.playerLoading = false

    // 全局拦截器会持续工作，不需要在组件销毁时恢复
    // console.log('[h265web] 组件销毁，全局拦截器继续工作')
  },
  methods: {
    updatePlayerDomSize() {
      const dom = this.$refs.container
      if (!this.parentNodeResizeObserver) {
        this.parentNodeResizeObserver = new ResizeObserver(entries => {
          this.updatePlayerDomSize()
        })
        this.parentNodeResizeObserver.observe(dom.parentNode)
      }
      const boxWidth = dom.parentNode.clientWidth
      const boxHeight = dom.parentNode.clientHeight
      let width = boxWidth
      let height = (9 / 16) * width
      if (boxHeight > 0 && boxWidth > boxHeight / 9 * 16) {
        height = boxHeight
        width = boxHeight / 9 * 16
      }

      const clientHeight = Math.min(document.body.clientHeight, document.documentElement.clientHeight)
      if (height > clientHeight) {
        height = clientHeight
        width = (16 / 9) * height
      }

      this.$refs.playerBox.style.width = width + 'px'
      this.$refs.playerBox.style.height = height + 'px'
      this.playerWidth = width
      this.playerHeight = height
      if (this.playing) {
        h265webPlayer[this._uid].resize(this.playerWidth, this.playerHeight)
      }
    },
    resize(width, height) {
      this.playerWidth = width
      this.playerHeight = height
      this.$refs.playerBox.style.width = width + 'px'
      this.$refs.playerBox.style.height = height + 'px'
      if (this.playing) {
        h265webPlayer[this._uid].resize(this.playerWidth, this.playerHeight)
      }
    },
    create(url) {
      console.log('开始创建播放器实例，UID:', this._uid, 'URL:', url)

      // 再次确保没有残留的播放器实例
      if (h265webPlayer[this._uid]) {
        console.warn('创建前发现残留的播放器实例，强制清理，UID:', this._uid)
        try {
          if (h265webPlayer[this._uid].release) {
            h265webPlayer[this._uid].release()
          }
        } catch (error) {
          console.warn('清理残留播放器时出现错误:', error)
        }
        h265webPlayer[this._uid] = null
        this.clearPlayerDOM()
      }

      this.playerLoading = true
      const options = {}

      console.log('正在创建新的h265web播放器实例，UID:', this._uid)
      h265webPlayer[this._uid] = new window.new265webjs(url, Object.assign(
        {
          player: 'glplayer', // 播放器容器id
          width: this.playerWidth,
          height: this.playerHeight,
          token: token,
          extInfo: {
            coreProbePart: 0.4,
            probeSize: 8192,
            ignoreAudio: this.hasAudio == null ? 0 : (this.hasAudio ? 0 : 1)
          },
          // 屏蔽统计和日志相关配置
          debug: false, // 关闭调试模式
          debugLevel: 0, // 设置调试级别为0
          logLevel: 0, // 关闭日志输出
          disableStats: true, // 禁用统计
          disableAnalytics: true, // 禁用分析
          noStats: true, // 不发送统计数据
          noLog: true, // 不输出日志
          silent: true // 静默模式
        },
        options
      ))

      console.log('h265web播放器实例创建成功，UID:', this._uid)
      const h265web = h265webPlayer[this._uid]
      h265web.onOpenFullScreen = () => {
        this.fullscreen = true
      }
      h265web.onCloseFullScreen = () => {
        this.fullscreen = false
      }
      h265web.onReadyShowDone = () => {
        // 准备好显示了，尝试自动播放
        const result = h265web.play()
        this.playing = result
        this.playerLoading = false
      }
      h265web.onLoadFinish = () => {
        try {
          this.loaded = true
          // 可以获取mediaInfo
          // @see https://github.com/numberwolf/h265web.js/blob/8b26a31ffa419bd0a0f99fbd5111590e144e36a8/example_normal/index.js#L252C9-L263C11
          if (h265web.mediaInfo && typeof h265web.mediaInfo === 'function') {
            this.mediaInfo = h265web.mediaInfo()
          } else {
            console.warn('播放器不支持mediaInfo方法')
          }
        } catch (error) {
          console.warn('获取媒体信息时出现错误:', error)
          this.loaded = true // 仍然标记为已加载，避免阻塞
        }
      }
      h265web.onPlayTime = (videoPTS) => {
        try {
          // 检查videoPTS是否有效
          if (videoPTS !== null && videoPTS !== undefined && !isNaN(videoPTS)) {
            this.$emit('playTimeChange', videoPTS)
          } else {
            console.warn('播放器返回无效的时间值:', videoPTS)
          }
        } catch (error) {
          console.warn('播放器时间回调出现错误:', error)
        }
      }
      h265web.onSeekFinish = () => {
        try {
          console.log('播放器seek完成')
          this.$emit('seekFinish')
        } catch (error) {
          console.warn('播放器seek完成回调出现错误:', error)
        }
      }
      h265web.do()
    },
    screenshot: function() {
      if (h265webPlayer[this._uid]) {
        const canvas = document.createElement('canvas')
        console.log(this.mediaInfo)
        canvas.width = this.mediaInfo.meta.size.width
        canvas.height = this.mediaInfo.meta.size.height
        h265webPlayer[this._uid].snapshot(canvas) // snapshot to canvas

        // 下载截图
        const link = document.createElement('a')
        link.download = 'screenshot.png'
        link.href = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream')
        link.click()
      }
    },
    playBtnClick: function(event) {
      this.play(this.videoUrl)
    },
    play: function(url) {
      console.log('开始播放视频，URL:', url, 'UID:', this._uid)

      // 确保完全清理旧的播放器
      if (h265webPlayer[this._uid]) {
        console.log('检测到已存在的播放器实例，先销毁，UID:', this._uid)
        this.destroy()
        // 给销毁操作更多时间完成，确保DOM完全清理
        setTimeout(() => {
          console.log('销毁完成，重新创建播放器，UID:', this._uid)
          this.play(url)
        }, 300) // 增加延迟时间
        return
      }

      if (!url) {
        console.warn('播放URL为空，无法播放')
        return
      }

      if (this.playerWidth === 0 || this.playerHeight === 0) {
        console.log('播放器尺寸未初始化，等待DOM更新')
        this.updatePlayerDomSize()
        setTimeout(() => {
          this.play(url)
        }, 300)
        return
      }

      console.log('创建新的播放器实例，UID:', this._uid)
      this.create(url)
    },
    unPause: function() {
      try {
        if (h265webPlayer[this._uid] && h265webPlayer[this._uid].play) {
          // 使用Promise处理来避免AbortError
          const playPromise = h265webPlayer[this._uid].play()
          if (playPromise && typeof playPromise.catch === 'function') {
            playPromise.catch(error => {
              // 忽略AbortError，这通常是由于快速的play/pause操作导致的
              if (error.name !== 'AbortError') {
                console.warn('恢复播放时出现错误:', error)
              }
            })
          }
          this.playing = h265webPlayer[this._uid].isPlaying()
        }
      } catch (error) {
        console.warn('恢复播放时出现错误:', error)
        this.playing = false
      }
      this.err = ''
    },
    pause: function() {
      try {
        if (h265webPlayer[this._uid] && h265webPlayer[this._uid].pause) {
          // 确保暂停操作能够正确执行
          const pauseResult = h265webPlayer[this._uid].pause()
          // 如果pause返回Promise，处理可能的错误
          if (pauseResult && typeof pauseResult.catch === 'function') {
            pauseResult.catch(error => {
              if (error.name !== 'AbortError') {
                console.warn('暂停播放时出现错误:', error)
              }
            })
          }
          this.playing = h265webPlayer[this._uid].isPlaying()
        }
      } catch (error) {
        console.warn('暂停播放时出现错误:', error)
        this.playing = false
      }
      this.err = ''
    },
    mute: function() {
      if (h265webPlayer[this._uid]) {
        h265webPlayer[this._uid].setVoice(0.0)
        this.isNotMute = false
      }
    },
    cancelMute: function() {
      if (h265webPlayer[this._uid]) {
        h265webPlayer[this._uid].setVoice(1.0)
        this.isNotMute = true
      }
    },
    destroy: function() {
      console.log('开始销毁h265web播放器，UID:', this._uid)

      // 立即重置状态，避免其他方法继续调用播放器
      this.playing = false
      this.loaded = false
      this.playerLoading = false
      this.err = ''

      if (h265webPlayer[this._uid]) {
        try {
          console.log('正在销毁播放器实例，UID:', this._uid)

          // 先暂停播放，避免事件监听器继续触发
          if (h265webPlayer[this._uid].pause) {
            h265webPlayer[this._uid].pause()
          }

          // 立即释放播放器资源，不使用setTimeout
          try {
            if (h265webPlayer[this._uid].release) {
              h265webPlayer[this._uid].release()
              console.log('播放器资源已释放，UID:', this._uid)
            }
          } catch (error) {
            console.warn('释放播放器资源时出现错误:', error)
          }

          // 立即清空播放器引用
          h265webPlayer[this._uid] = null
          console.log('播放器引用已清空，UID:', this._uid)

          // 清理DOM容器
          this.clearPlayerDOM()

        } catch (error) {
          console.warn('销毁播放器时出现错误:', error)
          h265webPlayer[this._uid] = null
          this.clearPlayerDOM()
        }
      } else {
        console.log('播放器实例不存在，直接清理DOM，UID:', this._uid)
        this.clearPlayerDOM()
      }

      console.log('h265web播放器销毁完成，UID:', this._uid)
    },

    clearPlayerDOM: function() {
      // 清理DOM容器，移除所有子元素和事件监听器
      try {
        if (this.$refs.playerBox) {
          console.log('清理播放器DOM容器，UID:', this._uid)

          // 移除所有子元素
          while (this.$refs.playerBox.firstChild) {
            this.$refs.playerBox.removeChild(this.$refs.playerBox.firstChild)
          }

          // 清理可能的内联样式
          this.$refs.playerBox.style.cssText = ''

          // 移除可能的事件监听器
          this.$refs.playerBox.innerHTML = ''

          console.log('播放器DOM容器已清理，UID:', this._uid)
        }
      } catch (error) {
        console.warn('清理DOM容器时出现错误:', error)
      }
    },
    fullscreenSwich: function() {
      const isFull = this.isFullscreen()
      if (isFull) {
        h265webPlayer[this._uid].closeFullScreen()
      } else {
        h265webPlayer[this._uid].fullScreen()
      }
      this.fullscreen = !isFull
    },
    isFullscreen: function() {
      return document.fullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement || false
    },
    setPlaybackRate: function(speed) {
      try {
        if (h265webPlayer[this._uid] && h265webPlayer[this._uid].setPlaybackRate) {
          h265webPlayer[this._uid].setPlaybackRate(speed)
        }
      } catch (error) {
        console.warn('设置播放倍速时出现错误:', error)
      }
    },
    seek: function(pts) {
      try {
        console.log('h265web播放器seek方法被调用，目标时间:', pts, '秒')
        console.log('播放器状态:', {
          playerExists: !!h265webPlayer[this._uid],
          seekMethodExists: !!(h265webPlayer[this._uid] && h265webPlayer[this._uid].seek),
          playerUid: this._uid,
          loaded: this.loaded,
          playing: this.playing
        })

        if (h265webPlayer[this._uid] && h265webPlayer[this._uid].seek) {
          console.log('执行播放器seek操作到:', pts, '秒')

          // 检查pts值是否合理
          if (pts < 0) {
            console.warn('seek时间小于0，调整为0')
            pts = 0
          }

          // 记录当前播放状态
          const wasPlayingBeforeSeek = this.playing

          // 执行seek操作
          h265webPlayer[this._uid].seek(pts)
          console.log('播放器seek操作已执行，之前播放状态:', wasPlayingBeforeSeek)

          // 触发seek完成事件，让父组件知道seek操作已完成
          setTimeout(() => {
            try {
              // 通知父组件seek操作完成
              this.$emit('seekFinish')
              console.log('h265web播放器seek操作完成')

              // 如果之前在播放，尝试恢复播放状态
              if (wasPlayingBeforeSeek && h265webPlayer[this._uid] && h265webPlayer[this._uid].play) {
                console.log('尝试恢复播放状态')
                try {
                  const playPromise = h265webPlayer[this._uid].play()
                  if (playPromise && typeof playPromise.catch === 'function') {
                    playPromise.catch(error => {
                      if (error.name !== 'AbortError') {
                        console.warn('seek后恢复播放时出现错误:', error)
                      }
                    })
                  }
                  this.playing = h265webPlayer[this._uid].isPlaying()
                } catch (error) {
                  console.warn('seek后恢复播放时出现错误:', error)
                }
              }
            } catch (error) {
              console.warn('触发seek完成事件时出现错误:', error)
            }
          }, 200) // 增加延迟，确保seek操作完全完成

          return true
        } else {
          console.warn('播放器未准备好或不支持seek操作', {
            playerExists: !!h265webPlayer[this._uid],
            seekMethodExists: !!(h265webPlayer[this._uid] && h265webPlayer[this._uid].seek)
          })
          return false
        }
      } catch (error) {
        console.error('播放器seek时出现错误:', error)
        return false
      }
    },

    // 添加获取当前播放时间的方法（如果播放器支持的话）
    getCurrentTime: function() {
      try {
        if (h265webPlayer[this._uid]) {
          // 尝试不同的时间获取方法
          if (h265webPlayer[this._uid].getCurrentTime) {
            return h265webPlayer[this._uid].getCurrentTime()
          } else if (h265webPlayer[this._uid].getTime) {
            return h265webPlayer[this._uid].getTime()
          } else if (h265webPlayer[this._uid].currentTime !== undefined) {
            return h265webPlayer[this._uid].currentTime
          }
        }
        return null
      } catch (error) {
        console.warn('获取播放器当前时间时出现错误:', error)
        return null
      }
    },

    // 添加获取播放器状态的方法
    getPlayerStatus: function() {
      try {
        if (h265webPlayer[this._uid]) {
          return {
            playing: this.playing,
            loaded: this.loaded,
            playerExists: true,
            hasSeekMethod: !!(h265webPlayer[this._uid].seek),
            hasTimeMethod: !!(h265webPlayer[this._uid].getCurrentTime || h265webPlayer[this._uid].getTime)
          }
        }
        return {
          playing: false,
          loaded: false,
          playerExists: false,
          hasSeekMethod: false,
          hasTimeMethod: false
        }
      } catch (error) {
        console.warn('获取播放器状态时出现错误:', error)
        return null
      }
    },

    // 确保全局拦截器正常工作
    ensureGlobalInterceptorActive() {
      try {
        // 检查全局拦截器是否存在并正常工作
        if (window.h265webInterceptor) {
          const status = window.h265webInterceptor.status()
          if (status.active) {
            // console.log('[h265web] 全局拦截器正常工作')
          } else {
            // console.warn('[h265web] 全局拦截器未激活，尝试重新启动')
            window.h265webInterceptor.start()
          }
        } else {
          // console.warn('[h265web] 全局拦截器不存在，可能未正确加载')
        }
      } catch (error) {
        // console.error('[h265web] 检查全局拦截器状态失败:', error)
      }
    },


  }
}
</script>

<style>
.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;
}

.h265web-btn {
  width: 20px;
  color: rgb(255, 255, 255);
  line-height: 27px;
  margin: 0px 10px;
  padding: 0px 2px;
  cursor: pointer;
  text-align: center;
  font-size: 0.8rem !important;
}

.buttons-box-right {
  position: absolute;
  right: 0;
}
.player-loading {
  width: fit-content;
  height: 30px;
  position: absolute;
  left: calc(50% - 52px);
  top: calc(50% - 52px);
  color: #fff;
  font-size: 16px;
}
.player-loading i{
  font-size: 24px;
  line-height: 24px;
  text-align: center;
  display: block;
}
.player-loading span{
  display: inline-block;
  font-size: 16px;
  height: 24px;
  line-height: 24px;
}
</style>
