(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-27cdfc42"],{"0db8":function(e,t,i){"use strict";i("a4be")},"26d6":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container",attrs:{id:"device"}},[i("deviceList",{directives:[{name:"show",rawName:"v-show",value:null===e.deviceId,expression:"deviceId === null"}],on:{"show-channel":e.showChannelList}}),null!==e.deviceId?i("channelList",{attrs:{"device-id":e.deviceId},on:{"show-device":e.showDevice}}):e._e()],1)},a=[],s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{height:"calc(100vh - 124px)"},attrs:{id:"app"}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.initData},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.initData},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",[i("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-plus",type:"primary"},on:{click:e.add}},[e._v("添加设备")]),i("el-button",{staticStyle:{"margin-right":"1rem"},attrs:{icon:"el-icon-info"},on:{click:function(t){return e.showInfo()}}},[e._v("接入信息 ")])],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:"",loading:e.getDeviceListLoading},on:{click:function(t){return e.getDeviceList()}}})],1)],1),i("el-table",{attrs:{size:"small",data:e.deviceList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"160"}}),i("el-table-column",{attrs:{prop:"deviceId",label:"设备编号","min-width":"160"}}),i("el-table-column",{attrs:{label:"地址","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.hostAddress?i("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.transport.toLowerCase())+"://"+e._s(t.row.hostAddress))]):e._e(),t.row.hostAddress?e._e():i("el-tag",{attrs:{size:"medium"}},[e._v("未知")])],1)]}}])}),i("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"100"}}),i("el-table-column",{attrs:{label:"流传输模式","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{staticStyle:{width:"120px"},attrs:{size:"mini",placeholder:"请选择"},on:{change:function(i){return e.transportChange(t.row)}},model:{value:t.row.streamMode,callback:function(i){e.$set(t.row,"streamMode",i)},expression:"scope.row.streamMode"}},[i("el-option",{key:"UDP",attrs:{label:"UDP",value:"UDP"}}),i("el-option",{key:"TCP-ACTIVE",attrs:{label:"TCP主动模式",value:"TCP-ACTIVE"}}),i("el-option",{key:"TCP-PASSIVE",attrs:{label:"TCP被动模式",value:"TCP-PASSIVE"}})],1)]}}])}),i("el-table-column",{attrs:{label:"通道数","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{staticStyle:{"font-size":"1rem"}},[e._v(e._s(t.row.channelCount))])]}}])}),i("el-table-column",{attrs:{label:"状态","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[t.row.onLine&&e.myServerId!==t.row.serverId?i("el-tag",{staticStyle:{"border-color":"#ecf1af"},attrs:{size:"medium"}},[e._v("在线 ")]):e._e(),t.row.onLine&&e.myServerId===t.row.serverId?i("el-tag",{attrs:{size:"medium"}},[e._v("在线 ")]):e._e(),t.row.onLine?e._e():i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")])],1)]}}])}),i("el-table-column",{attrs:{label:"订阅","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-checkbox",{attrs:{label:"目录",checked:t.row.subscribeCycleForCatalog>0},on:{change:function(i){return e.subscribeForCatalog(t.row.id,i)}}}),i("el-checkbox",{attrs:{label:"位置",checked:t.row.subscribeCycleForMobilePosition>0},on:{change:function(i){return e.subscribeForMobilePosition(t.row.id,i)}}})]}}])}),i("el-table-column",{attrs:{prop:"keepaliveTime",label:"最近心跳","min-width":"140"}}),i("el-table-column",{attrs:{prop:"registerTime",label:"最近注册","min-width":"140"}}),i("el-table-column",{attrs:{label:"操作","min-width":"300",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text",size:"medium",disabled:0===t.row.online,icon:"el-icon-refresh"},on:{click:function(i){return e.refDevice(t.row)},mouseover:function(i){return e.getTooltipContent(t.row.deviceId)}}},[e._v("刷新 ")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{type:"text",size:"medium",icon:"el-icon-video-camera"},on:{click:function(i){return e.showChannelList(t.row)}}},[e._v("通道 ")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{size:"medium",icon:"el-icon-edit",type:"text"},on:{click:function(i){return e.edit(t.row)}}},[e._v("编辑")]),i("el-divider",{attrs:{direction:"vertical"}}),i("el-dropdown",{on:{command:function(i){e.moreClick(i,t.row)}}},[i("el-button",{attrs:{size:"medium",type:"text"}},[e._v(" 操作"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",[i("el-dropdown-item",{staticStyle:{color:"#f56c6c"},attrs:{command:"delete"}},[e._v(" 删除 ")]),i("el-dropdown-item",{attrs:{command:"setGuard",disabled:!t.row.onLine}},[e._v(" 布防 ")]),i("el-dropdown-item",{attrs:{command:"resetGuard",disabled:!t.row.onLine}},[e._v(" 撤防 ")]),i("el-dropdown-item",{attrs:{command:"syncBasicParam",disabled:!t.row.onLine}},[e._v(" 基础配置同步 ")])],1)],1)]}}])})],1),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}}),i("deviceEdit",{ref:"deviceEdit"}),i("syncChannelProgress",{ref:"syncChannelProgress"}),i("configInfo",{ref:"configInfo"})],1)},o=[],r=i("c14f"),l=i("1da1"),c=(i("99af"),i("d3b7"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"deviceEdit"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"设备编辑",width:"40%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{"margin-right":"50px"},attrs:{id:"shared"}},[i("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"设备编号",prop:"deviceId"}},[e.isEdit?i("el-input",{attrs:{disabled:""},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}}):e._e(),e.isEdit?e._e():i("el-input",{attrs:{clearable:""},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}})],1),i("el-form-item",{attrs:{label:"设备名称",prop:"name"}},[i("el-input",{attrs:{clearable:""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),i("el-form-item",{attrs:{label:"密码",prop:"password"}},[i("el-input",{attrs:{clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),i("el-form-item",{attrs:{label:"收流IP",prop:"sdpIp"}},[i("el-input",{attrs:{type:"sdpIp",clearable:""},model:{value:e.form.sdpIp,callback:function(t){e.$set(e.form,"sdpIp",t)},expression:"form.sdpIp"}})],1),i("el-form-item",{attrs:{label:"流媒体ID",prop:"mediaServerId"}},[i("el-select",{staticStyle:{float:"left",width:"100%"},model:{value:e.form.mediaServerId,callback:function(t){e.$set(e.form,"mediaServerId",t)},expression:"form.mediaServerId"}},[i("el-option",{key:"auto",attrs:{label:"自动负载最小",value:"auto"}}),e._l(e.mediaServerList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.id,value:e.id}})}))],2)],1),i("el-form-item",{attrs:{label:"字符集",prop:"charset"}},[i("el-select",{staticStyle:{float:"left",width:"100%"},model:{value:e.form.charset,callback:function(t){e.$set(e.form,"charset",t)},expression:"form.charset"}},[i("el-option",{key:"GB2312",attrs:{label:"GB2312",value:"gb2312"}}),i("el-option",{key:"UTF-8",attrs:{label:"UTF-8",value:"utf-8"}})],1)],1),i("el-form-item",{attrs:{label:"其他选项"}},[i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"SSRC校验"},model:{value:e.form.ssrcCheck,callback:function(t){e.$set(e.form,"ssrcCheck",t)},expression:"form.ssrcCheck"}}),i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"作为消息通道"},model:{value:e.form.asMessageChannel,callback:function(t){e.$set(e.form,"asMessageChannel",t)},expression:"form.asMessageChannel"}}),i("el-checkbox",{staticStyle:{float:"left"},attrs:{label:"收到ACK后发流"},model:{value:e.form.broadcastPushAfterAck,callback:function(t){e.$set(e.form,"broadcastPushAfterAck",t)},expression:"form.broadcastPushAfterAck"}})],1),i("el-form-item",[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确认")]),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)],1)])],1)}),d=[],u=i("a888"),h={name:"DeviceEdit",directives:{elDragDialog:u["a"]},props:{},data:function(){return{listChangeCallback:null,showDialog:!1,isLoging:!1,hostNames:[],mediaServerList:[],form:{},isEdit:!1,rules:{deviceId:[{required:!0,message:"请输入设备编号",trigger:"blur"}]}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.isEdit=!1,e&&(this.isEdit=!0),this.form={},this.listChangeCallback=t,null!=e&&(this.form=e),this.getMediaServerList()},getMediaServerList:function(){var e=this;this.$store.dispatch("server/getOnlineMediaServerList").then((function(t){e.mediaServerList=t}))},onSubmit:function(){var e=this;this.isEdit?this.$store.dispatch("device/update",this.form).then((function(t){e.listChangeCallback()})):this.$store.dispatch("device/add",this.form).then((function(t){e.listChangeCallback()}))},close:function(){this.showDialog=!1,this.$refs.form.resetFields()}}},m=h,f=i("2877"),p=Object(f["a"])(m,c,d,!1,null,null,null),v=p.exports,g=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoging,expression:"isLoging"}],attrs:{id:"SyncChannelProgress"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],staticStyle:{"text-align":"center"},attrs:{width:"240px",top:"13%","append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0,"show-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("el-progress",{attrs:{type:"circle",percentage:e.percentage,status:e.syncStatus}}),i("div",{staticStyle:{"text-align":"center"}},[e._v(" "+e._s(e.msg)+" ")])],1)],1)},b=[],w=(i("a9e3"),{name:"SyncChannelProgress",directives:{elDragDialog:u["a"]},props:["platformId"],data:function(){return{endCallBack:null,syncStatus:null,percentage:0,total:0,current:0,showDialog:!1,isLoging:!1,syncFlag:!1,deviceId:null,timer:null,errorTimer:null,msg:"正在同步"}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log("deviceId: "+e),this.deviceId=e,this.showDialog=!0,this.msg="",this.percentage=0,this.total=0,this.current=0,this.syncFlag=!1,this.syncStatus=null,this.endCallBack=t,this.getProgress()},getProgress:function(){var e=this;this.$store.dispatch("device/queryDeviceSyncStatus",this.deviceId).then((function(t){var i=t.data,n=(t.code,t.msg);null===i?(e.msg=n,e.timer=setTimeout(e.getProgress,300)):i.syncIng?0===i.total?(e.msg="等待同步中",e.timer=setTimeout(e.getProgress,300)):(e.syncFlag=!0,e.total=i.total,e.current=i.current,e.percentage=Math.floor(Number(i.current)/Number(i.total)*1e4)/100,e.msg="同步中...[".concat(i.current,"/").concat(i.total,"]"),e.timer=setTimeout(e.getProgress,300)):i.errorMsg?(e.msg=i.errorMsg,e.syncStatus="exception"):(e.syncStatus="success",e.percentage=100,e.msg="同步成功",setTimeout((function(){e.showDialog=!1}),3e3))})).catch((function(t){console.log(t),e.syncStatus="error",e.msg=t,window.clearTimeout(e.errorTimer),e.errorTimer=setTimeout((function(){e.showDialog=!1}),2e3)}))},close:function(){this.endCallBack&&this.endCallBack(),window.clearTimeout(this.timer)}}}),y=w,I=Object(f["a"])(y,g,b,!1,null,null,null),C=I.exports,S=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"configInfo"}},[i("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],attrs:{title:"接入信息",width:"=80%",top:"2rem","close-on-click-modal":!1,visible:e.showDialog,"destroy-on-close":!0},on:{"update:visible":function(t){e.showDialog=t},close:function(t){return e.close()}}},[i("div",{staticStyle:{"margin-top":"1rem","margin-right":"100px"},attrs:{id:"shared"}},[e.key&&"sip"!==e.key||!e.configInfoData.sip?e._e():i("el-descriptions",{attrs:{title:"国标服务信息",span:2}},[i("el-descriptions-item",{attrs:{label:"编号"}},[e._v(e._s(e.configInfoData.sip.id))]),i("el-descriptions-item",{attrs:{label:"域"}},[e._v(e._s(e.configInfoData.sip.domain))]),i("el-descriptions-item",{attrs:{label:"IP"}},[e._v(e._s(e.configInfoData.sip.showIp))]),i("el-descriptions-item",{attrs:{label:"端口"}},[e._v(e._s(e.configInfoData.sip.port))]),i("el-descriptions-item",{attrs:{label:"密码"}},[i("span",{staticClass:"password-text",on:{click:function(t){e.passwordVisible=!e.passwordVisible}}},[e._v(" "+e._s(e.passwordVisible?e.configInfoData.sip.password:"•••••••••••")+" ")])])],1),"jt1078Config"===e.key&&e.configInfoData.jt1078Config?i("el-descriptions",{attrs:{title:"部标服务信息",span:2}},[i("el-descriptions-item",{attrs:{label:"端口"}},[e._v(e._s(e.configInfoData.jt1078Config.port))]),i("el-descriptions-item",{attrs:{label:"密码"}},[i("span",{staticClass:"password-text",on:{click:function(t){e.jt1078PasswordVisible=!e.jt1078PasswordVisible}}},[e._v(" "+e._s(e.jt1078PasswordVisible?e.configInfoData.jt1078Config.password:"•••••••••••")+" ")])])],1):e._e()],1)])],1)},D=[],_={name:"ConfigInfo",directives:{elDragDialog:u["a"]},props:{},data:function(){return{showDialog:!1,key:null,passwordVisible:!1,jt1078PasswordVisible:!1,configInfoData:{sip:{}}}},computed:{},created:function(){},methods:{openDialog:function(e,t){console.log(e),this.showDialog=!0,this.key=t,this.configInfoData=e,this.passwordVisible=!1,this.jt1078PasswordVisible=!1},close:function(){this.showDialog=!1}}},k=_,$=(i("0db8"),Object(f["a"])(k,S,D,!1,null,null,null)),L=$.exports,x=i("2b0e"),P={name:"App",components:{configInfo:L,deviceEdit:v,syncChannelProgress:C},data:function(){return{deviceList:[],currentDevice:{},searchSrt:"",online:null,videoComponentList:[],updateLooper:0,currentDeviceChannelsLength:0,currentPage:1,count:15,total:0,getDeviceListLoading:!1}},computed:{Vue:function(){return x["default"]},myServerId:function(){return this.$store.getters.serverId}},mounted:function(){this.initData(),this.updateLooper=setInterval(this.getDeviceList,1e4)},destroyed:function(){this.$destroy("videojs"),clearTimeout(this.updateLooper)},methods:{initData:function(){this.currentPage=1,this.total=0,this.getDeviceList()},currentChange:function(e){this.currentPage=e,this.getDeviceList()},handleSizeChange:function(e){this.count=e,this.getDeviceList()},getDeviceList:function(){var e=this;this.getDeviceListLoading=!0,this.$store.dispatch("device/queryDevices",{page:this.currentPage,count:this.count,query:this.searchSrt,status:this.online}).then((function(t){e.total=t.total,e.deviceList=t.list})).finally((function(){e.getDeviceListLoading=!1}))},deleteDevice:function(e){var t=this,i="确定删除此设备？";0!==e.online&&(i="在线设备删除后仍可通过注册再次上线。<br/>如需彻底删除请先将设备离线。<br/><strong>确定删除此设备？</strong>"),this.$confirm(i,"提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",cancelButtonText:"取消",center:!0,type:"warning"}).then((function(){t.$store.dispatch("device/deleteDevice",e.deviceId).then((function(e){t.getDeviceList()}))}))},showChannelList:function(e){this.$emit("show-channel",e.deviceId)},showDevicePosition:function(e){this.$router.push("/map?deviceId=".concat(e.deviceId))},refDevice:function(e){var t=this;console.log("刷新对应设备:"+e.deviceId),this.$store.dispatch("device/sync",e.deviceId).then((function(i){i&&i.errorMsg?t.$message({showClose:!0,message:i.errorMsg,type:"error"}):t.$refs.syncChannelProgress.openDialog(e.deviceId,(function(){t.getDeviceList()}))})).finally((function(){t.getDeviceList()}))},getTooltipContent:function(){var e=Object(l["a"])(Object(r["a"])().m((function e(t){var i;return Object(r["a"])().w((function(e){while(1)switch(e.n){case 0:return i="",e.n=1,this.$store.dispatch("device/queryDeviceSyncStatus",t).then((function(e){null!==e.errorMsg&&(i=e.errorMsg),i="同步中...[".concat(e.current,"/").concat(e.total,"]")})).catch((function(e){i=e}));case 1:return e.a(2,i)}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),transportChange:function(e){console.log("修改传输方式为 ".concat(e.streamMode,"：").concat(e.deviceId," ")),console.log(e.streamMode),this.$store.dispatch("device/updateDeviceTransport",[e.deviceId,e.streamMode])},edit:function(e){var t=this;this.$refs.deviceEdit.openDialog(e,(function(){t.$refs.deviceEdit.close(),t.$message({showClose:!0,message:"设备修改成功，通道字符集将在下次更新生效",type:"success"}),setTimeout(t.getDeviceList,200)}))},add:function(){var e=this;this.$refs.deviceEdit.openDialog(null,(function(){e.$refs.deviceEdit.close(),e.$message({showClose:!0,message:"添加成功",type:"success"}),setTimeout(e.getDeviceList,200)}))},showInfo:function(){var e=this;this.$store.dispatch("server/getSystemConfig").then((function(t){e.serverId=t.addOn.serverId,e.$refs.configInfo.openDialog(t)}))},moreClick:function(e,t){"setGuard"===e?this.setGuard(t):"resetGuard"===e?this.resetGuard(t):"delete"===e?this.deleteDevice(t):"syncBasicParam"===e&&this.syncBasicParam(t)},setGuard:function(e){var t=this;this.$store.dispatch("device/setGuard",e.deviceId).then((function(e){t.$message.success({showClose:!0,message:"布防成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},resetGuard:function(e){var t=this;this.$store.dispatch("device/ResetGuard",e.deviceId).then((function(e){t.$message.success({showClose:!0,message:"撤防成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},subscribeForCatalog:function(e,t){var i=this;this.$store.dispatch("device/subscribeCatalog",{id:e,cycle:t?60:0}).then((function(e){i.$message.success({showClose:!0,message:t?"订阅成功":"取消订阅成功"})})).catch((function(e){i.$message.error({showClose:!0,message:e.message})}))},subscribeForMobilePosition:function(e,t){var i=this;this.$store.dispatch("device/subscribeMobilePosition",{id:e,cycle:t?60:0,interval:t?5:0}).then((function(e){i.$message.success({showClose:!0,message:t?"订阅成功":"取消订阅成功"})})).catch((function(e){i.$message.error({showClose:!0,message:e.message})}))},syncBasicParam:function(e){var t=this;this.$store.dispatch("device/queryBasicParam").then((function(e){t.$message.success({showClose:!0,message:"配置已同步，当前心跳间隔： ".concat(e.BasicParam.HeartBeatInterval," 心跳间隔:").concat(res.data.data.BasicParam.HeartBeatCount)})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))}}},T=P,E=Object(f["a"])(T,s,o,!1,null,null,null),z=E.exports,M=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{height:"calc(100vh - 124px)"},attrs:{id:"channelList"}},[e.editId?e._e():i("div",{staticStyle:{height:"100%"}},[i("el-form",{attrs:{inline:!0,size:"mini"}},[i("el-form-item",{staticStyle:{"margin-right":"2rem"}},[i("el-page-header",{attrs:{content:"通道列表"},on:{back:e.showDevice}})],1),i("el-form-item",{attrs:{label:"搜索"}},[i("el-input",{staticStyle:{"margin-right":"1rem",width:"auto"},attrs:{placeholder:"关键字","prefix-icon":"el-icon-search",clearable:""},on:{input:e.search},model:{value:e.searchSrt,callback:function(t){e.searchSrt=t},expression:"searchSrt"}})],1),i("el-form-item",{attrs:{label:"通道类型"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.channelType,callback:function(t){e.channelType=t},expression:"channelType"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"设备",value:"false"}}),i("el-option",{attrs:{label:"子目录",value:"true"}})],1)],1),i("el-form-item",{attrs:{label:"在线状态"}},[i("el-select",{staticStyle:{width:"8rem","margin-right":"1rem"},attrs:{placeholder:"请选择","default-first-option":""},on:{change:e.search},model:{value:e.online,callback:function(t){e.online=t},expression:"online"}},[i("el-option",{attrs:{label:"全部",value:""}}),i("el-option",{attrs:{label:"在线",value:"true"}}),i("el-option",{attrs:{label:"离线",value:"false"}})],1)],1),i("el-form-item",{attrs:{label:"码流类型重置"}},[i("el-select",{staticStyle:{width:"16rem","margin-right":"1rem"},attrs:{placeholder:"请选择码流类型","default-first-option":""},on:{change:e.subStreamChange},model:{value:e.subStream,callback:function(t){e.subStream=t},expression:"subStream"}},[i("el-option",{attrs:{label:"stream:0(主码流)",value:"stream:0"}}),i("el-option",{attrs:{label:"stream:1(子码流)",value:"stream:1"}}),i("el-option",{attrs:{label:"streamnumber:0(主码流-2022)",value:"streamnumber:0"}}),i("el-option",{attrs:{label:"streamnumber:1(子码流-2022)",value:"streamnumber:1"}}),i("el-option",{attrs:{label:"streamprofile:0(主码流-大华)",value:"streamprofile:0"}}),i("el-option",{attrs:{label:"streamprofile:1(子码流-大华)",value:"streamprofile:1"}}),i("el-option",{attrs:{label:"streamMode:main(主码流-水星+TP-LINK)",value:"streamMode:main"}}),i("el-option",{attrs:{label:"streamMode:sub(子码流-水星+TP-LINK)",value:"streamMode:sub"}})],1)],1),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",circle:""},on:{click:function(t){return e.refresh()}}})],1)],1),i("el-table",{ref:"channelListTable",staticStyle:{width:"100%","font-size":"12px"},attrs:{size:"small",data:e.deviceChannelList,height:"calc(100% - 64px)","header-row-class-name":"table-header"}},[i("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"180"}}),i("el-table-column",{attrs:{prop:"deviceId",label:"编号","min-width":"180"}}),i("el-table-column",{attrs:{label:"快照","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-image",{staticStyle:{width:"60px"},attrs:{src:e.getSnap(t.row),"preview-src-list":e.getBigSnap(t.row),fit:"contain"},on:{error:function(i){return e.getSnapErrorEvent(t.row.deviceId,t.row.channelId)}}},[i("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[i("i",{staticClass:"el-icon-picture-outline"})])])]}}],null,!1,2328878535)}),i("el-table-column",{attrs:{prop:"manufacturer",label:"厂家","min-width":"100"}}),i("el-table-column",{attrs:{label:"位置信息","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.longitude&&t.row.latitude?i("span",[e._v(e._s(t.row.longitude)),i("br"),e._v(e._s(t.row.latitude))]):e._e(),t.row.longitude&&t.row.latitude?e._e():i("span",[e._v("无")])]}}],null,!1,1496757730)}),i("el-table-column",{attrs:{prop:"ptzType",label:"云台类型","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",[e._v(e._s(t.row.ptzTypeText))])]}}],null,!1,867005290)}),i("el-table-column",{attrs:{label:"开启音频","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-color":"#409EFF"},on:{change:function(i){return e.updateChannel(t.row)}},model:{value:t.row.hasAudio,callback:function(i){e.$set(t.row,"hasAudio",i)},expression:"scope.row.hasAudio"}})]}}],null,!1,280923771)}),i("el-table-column",{attrs:{label:"码流类型","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{staticStyle:{"margin-right":"1rem"},attrs:{size:"mini",placeholder:"请选择码流类型","default-first-option":""},on:{change:function(i){return e.channelSubStreamChange(t.row)}},model:{value:t.row.streamIdentification,callback:function(i){e.$set(t.row,"streamIdentification",i)},expression:"scope.row.streamIdentification"}},[i("el-option",{attrs:{label:"stream:0(主码流)",value:"stream:0"}}),i("el-option",{attrs:{label:"stream:1(子码流)",value:"stream:1"}}),i("el-option",{attrs:{label:"streamnumber:0(主码流-2022)",value:"streamnumber:0"}}),i("el-option",{attrs:{label:"streamnumber:1(子码流-2022)",value:"streamnumber:1"}}),i("el-option",{attrs:{label:"streamprofile:0(主码流-大华)",value:"streamprofile:0"}}),i("el-option",{attrs:{label:"streamprofile:1(子码流-大华)",value:"streamprofile:1"}}),i("el-option",{attrs:{label:"streamMode:main(主码流-水星+TP-LINK)",value:"streamMode:main"}}),i("el-option",{attrs:{label:"streamMode:sub(子码流-水星+TP-LINK)",value:"streamMode:sub"}})],1)]}}],null,!1,2473089669)}),i("el-table-column",{attrs:{label:"状态","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},["ON"===t.row.status?i("el-tag",{attrs:{size:"medium"}},[e._v("在线")]):e._e(),"ON"!==t.row.status?i("el-tag",{attrs:{size:"medium",type:"info"}},[e._v("离线")]):e._e()],1)]}}],null,!1,2943336009)}),i("el-table-column",{attrs:{label:"操作","min-width":"340",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"medium",disabled:null==e.device||0===e.device.online,icon:"el-icon-video-play",type:"text",loading:t.row.playLoading},on:{click:function(i){return e.sendDevicePush(t.row)}}},[e._v("播放 ")]),t.row.streamId?i("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"medium",disabled:null==e.device||0===e.device.online,icon:"el-icon-switch-button",type:"text"},on:{click:function(i){return e.stopDevicePush(t.row)}}},[e._v("停止 ")]):e._e(),i("el-divider",{attrs:{direction:"vertical"}}),i("el-button",{attrs:{size:"medium",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleEdit(t.row)}}},[e._v(" 编辑 ")]),i("el-divider",{attrs:{direction:"vertical"}}),t.row.subCount>0||1===t.row.parental||t.row.deviceId.length<=8?i("el-button",{attrs:{size:"medium",icon:"el-icon-s-open",type:"text"},on:{click:function(i){return e.changeSubchannel(t.row)}}},[e._v("查看 ")]):e._e(),t.row.subCount>0||1===t.row.parental||t.row.deviceId.length<=8?i("el-divider",{attrs:{direction:"vertical"}}):e._e(),i("el-dropdown",{on:{command:function(i){e.moreClick(i,t.row)}}},[i("el-button",{attrs:{size:"medium",type:"text"}},[e._v(" 更多"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",[i("el-dropdown-item",{attrs:{command:"records",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像")]),i("el-dropdown-item",{attrs:{command:"cloudRecords",disabled:null==e.device||0===e.device.online}},[e._v(" 云端录像")]),i("el-dropdown-item",{attrs:{command:"record",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像控制-开始")]),i("el-dropdown-item",{attrs:{command:"stopRecord",disabled:null==e.device||0===e.device.online}},[e._v(" 设备录像控制-停止")])],1)],1)]}}],null,!1,289514641)})],1),i("el-pagination",{staticStyle:{"text-align":"right"},attrs:{"current-page":e.currentPage,"page-size":e.count,"page-sizes":[15,25,35,50],layout:"total, sizes, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.currentChange}})],1),i("devicePlayer",{ref:"devicePlayer"}),e.editId?i("channel-edit",{attrs:{id:e.editId,"close-edit":e.closeEdit}}):e._e()],1)},A=[],B=(i("b0c0"),i("0643"),i("4e3e"),i("159b"),i("0328")),R=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.locading,expression:"locading"}],staticStyle:{width:"100%"},attrs:{id:"ChannelEdit"}},[i("div",{staticClass:"page-header"},[i("div",{staticClass:"page-title"},[i("el-page-header",{attrs:{content:"编辑通道"},on:{back:e.close}})],1)]),i("CommonChannelEdit",{ref:"commonChannelEdit",attrs:{id:e.id,"save-success":e.close,cancel:e.close}})],1)},j=[],q=i("7317"),N={name:"ChannelEdit",components:{CommonChannelEdit:q["a"]},props:["id","closeEdit"],data:function(){return{}},methods:{close:function(){this.closeEdit()}}},O=N,V=Object(f["a"])(O,R,j,!1,null,null,null),F=V.exports,G={name:"ChannelList",components:{devicePlayer:B["a"],ChannelEdit:F},props:{defaultPage:{type:Number,default:1},defaultCount:{type:Number,default:15},deviceId:{type:String,default:null},parentChannelId:{type:String||null,default:null}},data:function(){return{device:null,deviceChannelList:[],videoComponentList:[],currentPlayerInfo:{},updateLooper:0,searchSrt:"",channelType:"",online:"",subStream:"",winHeight:window.innerHeight-200,currentPage:1|this.defaultPage,count:15|this.defaultCount,total:0,beforeUrl:"/device",editId:null,loadSnap:{},ptzTypes:{0:"未知",1:"球机",2:"半球",3:"固定枪机",4:"遥控枪机"}}},watch:{deviceId:function(e){var t=this;this.$store.dispatch("device/queryDeviceOne",this.deviceId).then((function(e){t.device=e})),this.initData()}},mounted:function(){var e=this;console.log(23222),this.deviceId&&this.$store.dispatch("device/queryDeviceOne",this.deviceId).then((function(t){e.device=t})),this.initData()},destroyed:function(){this.$destroy("videojs"),clearTimeout(this.updateLooper)},methods:{initData:function(){null===this.parentChannelId||"undefined"===typeof this.parentChannelId||0===this.parentChannelId?this.getDeviceChannelList():this.showSubChannels()},initParam:function(){this.deviceId=this.$route.params.deviceId,this.parentChannelId=this.$route.params.parentChannelId,this.currentPage=1,this.count=15,""!==this.parentChannelId&&0!==this.parentChannelId||(this.beforeUrl="/device/list")},currentChange:function(e){this.currentPage=e,this.initData()},handleSizeChange:function(e){this.count=e,this.getDeviceChannelList()},getDeviceChannelList:function(){var e=this;console.log(this.deviceId),"undefined"!==typeof this.deviceId&&this.$store.dispatch("device/queryChannels",[this.deviceId,{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType}]).then((function(t){e.total=t.total,e.deviceChannelList=t.list,e.deviceChannelList.forEach((function(t){t.ptzType=t.ptzType+"",e.$set(t,"playLoading",!1)})),e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))}))},sendDevicePush:function(e){var t=this,i=this.deviceId,n=e.deviceId;e.playLoading=!0,console.log("通知设备推流1："+i+" : "+n),this.$store.dispatch("play/play",[i,n]).then((function(a){setTimeout((function(){var e=i+"_"+n;t.loadSnap[i+n]=0,t.getSnapErrorEvent(e)}),5e3),e.streamId=a.stream,t.$refs.devicePlayer.openDialog("media",i,n,{streamInfo:a,hasAudio:e.hasAudio}),setTimeout((function(){t.initData()}),1e3)})).finally((function(){e.playLoading=!1}))},moreClick:function(e,t){"records"===e?this.queryRecords(t):"cloudRecords"===e?this.queryCloudRecords(t):"record"===e?this.startRecord(t):"stopRecord"===e&&this.stopRecord(t)},queryRecords:function(e){var t=this.deviceId,i=e.deviceId;this.$router.push("/device/record/".concat(t,"/").concat(i))},queryCloudRecords:function(e){var t=this.deviceId,i=e.deviceId;this.$router.push("/cloudRecord/detail/rtp/".concat(t,"_").concat(i))},startRecord:function(e){var t=this;this.$store.dispatch("device/deviceRecord",{deviceId:this.deviceId,channelId:e.deviceId,recordCmdStr:"Record"}).then((function(e){t.$message.success({showClose:!0,message:"开始录像成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},stopRecord:function(e){var t=this;this.$store.dispatch("device/deviceRecord",{deviceId:this.deviceId,channelId:e.deviceId,recordCmdStr:"StopRecord"}).then((function(e){t.$message.success({showClose:!0,message:"停止录像成功"})})).catch((function(e){t.$message.error({showClose:!0,message:e.message})}))},stopDevicePush:function(e){var t=this;this.$store.dispatch("play/stop",[this.deviceId,e.deviceId]).then((function(e){t.initData()})).catch((function(e){402===e.response.status?t.initData():console.log(e)}))},getSnap:function(e){var t=window.baseUrl?window.baseUrl:"";return t+"/api/device/query/snap/"+this.deviceId+"/"+e.deviceId},getBigSnap:function(e){return[this.getSnap(e)]},getSnapErrorEvent:function(e,t){var i=this;if("undefined"!==typeof this.loadSnap[e+t]){if(console.log("下载截图"+this.loadSnap[e+t]),this.loadSnap[e+t]>5)return void delete this.loadSnap[e+t];setTimeout((function(){var n="/api/device/query/snap/"+e+"/"+t;i.loadSnap[e+t]++,document.getElementById(e+t).setAttribute("src",n+"?"+(new Date).getTime())}),1e3)}},showDevice:function(){this.$emit("show-device")},changeSubchannel:function(e){var t=this;this.beforeUrl=this.$router.currentRoute.path;var i="/".concat(this.$router.currentRoute.name,"/").concat(this.$router.currentRoute.params.deviceId,"/").concat(e.deviceId);this.$router.push(i).then((function(){t.searchSrt="",t.channelType="",t.online="",t.initParam(),t.initData()}))},showSubChannels:function(){var e=this;this.$store.dispatch("device/querySubChannels",[{page:this.currentPage,count:this.count,query:this.searchSrt,online:this.online,channelType:this.channelType},this.deviceId,this.parentChannelId]).then((function(t){e.total=t.total,e.deviceChannelList=t.list,e.deviceChannelList.forEach((function(e){e.ptzType=e.ptzType+""})),e.$nextTick((function(){e.$refs.channelListTable.doLayout()}))}))},search:function(){this.currentPage=1,this.total=0,this.initData()},updateChannel:function(e){this.$store.dispatch("device/changeChannelAudio",{channelId:e.id,audio:e.hasAudio})},subStreamChange:function(){var e=this;this.$confirm("确定重置所有通道的码流类型?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$store.dispatch("device/updateChannelStreamIdentification",{deviceDbId:e.device.id,streamIdentification:e.subStream}).then((function(t){e.initData()})).finally((function(){e.subStream=""}))})).catch((function(){e.subStream=""}))},channelSubStreamChange:function(e){var t=this;this.$store.dispatch("device/updateChannelStreamIdentification",{deviceDbId:e.deviceDbId,id:e.id,streamIdentification:e.streamIdentification}).then((function(e){t.initData()})).finally((function(){t.subStream=""}))},refresh:function(){this.initData()},handleEdit:function(e){this.editId=e.id},closeEdit:function(){this.editId=null,this.getDeviceChannelList()}}},U=G,H=Object(f["a"])(U,M,A,!1,null,null,null),K=H.exports,J={name:"Device",components:{deviceList:z,channelList:K},data:function(){return{deviceId:null}},methods:{showChannelList:function(e){this.deviceId=e},showDevice:function(){this.deviceId=null}}},Q=J,W=Object(f["a"])(Q,n,a,!1,null,null,null);t["default"]=W.exports},a4be:function(e,t,i){}}]);